{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/*\n* A third-party license is embedded for some of the code in this file:\n* The \"scaleLevels\" was originally copied from \"d3.js\" with some\n* modifications made for this project.\n* (See more details in the comment on the definition of \"scaleLevels\" below.)\n* The use of the source code of this file is also subject to the terms\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\n* </licenses/LICENSE-d3>).\n*/\n// [About UTC and local time zone]:\n// In most cases, `number.parseDate` will treat input data string as local time\n// (except time zone is specified in time string). And `format.formateTime` returns\n// local time by default. option.useUTC is false by default. This design has\n// considered these common cases:\n// (1) Time that is persistent in server is in UTC, but it is needed to be displayed\n// in local time by default.\n// (2) By default, the input data string (e.g., '2011-01-02') should be displayed\n// as its original time, without any time difference.\nimport * as numberUtil from '../util/number.js';\nimport { ONE_SECOND, ONE_MINUTE, ONE_HOUR, ONE_DAY, ONE_YEAR, format, leveledFormat, timeUnits, fullLeveledFormatter, getPrimaryTimeUnit, isPrimaryTimeUnit, getDefaultFormatPrecisionOfInterval, fullYearGetterName, monthSetterName, fullYearSetterName, dateSetterName, hoursGetterName, hoursSetterName, minutesSetterName, secondsSetterName, millisecondsSetterName, monthGetterName, dateGetterName, minutesGetterName, secondsGetterName, millisecondsGetterName, getUnitFromValue, primaryTimeUnits, roundTime } from '../util/time.js';\nimport * as scaleHelper from './helper.js';\nimport IntervalScale from './Interval.js';\nimport Scale from './Scale.js';\nimport { warn } from '../util/log.js';\nimport { each, filter, indexOf, isNumber, map } from 'zrender/lib/core/util.js';\nimport { getScaleBreakHelper } from './break.js';\n// FIXME 公用？\nvar bisect = function (a, x, lo, hi) {\n  while (lo < hi) {\n    var mid = lo + hi >>> 1;\n    if (a[mid][1] < x) {\n      lo = mid + 1;\n    } else {\n      hi = mid;\n    }\n  }\n  return lo;\n};\nvar TimeScale = /** @class */function (_super) {\n  __extends(TimeScale, _super);\n  function TimeScale(settings) {\n    var _this = _super.call(this, settings) || this;\n    _this.type = 'time';\n    return _this;\n  }\n  /**\n   * Get label is mainly for other components like dataZoom, tooltip.\n   */\n  TimeScale.prototype.getLabel = function (tick) {\n    var useUTC = this.getSetting('useUTC');\n    return format(tick.value, fullLeveledFormatter[getDefaultFormatPrecisionOfInterval(getPrimaryTimeUnit(this._minLevelUnit))] || fullLeveledFormatter.second, useUTC, this.getSetting('locale'));\n  };\n  TimeScale.prototype.getFormattedLabel = function (tick, idx, labelFormatter) {\n    var isUTC = this.getSetting('useUTC');\n    var lang = this.getSetting('locale');\n    return leveledFormat(tick, idx, labelFormatter, lang, isUTC);\n  };\n  /**\n   * @override\n   */\n  TimeScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var interval = this._interval;\n    var extent = this._extent;\n    var scaleBreakHelper = getScaleBreakHelper();\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    var useUTC = this.getSetting('useUTC');\n    if (scaleBreakHelper && opt.breakTicks === 'only_break') {\n      getScaleBreakHelper().addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n      return ticks;\n    }\n    var extent0Unit = getUnitFromValue(extent[1], useUTC);\n    ticks.push({\n      value: extent[0],\n      time: {\n        level: 0,\n        upperTimeUnit: extent0Unit,\n        lowerTimeUnit: extent0Unit\n      }\n    });\n    var innerTicks = getIntervalTicks(this._minLevelUnit, this._approxInterval, useUTC, extent, this._getExtentSpanWithBreaks(), this._brkCtx);\n    ticks = ticks.concat(innerTicks);\n    var extent1Unit = getUnitFromValue(extent[1], useUTC);\n    ticks.push({\n      value: extent[1],\n      time: {\n        level: 0,\n        upperTimeUnit: extent1Unit,\n        lowerTimeUnit: extent1Unit\n      }\n    });\n    var isUTC = this.getSetting('useUTC');\n    var upperUnitIndex = primaryTimeUnits.length - 1;\n    var maxLevel = 0;\n    each(ticks, function (tick) {\n      upperUnitIndex = Math.min(upperUnitIndex, indexOf(primaryTimeUnits, tick.time.upperTimeUnit));\n      maxLevel = Math.max(maxLevel, tick.time.level);\n    });\n    if (scaleBreakHelper) {\n      getScaleBreakHelper().pruneTicksByBreak(opt.pruneByBreak, ticks, this._brkCtx.breaks, function (item) {\n        return item.value;\n      }, this._approxInterval, this._extent);\n    }\n    if (scaleBreakHelper && opt.breakTicks !== 'none') {\n      getScaleBreakHelper().addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent, function (trimmedBrk) {\n        // @see `parseTimeAxisLabelFormatterDictionary`.\n        var lowerBrkUnitIndex = Math.max(indexOf(primaryTimeUnits, getUnitFromValue(trimmedBrk.vmin, isUTC)), indexOf(primaryTimeUnits, getUnitFromValue(trimmedBrk.vmax, isUTC)));\n        var upperBrkUnitIndex = 0;\n        for (var unitIdx = 0; unitIdx < primaryTimeUnits.length; unitIdx++) {\n          if (!isPrimaryUnitValueAndGreaterSame(primaryTimeUnits[unitIdx], trimmedBrk.vmin, trimmedBrk.vmax, isUTC)) {\n            upperBrkUnitIndex = unitIdx;\n            break;\n          }\n        }\n        var upperIdx = Math.min(upperBrkUnitIndex, upperUnitIndex);\n        var lowerIdx = Math.max(upperIdx, lowerBrkUnitIndex);\n        return {\n          level: maxLevel,\n          lowerTimeUnit: primaryTimeUnits[lowerIdx],\n          upperTimeUnit: primaryTimeUnits[upperIdx]\n        };\n      });\n    }\n    return ticks;\n  };\n  TimeScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this.getExtent();\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      // Expand extent\n      extent[0] -= ONE_DAY;\n      extent[1] += ONE_DAY;\n    }\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (extent[1] === -Infinity && extent[0] === Infinity) {\n      var d = new Date();\n      extent[1] = +new Date(d.getFullYear(), d.getMonth(), d.getDate());\n      extent[0] = extent[1] - ONE_DAY;\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n  };\n  TimeScale.prototype.calcNiceTicks = function (approxTickNum, minInterval, maxInterval) {\n    approxTickNum = approxTickNum || 10;\n    var span = this._getExtentSpanWithBreaks();\n    this._approxInterval = span / approxTickNum;\n    if (minInterval != null && this._approxInterval < minInterval) {\n      this._approxInterval = minInterval;\n    }\n    if (maxInterval != null && this._approxInterval > maxInterval) {\n      this._approxInterval = maxInterval;\n    }\n    var scaleIntervalsLen = scaleIntervals.length;\n    var idx = Math.min(bisect(scaleIntervals, this._approxInterval, 0, scaleIntervalsLen), scaleIntervalsLen - 1);\n    // Interval that can be used to calculate ticks\n    this._interval = scaleIntervals[idx][1];\n    this._intervalPrecision = scaleHelper.getIntervalPrecision(this._interval);\n    // Min level used when picking ticks from top down.\n    // We check one more level to avoid the ticks are to sparse in some case.\n    this._minLevelUnit = scaleIntervals[Math.max(idx - 1, 0)][0];\n  };\n  TimeScale.prototype.parse = function (val) {\n    // val might be float.\n    return isNumber(val) ? val : +numberUtil.parseDate(val);\n  };\n  TimeScale.prototype.contain = function (val) {\n    return scaleHelper.contain(val, this._extent);\n  };\n  TimeScale.prototype.normalize = function (val) {\n    return this._calculator.normalize(val, this._extent);\n  };\n  TimeScale.prototype.scale = function (val) {\n    return this._calculator.scale(val, this._extent);\n  };\n  TimeScale.type = 'time';\n  return TimeScale;\n}(IntervalScale);\n/**\n * This implementation was originally copied from \"d3.js\"\n * <https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/time/scale.js>\n * with some modifications made for this program.\n * See the license statement at the head of this file.\n */\nvar scaleIntervals = [\n// Format                           interval\n['second', ONE_SECOND], ['minute', ONE_MINUTE], ['hour', ONE_HOUR], ['quarter-day', ONE_HOUR * 6], ['half-day', ONE_HOUR * 12], ['day', ONE_DAY * 1.2], ['half-week', ONE_DAY * 3.5], ['week', ONE_DAY * 7], ['month', ONE_DAY * 31], ['quarter', ONE_DAY * 95], ['half-year', ONE_YEAR / 2], ['year', ONE_YEAR] // 1Y\n];\nfunction isPrimaryUnitValueAndGreaterSame(unit, valueA, valueB, isUTC) {\n  return roundTime(new Date(valueA), unit, isUTC).getTime() === roundTime(new Date(valueB), unit, isUTC).getTime();\n}\n// function isUnitValueSame(\n//     unit: PrimaryTimeUnit,\n//     valueA: number,\n//     valueB: number,\n//     isUTC: boolean\n// ): boolean {\n//     const dateA = numberUtil.parseDate(valueA) as any;\n//     const dateB = numberUtil.parseDate(valueB) as any;\n//     const isSame = (unit: PrimaryTimeUnit) => {\n//         return getUnitValue(dateA, unit, isUTC)\n//             === getUnitValue(dateB, unit, isUTC);\n//     };\n//     const isSameYear = () => isSame('year');\n//     // const isSameHalfYear = () => isSameYear() && isSame('half-year');\n//     // const isSameQuater = () => isSameYear() && isSame('quarter');\n//     const isSameMonth = () => isSameYear() && isSame('month');\n//     const isSameDay = () => isSameMonth() && isSame('day');\n//     // const isSameHalfDay = () => isSameDay() && isSame('half-day');\n//     const isSameHour = () => isSameDay() && isSame('hour');\n//     const isSameMinute = () => isSameHour() && isSame('minute');\n//     const isSameSecond = () => isSameMinute() && isSame('second');\n//     const isSameMilliSecond = () => isSameSecond() && isSame('millisecond');\n//     switch (unit) {\n//         case 'year':\n//             return isSameYear();\n//         case 'month':\n//             return isSameMonth();\n//         case 'day':\n//             return isSameDay();\n//         case 'hour':\n//             return isSameHour();\n//         case 'minute':\n//             return isSameMinute();\n//         case 'second':\n//             return isSameSecond();\n//         case 'millisecond':\n//             return isSameMilliSecond();\n//     }\n// }\n// const primaryUnitGetters = {\n//     year: fullYearGetterName(),\n//     month: monthGetterName(),\n//     day: dateGetterName(),\n//     hour: hoursGetterName(),\n//     minute: minutesGetterName(),\n//     second: secondsGetterName(),\n//     millisecond: millisecondsGetterName()\n// };\n// const primaryUnitUTCGetters = {\n//     year: fullYearGetterName(true),\n//     month: monthGetterName(true),\n//     day: dateGetterName(true),\n//     hour: hoursGetterName(true),\n//     minute: minutesGetterName(true),\n//     second: secondsGetterName(true),\n//     millisecond: millisecondsGetterName(true)\n// };\n// function moveTick(date: Date, unitName: TimeUnit, step: number, isUTC: boolean) {\n//     step = step || 1;\n//     switch (getPrimaryTimeUnit(unitName)) {\n//         case 'year':\n//             date[fullYearSetterName(isUTC)](date[fullYearGetterName(isUTC)]() + step);\n//             break;\n//         case 'month':\n//             date[monthSetterName(isUTC)](date[monthGetterName(isUTC)]() + step);\n//             break;\n//         case 'day':\n//             date[dateSetterName(isUTC)](date[dateGetterName(isUTC)]() + step);\n//             break;\n//         case 'hour':\n//             date[hoursSetterName(isUTC)](date[hoursGetterName(isUTC)]() + step);\n//             break;\n//         case 'minute':\n//             date[minutesSetterName(isUTC)](date[minutesGetterName(isUTC)]() + step);\n//             break;\n//         case 'second':\n//             date[secondsSetterName(isUTC)](date[secondsGetterName(isUTC)]() + step);\n//             break;\n//         case 'millisecond':\n//             date[millisecondsSetterName(isUTC)](date[millisecondsGetterName(isUTC)]() + step);\n//             break;\n//     }\n//     return date.getTime();\n// }\n// const DATE_INTERVALS = [[8, 7.5], [4, 3.5], [2, 1.5]];\n// const MONTH_INTERVALS = [[6, 5.5], [3, 2.5], [2, 1.5]];\n// const MINUTES_SECONDS_INTERVALS = [[30, 30], [20, 20], [15, 15], [10, 10], [5, 5], [2, 2]];\nfunction getDateInterval(approxInterval, daysInMonth) {\n  approxInterval /= ONE_DAY;\n  return approxInterval > 16 ? 16\n  // Math.floor(daysInMonth / 2) + 1  // In this case we only want one tick between two months.\n  : approxInterval > 7.5 ? 7 // TODO week 7 or day 8?\n  : approxInterval > 3.5 ? 4 : approxInterval > 1.5 ? 2 : 1;\n}\nfunction getMonthInterval(approxInterval) {\n  var APPROX_ONE_MONTH = 30 * ONE_DAY;\n  approxInterval /= APPROX_ONE_MONTH;\n  return approxInterval > 6 ? 6 : approxInterval > 3 ? 3 : approxInterval > 2 ? 2 : 1;\n}\nfunction getHourInterval(approxInterval) {\n  approxInterval /= ONE_HOUR;\n  return approxInterval > 12 ? 12 : approxInterval > 6 ? 6 : approxInterval > 3.5 ? 4 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMinutesAndSecondsInterval(approxInterval, isMinutes) {\n  approxInterval /= isMinutes ? ONE_MINUTE : ONE_SECOND;\n  return approxInterval > 30 ? 30 : approxInterval > 20 ? 20 : approxInterval > 15 ? 15 : approxInterval > 10 ? 10 : approxInterval > 5 ? 5 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMillisecondsInterval(approxInterval) {\n  return numberUtil.nice(approxInterval, true);\n}\n// e.g., if the input unit is 'day', start calculate ticks from the first day of\n// that month to make ticks \"nice\".\nfunction getFirstTimestampOfUnit(timestamp, unitName, isUTC) {\n  var upperUnitIdx = Math.max(0, indexOf(primaryTimeUnits, unitName) - 1);\n  return roundTime(new Date(timestamp), primaryTimeUnits[upperUnitIdx], isUTC).getTime();\n}\nfunction createEstimateNiceMultiple(setMethodName, dateMethodInterval) {\n  var tmpDate = new Date(0);\n  tmpDate[setMethodName](1);\n  var tmpTime = tmpDate.getTime();\n  tmpDate[setMethodName](1 + dateMethodInterval);\n  var approxTimeInterval = tmpDate.getTime() - tmpTime;\n  return function (tickVal, targetValue) {\n    // Only in month that accurate result can not get by division of\n    // timestamp interval, but no need accurate here.\n    return Math.max(0, Math.round((targetValue - tickVal) / approxTimeInterval));\n  };\n}\nfunction getIntervalTicks(bottomUnitName, approxInterval, isUTC, extent, extentSpanWithBreaks, brkCtx) {\n  var safeLimit = 10000;\n  var unitNames = timeUnits;\n  var iter = 0;\n  function addTicksInSpan(interval, minTimestamp, maxTimestamp, getMethodName, setMethodName, isDate, out) {\n    var estimateNiceMultiple = createEstimateNiceMultiple(setMethodName, interval);\n    var dateTime = minTimestamp;\n    var date = new Date(dateTime);\n    // if (isDate) {\n    //     d -= 1; // Starts with 0;   PENDING\n    // }\n    while (dateTime < maxTimestamp && dateTime <= extent[1]) {\n      out.push({\n        value: dateTime\n      });\n      if (iter++ > safeLimit) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn('Exceed safe limit in time scale.');\n        }\n        break;\n      }\n      date[setMethodName](date[getMethodName]() + interval);\n      dateTime = date.getTime();\n      if (brkCtx) {\n        var moreMultiple = brkCtx.calcNiceTickMultiple(dateTime, estimateNiceMultiple);\n        if (moreMultiple > 0) {\n          date[setMethodName](date[getMethodName]() + moreMultiple * interval);\n          dateTime = date.getTime();\n        }\n      }\n    }\n    // This extra tick is for calcuating ticks of next level. Will not been added to the final result\n    out.push({\n      value: dateTime,\n      notAdd: true\n    });\n  }\n  function addLevelTicks(unitName, lastLevelTicks, levelTicks) {\n    var newAddedTicks = [];\n    var isFirstLevel = !lastLevelTicks.length;\n    if (isPrimaryUnitValueAndGreaterSame(getPrimaryTimeUnit(unitName), extent[0], extent[1], isUTC)) {\n      return;\n    }\n    if (isFirstLevel) {\n      lastLevelTicks = [{\n        value: getFirstTimestampOfUnit(extent[0], unitName, isUTC)\n      }, {\n        value: extent[1]\n      }];\n    }\n    for (var i = 0; i < lastLevelTicks.length - 1; i++) {\n      var startTick = lastLevelTicks[i].value;\n      var endTick = lastLevelTicks[i + 1].value;\n      if (startTick === endTick) {\n        continue;\n      }\n      var interval = void 0;\n      var getterName = void 0;\n      var setterName = void 0;\n      var isDate = false;\n      switch (unitName) {\n        case 'year':\n          interval = Math.max(1, Math.round(approxInterval / ONE_DAY / 365));\n          getterName = fullYearGetterName(isUTC);\n          setterName = fullYearSetterName(isUTC);\n          break;\n        case 'half-year':\n        case 'quarter':\n        case 'month':\n          interval = getMonthInterval(approxInterval);\n          getterName = monthGetterName(isUTC);\n          setterName = monthSetterName(isUTC);\n          break;\n        case 'week': // PENDING If week is added. Ignore day.\n        case 'half-week':\n        case 'day':\n          interval = getDateInterval(approxInterval, 31); // Use 32 days and let interval been 16\n          getterName = dateGetterName(isUTC);\n          setterName = dateSetterName(isUTC);\n          isDate = true;\n          break;\n        case 'half-day':\n        case 'quarter-day':\n        case 'hour':\n          interval = getHourInterval(approxInterval);\n          getterName = hoursGetterName(isUTC);\n          setterName = hoursSetterName(isUTC);\n          break;\n        case 'minute':\n          interval = getMinutesAndSecondsInterval(approxInterval, true);\n          getterName = minutesGetterName(isUTC);\n          setterName = minutesSetterName(isUTC);\n          break;\n        case 'second':\n          interval = getMinutesAndSecondsInterval(approxInterval, false);\n          getterName = secondsGetterName(isUTC);\n          setterName = secondsSetterName(isUTC);\n          break;\n        case 'millisecond':\n          interval = getMillisecondsInterval(approxInterval);\n          getterName = millisecondsGetterName(isUTC);\n          setterName = millisecondsSetterName(isUTC);\n          break;\n      }\n      // Notice: This expansion by `getFirstTimestampOfUnit` may cause too many ticks and\n      // iteration. e.g., when three levels of ticks is displayed, which can be caused by\n      // data zoom and axis breaks. Thus trim them here.\n      if (endTick >= extent[0] && startTick <= extent[1]) {\n        addTicksInSpan(interval, startTick, endTick, getterName, setterName, isDate, newAddedTicks);\n      }\n      if (unitName === 'year' && levelTicks.length > 1 && i === 0) {\n        // Add nearest years to the left extent.\n        levelTicks.unshift({\n          value: levelTicks[0].value - interval\n        });\n      }\n    }\n    for (var i = 0; i < newAddedTicks.length; i++) {\n      levelTicks.push(newAddedTicks[i]);\n    }\n  }\n  var levelsTicks = [];\n  var currentLevelTicks = [];\n  var tickCount = 0;\n  var lastLevelTickCount = 0;\n  for (var i = 0; i < unitNames.length; ++i) {\n    var primaryTimeUnit = getPrimaryTimeUnit(unitNames[i]);\n    if (!isPrimaryTimeUnit(unitNames[i])) {\n      // TODO\n      continue;\n    }\n    addLevelTicks(unitNames[i], levelsTicks[levelsTicks.length - 1] || [], currentLevelTicks);\n    var nextPrimaryTimeUnit = unitNames[i + 1] ? getPrimaryTimeUnit(unitNames[i + 1]) : null;\n    if (primaryTimeUnit !== nextPrimaryTimeUnit) {\n      if (currentLevelTicks.length) {\n        lastLevelTickCount = tickCount;\n        // Remove the duplicate so the tick count can be precisely.\n        currentLevelTicks.sort(function (a, b) {\n          return a.value - b.value;\n        });\n        var levelTicksRemoveDuplicated = [];\n        for (var i_1 = 0; i_1 < currentLevelTicks.length; ++i_1) {\n          var tickValue = currentLevelTicks[i_1].value;\n          if (i_1 === 0 || currentLevelTicks[i_1 - 1].value !== tickValue) {\n            levelTicksRemoveDuplicated.push(currentLevelTicks[i_1]);\n            if (tickValue >= extent[0] && tickValue <= extent[1]) {\n              tickCount++;\n            }\n          }\n        }\n        var targetTickNum = extentSpanWithBreaks / approxInterval;\n        // Added too much in this level and not too less in last level\n        if (tickCount > targetTickNum * 1.5 && lastLevelTickCount > targetTickNum / 1.5) {\n          break;\n        }\n        // Only treat primary time unit as one level.\n        levelsTicks.push(levelTicksRemoveDuplicated);\n        if (tickCount > targetTickNum || bottomUnitName === unitNames[i]) {\n          break;\n        }\n      }\n      // Reset if next unitName is primary\n      currentLevelTicks = [];\n    }\n  }\n  var levelsTicksInExtent = filter(map(levelsTicks, function (levelTicks) {\n    return filter(levelTicks, function (tick) {\n      return tick.value >= extent[0] && tick.value <= extent[1] && !tick.notAdd;\n    });\n  }), function (levelTicks) {\n    return levelTicks.length > 0;\n  });\n  var ticks = [];\n  var maxLevel = levelsTicksInExtent.length - 1;\n  for (var i = 0; i < levelsTicksInExtent.length; ++i) {\n    var levelTicks = levelsTicksInExtent[i];\n    for (var k = 0; k < levelTicks.length; ++k) {\n      var unit = getUnitFromValue(levelTicks[k].value, isUTC);\n      ticks.push({\n        value: levelTicks[k].value,\n        time: {\n          level: maxLevel - i,\n          upperTimeUnit: unit,\n          lowerTimeUnit: unit\n        }\n      });\n    }\n  }\n  ticks.sort(function (a, b) {\n    return a.value - b.value;\n  });\n  // Remove duplicates\n  var result = [];\n  for (var i = 0; i < ticks.length; ++i) {\n    if (i === 0 || ticks[i].value !== ticks[i - 1].value) {\n      result.push(ticks[i]);\n    }\n  }\n  return result;\n}\nScale.registerClass(TimeScale);\nexport default TimeScale;", "map": {"version": 3, "names": ["__extends", "numberUtil", "ONE_SECOND", "ONE_MINUTE", "ONE_HOUR", "ONE_DAY", "ONE_YEAR", "format", "leveledFormat", "timeUnits", "fullLeveledFormatter", "getPrimaryTimeUnit", "isPrimaryTimeUnit", "getDefaultFormatPrecisionOfInterval", "fullYearGetterName", "monthSetterName", "fullYearSetterName", "dateSetterName", "hoursGetterName", "hoursSetterName", "minutesSetterName", "seconds<PERSON><PERSON><PERSON><PERSON><PERSON>", "millisecondsSetterName", "monthGetterName", "dateGetterName", "minutesGetterName", "seconds<PERSON><PERSON><PERSON><PERSON><PERSON>", "millisecondsGetterName", "getUnitFromValue", "primaryTimeUnits", "roundTime", "scaleHelper", "IntervalScale", "Scale", "warn", "each", "filter", "indexOf", "isNumber", "map", "getScaleBreakHelper", "bisect", "a", "x", "lo", "hi", "mid", "TimeScale", "_super", "settings", "_this", "call", "type", "prototype", "get<PERSON><PERSON><PERSON>", "tick", "useUTC", "getSetting", "value", "_minLevelUnit", "second", "getFormattedLabel", "idx", "labelFormatter", "isUTC", "lang", "getTicks", "opt", "interval", "_interval", "extent", "_extent", "scaleBreakHelper", "ticks", "breakTicks", "addBreaksToTicks", "_brkCtx", "breaks", "extent0Unit", "push", "time", "level", "upperTimeUnit", "lowerTimeUnit", "innerTicks", "getIntervalTicks", "_approxInterval", "_getExtentSpanWithBreaks", "concat", "extent1Unit", "upperUnitIndex", "length", "maxLevel", "Math", "min", "max", "pruneTicksByBreak", "pruneByBreak", "item", "trimmedBrk", "lowerBrkUnitIndex", "vmin", "vmax", "upperBrkUnitIndex", "unitIdx", "isPrimaryUnitValueAndGreaterSame", "upperIdx", "lowerIdx", "calcNiceExtent", "getExtent", "Infinity", "d", "Date", "getFullYear", "getMonth", "getDate", "_innerSetExtent", "calcNiceTicks", "splitNumber", "minInterval", "maxInterval", "approxTickNum", "span", "scaleIntervalsLen", "scaleIntervals", "_intervalPrecision", "getIntervalPrecision", "parse", "val", "parseDate", "contain", "normalize", "_calculator", "scale", "unit", "valueA", "valueB", "getTime", "getDateInterval", "approxInterval", "daysInMonth", "getMonthInterval", "APPROX_ONE_MONTH", "getHourInterval", "getMinutesAndSecondsInterval", "isMinutes", "getMillisecondsInterval", "nice", "getFirstTimestampOfUnit", "timestamp", "unitName", "upperUnitIdx", "createEstimateNiceMultiple", "setMethodName", "dateMethodInterval", "tmpDate", "tmpTime", "approxTimeInterval", "tickVal", "targetValue", "round", "bottomUnitName", "extentSpanWithBreaks", "brkCtx", "safeLimit", "unitNames", "iter", "addTicksInSpan", "minTimestamp", "maxTimestamp", "getMethodName", "isDate", "out", "estimateNiceMultiple", "dateTime", "date", "process", "env", "NODE_ENV", "moreMultiple", "calcNiceTickMultiple", "notAdd", "addLevelTicks", "lastLevelTicks", "levelTicks", "newAddedTicks", "isFirstLevel", "i", "startTick", "endTick", "getterName", "setter<PERSON><PERSON>", "unshift", "levelsTicks", "currentLevelTicks", "tickCount", "lastLevelTickCount", "primaryTimeUnit", "nextPrimaryTimeUnit", "sort", "b", "levelTicksRemoveDuplicated", "i_1", "tickValue", "targetTickNum", "levelsTicksInExtent", "k", "result", "registerClass"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/scale/Time.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\n/*\n* A third-party license is embedded for some of the code in this file:\n* The \"scaleLevels\" was originally copied from \"d3.js\" with some\n* modifications made for this project.\n* (See more details in the comment on the definition of \"scaleLevels\" below.)\n* The use of the source code of this file is also subject to the terms\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\n* </licenses/LICENSE-d3>).\n*/\n// [About UTC and local time zone]:\n// In most cases, `number.parseDate` will treat input data string as local time\n// (except time zone is specified in time string). And `format.formateTime` returns\n// local time by default. option.useUTC is false by default. This design has\n// considered these common cases:\n// (1) Time that is persistent in server is in UTC, but it is needed to be displayed\n// in local time by default.\n// (2) By default, the input data string (e.g., '2011-01-02') should be displayed\n// as its original time, without any time difference.\nimport * as numberUtil from '../util/number.js';\nimport { ONE_SECOND, ONE_MINUTE, ONE_HOUR, ONE_DAY, ONE_YEAR, format, leveledFormat, timeUnits, fullLeveledFormatter, getPrimaryTimeUnit, isPrimaryTimeUnit, getDefaultFormatPrecisionOfInterval, fullYearGetterName, monthSetterName, fullYearSetterName, dateSetterName, hoursGetterName, hoursSetterName, minutesSetterName, secondsSetterName, millisecondsSetterName, monthGetterName, dateGetterName, minutesGetterName, secondsGetterName, millisecondsGetterName, getUnitFromValue, primaryTimeUnits, roundTime } from '../util/time.js';\nimport * as scaleHelper from './helper.js';\nimport IntervalScale from './Interval.js';\nimport Scale from './Scale.js';\nimport { warn } from '../util/log.js';\nimport { each, filter, indexOf, isNumber, map } from 'zrender/lib/core/util.js';\nimport { getScaleBreakHelper } from './break.js';\n// FIXME 公用？\nvar bisect = function (a, x, lo, hi) {\n  while (lo < hi) {\n    var mid = lo + hi >>> 1;\n    if (a[mid][1] < x) {\n      lo = mid + 1;\n    } else {\n      hi = mid;\n    }\n  }\n  return lo;\n};\nvar TimeScale = /** @class */function (_super) {\n  __extends(TimeScale, _super);\n  function TimeScale(settings) {\n    var _this = _super.call(this, settings) || this;\n    _this.type = 'time';\n    return _this;\n  }\n  /**\n   * Get label is mainly for other components like dataZoom, tooltip.\n   */\n  TimeScale.prototype.getLabel = function (tick) {\n    var useUTC = this.getSetting('useUTC');\n    return format(tick.value, fullLeveledFormatter[getDefaultFormatPrecisionOfInterval(getPrimaryTimeUnit(this._minLevelUnit))] || fullLeveledFormatter.second, useUTC, this.getSetting('locale'));\n  };\n  TimeScale.prototype.getFormattedLabel = function (tick, idx, labelFormatter) {\n    var isUTC = this.getSetting('useUTC');\n    var lang = this.getSetting('locale');\n    return leveledFormat(tick, idx, labelFormatter, lang, isUTC);\n  };\n  /**\n   * @override\n   */\n  TimeScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var interval = this._interval;\n    var extent = this._extent;\n    var scaleBreakHelper = getScaleBreakHelper();\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    var useUTC = this.getSetting('useUTC');\n    if (scaleBreakHelper && opt.breakTicks === 'only_break') {\n      getScaleBreakHelper().addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n      return ticks;\n    }\n    var extent0Unit = getUnitFromValue(extent[1], useUTC);\n    ticks.push({\n      value: extent[0],\n      time: {\n        level: 0,\n        upperTimeUnit: extent0Unit,\n        lowerTimeUnit: extent0Unit\n      }\n    });\n    var innerTicks = getIntervalTicks(this._minLevelUnit, this._approxInterval, useUTC, extent, this._getExtentSpanWithBreaks(), this._brkCtx);\n    ticks = ticks.concat(innerTicks);\n    var extent1Unit = getUnitFromValue(extent[1], useUTC);\n    ticks.push({\n      value: extent[1],\n      time: {\n        level: 0,\n        upperTimeUnit: extent1Unit,\n        lowerTimeUnit: extent1Unit\n      }\n    });\n    var isUTC = this.getSetting('useUTC');\n    var upperUnitIndex = primaryTimeUnits.length - 1;\n    var maxLevel = 0;\n    each(ticks, function (tick) {\n      upperUnitIndex = Math.min(upperUnitIndex, indexOf(primaryTimeUnits, tick.time.upperTimeUnit));\n      maxLevel = Math.max(maxLevel, tick.time.level);\n    });\n    if (scaleBreakHelper) {\n      getScaleBreakHelper().pruneTicksByBreak(opt.pruneByBreak, ticks, this._brkCtx.breaks, function (item) {\n        return item.value;\n      }, this._approxInterval, this._extent);\n    }\n    if (scaleBreakHelper && opt.breakTicks !== 'none') {\n      getScaleBreakHelper().addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent, function (trimmedBrk) {\n        // @see `parseTimeAxisLabelFormatterDictionary`.\n        var lowerBrkUnitIndex = Math.max(indexOf(primaryTimeUnits, getUnitFromValue(trimmedBrk.vmin, isUTC)), indexOf(primaryTimeUnits, getUnitFromValue(trimmedBrk.vmax, isUTC)));\n        var upperBrkUnitIndex = 0;\n        for (var unitIdx = 0; unitIdx < primaryTimeUnits.length; unitIdx++) {\n          if (!isPrimaryUnitValueAndGreaterSame(primaryTimeUnits[unitIdx], trimmedBrk.vmin, trimmedBrk.vmax, isUTC)) {\n            upperBrkUnitIndex = unitIdx;\n            break;\n          }\n        }\n        var upperIdx = Math.min(upperBrkUnitIndex, upperUnitIndex);\n        var lowerIdx = Math.max(upperIdx, lowerBrkUnitIndex);\n        return {\n          level: maxLevel,\n          lowerTimeUnit: primaryTimeUnits[lowerIdx],\n          upperTimeUnit: primaryTimeUnits[upperIdx]\n        };\n      });\n    }\n    return ticks;\n  };\n  TimeScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this.getExtent();\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      // Expand extent\n      extent[0] -= ONE_DAY;\n      extent[1] += ONE_DAY;\n    }\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (extent[1] === -Infinity && extent[0] === Infinity) {\n      var d = new Date();\n      extent[1] = +new Date(d.getFullYear(), d.getMonth(), d.getDate());\n      extent[0] = extent[1] - ONE_DAY;\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n  };\n  TimeScale.prototype.calcNiceTicks = function (approxTickNum, minInterval, maxInterval) {\n    approxTickNum = approxTickNum || 10;\n    var span = this._getExtentSpanWithBreaks();\n    this._approxInterval = span / approxTickNum;\n    if (minInterval != null && this._approxInterval < minInterval) {\n      this._approxInterval = minInterval;\n    }\n    if (maxInterval != null && this._approxInterval > maxInterval) {\n      this._approxInterval = maxInterval;\n    }\n    var scaleIntervalsLen = scaleIntervals.length;\n    var idx = Math.min(bisect(scaleIntervals, this._approxInterval, 0, scaleIntervalsLen), scaleIntervalsLen - 1);\n    // Interval that can be used to calculate ticks\n    this._interval = scaleIntervals[idx][1];\n    this._intervalPrecision = scaleHelper.getIntervalPrecision(this._interval);\n    // Min level used when picking ticks from top down.\n    // We check one more level to avoid the ticks are to sparse in some case.\n    this._minLevelUnit = scaleIntervals[Math.max(idx - 1, 0)][0];\n  };\n  TimeScale.prototype.parse = function (val) {\n    // val might be float.\n    return isNumber(val) ? val : +numberUtil.parseDate(val);\n  };\n  TimeScale.prototype.contain = function (val) {\n    return scaleHelper.contain(val, this._extent);\n  };\n  TimeScale.prototype.normalize = function (val) {\n    return this._calculator.normalize(val, this._extent);\n  };\n  TimeScale.prototype.scale = function (val) {\n    return this._calculator.scale(val, this._extent);\n  };\n  TimeScale.type = 'time';\n  return TimeScale;\n}(IntervalScale);\n/**\n * This implementation was originally copied from \"d3.js\"\n * <https://github.com/d3/d3/blob/b516d77fb8566b576088e73410437494717ada26/src/time/scale.js>\n * with some modifications made for this program.\n * See the license statement at the head of this file.\n */\nvar scaleIntervals = [\n// Format                           interval\n['second', ONE_SECOND], ['minute', ONE_MINUTE], ['hour', ONE_HOUR], ['quarter-day', ONE_HOUR * 6], ['half-day', ONE_HOUR * 12], ['day', ONE_DAY * 1.2], ['half-week', ONE_DAY * 3.5], ['week', ONE_DAY * 7], ['month', ONE_DAY * 31], ['quarter', ONE_DAY * 95], ['half-year', ONE_YEAR / 2], ['year', ONE_YEAR] // 1Y\n];\nfunction isPrimaryUnitValueAndGreaterSame(unit, valueA, valueB, isUTC) {\n  return roundTime(new Date(valueA), unit, isUTC).getTime() === roundTime(new Date(valueB), unit, isUTC).getTime();\n}\n// function isUnitValueSame(\n//     unit: PrimaryTimeUnit,\n//     valueA: number,\n//     valueB: number,\n//     isUTC: boolean\n// ): boolean {\n//     const dateA = numberUtil.parseDate(valueA) as any;\n//     const dateB = numberUtil.parseDate(valueB) as any;\n//     const isSame = (unit: PrimaryTimeUnit) => {\n//         return getUnitValue(dateA, unit, isUTC)\n//             === getUnitValue(dateB, unit, isUTC);\n//     };\n//     const isSameYear = () => isSame('year');\n//     // const isSameHalfYear = () => isSameYear() && isSame('half-year');\n//     // const isSameQuater = () => isSameYear() && isSame('quarter');\n//     const isSameMonth = () => isSameYear() && isSame('month');\n//     const isSameDay = () => isSameMonth() && isSame('day');\n//     // const isSameHalfDay = () => isSameDay() && isSame('half-day');\n//     const isSameHour = () => isSameDay() && isSame('hour');\n//     const isSameMinute = () => isSameHour() && isSame('minute');\n//     const isSameSecond = () => isSameMinute() && isSame('second');\n//     const isSameMilliSecond = () => isSameSecond() && isSame('millisecond');\n//     switch (unit) {\n//         case 'year':\n//             return isSameYear();\n//         case 'month':\n//             return isSameMonth();\n//         case 'day':\n//             return isSameDay();\n//         case 'hour':\n//             return isSameHour();\n//         case 'minute':\n//             return isSameMinute();\n//         case 'second':\n//             return isSameSecond();\n//         case 'millisecond':\n//             return isSameMilliSecond();\n//     }\n// }\n// const primaryUnitGetters = {\n//     year: fullYearGetterName(),\n//     month: monthGetterName(),\n//     day: dateGetterName(),\n//     hour: hoursGetterName(),\n//     minute: minutesGetterName(),\n//     second: secondsGetterName(),\n//     millisecond: millisecondsGetterName()\n// };\n// const primaryUnitUTCGetters = {\n//     year: fullYearGetterName(true),\n//     month: monthGetterName(true),\n//     day: dateGetterName(true),\n//     hour: hoursGetterName(true),\n//     minute: minutesGetterName(true),\n//     second: secondsGetterName(true),\n//     millisecond: millisecondsGetterName(true)\n// };\n// function moveTick(date: Date, unitName: TimeUnit, step: number, isUTC: boolean) {\n//     step = step || 1;\n//     switch (getPrimaryTimeUnit(unitName)) {\n//         case 'year':\n//             date[fullYearSetterName(isUTC)](date[fullYearGetterName(isUTC)]() + step);\n//             break;\n//         case 'month':\n//             date[monthSetterName(isUTC)](date[monthGetterName(isUTC)]() + step);\n//             break;\n//         case 'day':\n//             date[dateSetterName(isUTC)](date[dateGetterName(isUTC)]() + step);\n//             break;\n//         case 'hour':\n//             date[hoursSetterName(isUTC)](date[hoursGetterName(isUTC)]() + step);\n//             break;\n//         case 'minute':\n//             date[minutesSetterName(isUTC)](date[minutesGetterName(isUTC)]() + step);\n//             break;\n//         case 'second':\n//             date[secondsSetterName(isUTC)](date[secondsGetterName(isUTC)]() + step);\n//             break;\n//         case 'millisecond':\n//             date[millisecondsSetterName(isUTC)](date[millisecondsGetterName(isUTC)]() + step);\n//             break;\n//     }\n//     return date.getTime();\n// }\n// const DATE_INTERVALS = [[8, 7.5], [4, 3.5], [2, 1.5]];\n// const MONTH_INTERVALS = [[6, 5.5], [3, 2.5], [2, 1.5]];\n// const MINUTES_SECONDS_INTERVALS = [[30, 30], [20, 20], [15, 15], [10, 10], [5, 5], [2, 2]];\nfunction getDateInterval(approxInterval, daysInMonth) {\n  approxInterval /= ONE_DAY;\n  return approxInterval > 16 ? 16\n  // Math.floor(daysInMonth / 2) + 1  // In this case we only want one tick between two months.\n  : approxInterval > 7.5 ? 7 // TODO week 7 or day 8?\n  : approxInterval > 3.5 ? 4 : approxInterval > 1.5 ? 2 : 1;\n}\nfunction getMonthInterval(approxInterval) {\n  var APPROX_ONE_MONTH = 30 * ONE_DAY;\n  approxInterval /= APPROX_ONE_MONTH;\n  return approxInterval > 6 ? 6 : approxInterval > 3 ? 3 : approxInterval > 2 ? 2 : 1;\n}\nfunction getHourInterval(approxInterval) {\n  approxInterval /= ONE_HOUR;\n  return approxInterval > 12 ? 12 : approxInterval > 6 ? 6 : approxInterval > 3.5 ? 4 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMinutesAndSecondsInterval(approxInterval, isMinutes) {\n  approxInterval /= isMinutes ? ONE_MINUTE : ONE_SECOND;\n  return approxInterval > 30 ? 30 : approxInterval > 20 ? 20 : approxInterval > 15 ? 15 : approxInterval > 10 ? 10 : approxInterval > 5 ? 5 : approxInterval > 2 ? 2 : 1;\n}\nfunction getMillisecondsInterval(approxInterval) {\n  return numberUtil.nice(approxInterval, true);\n}\n// e.g., if the input unit is 'day', start calculate ticks from the first day of\n// that month to make ticks \"nice\".\nfunction getFirstTimestampOfUnit(timestamp, unitName, isUTC) {\n  var upperUnitIdx = Math.max(0, indexOf(primaryTimeUnits, unitName) - 1);\n  return roundTime(new Date(timestamp), primaryTimeUnits[upperUnitIdx], isUTC).getTime();\n}\nfunction createEstimateNiceMultiple(setMethodName, dateMethodInterval) {\n  var tmpDate = new Date(0);\n  tmpDate[setMethodName](1);\n  var tmpTime = tmpDate.getTime();\n  tmpDate[setMethodName](1 + dateMethodInterval);\n  var approxTimeInterval = tmpDate.getTime() - tmpTime;\n  return function (tickVal, targetValue) {\n    // Only in month that accurate result can not get by division of\n    // timestamp interval, but no need accurate here.\n    return Math.max(0, Math.round((targetValue - tickVal) / approxTimeInterval));\n  };\n}\nfunction getIntervalTicks(bottomUnitName, approxInterval, isUTC, extent, extentSpanWithBreaks, brkCtx) {\n  var safeLimit = 10000;\n  var unitNames = timeUnits;\n  var iter = 0;\n  function addTicksInSpan(interval, minTimestamp, maxTimestamp, getMethodName, setMethodName, isDate, out) {\n    var estimateNiceMultiple = createEstimateNiceMultiple(setMethodName, interval);\n    var dateTime = minTimestamp;\n    var date = new Date(dateTime);\n    // if (isDate) {\n    //     d -= 1; // Starts with 0;   PENDING\n    // }\n    while (dateTime < maxTimestamp && dateTime <= extent[1]) {\n      out.push({\n        value: dateTime\n      });\n      if (iter++ > safeLimit) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn('Exceed safe limit in time scale.');\n        }\n        break;\n      }\n      date[setMethodName](date[getMethodName]() + interval);\n      dateTime = date.getTime();\n      if (brkCtx) {\n        var moreMultiple = brkCtx.calcNiceTickMultiple(dateTime, estimateNiceMultiple);\n        if (moreMultiple > 0) {\n          date[setMethodName](date[getMethodName]() + moreMultiple * interval);\n          dateTime = date.getTime();\n        }\n      }\n    }\n    // This extra tick is for calcuating ticks of next level. Will not been added to the final result\n    out.push({\n      value: dateTime,\n      notAdd: true\n    });\n  }\n  function addLevelTicks(unitName, lastLevelTicks, levelTicks) {\n    var newAddedTicks = [];\n    var isFirstLevel = !lastLevelTicks.length;\n    if (isPrimaryUnitValueAndGreaterSame(getPrimaryTimeUnit(unitName), extent[0], extent[1], isUTC)) {\n      return;\n    }\n    if (isFirstLevel) {\n      lastLevelTicks = [{\n        value: getFirstTimestampOfUnit(extent[0], unitName, isUTC)\n      }, {\n        value: extent[1]\n      }];\n    }\n    for (var i = 0; i < lastLevelTicks.length - 1; i++) {\n      var startTick = lastLevelTicks[i].value;\n      var endTick = lastLevelTicks[i + 1].value;\n      if (startTick === endTick) {\n        continue;\n      }\n      var interval = void 0;\n      var getterName = void 0;\n      var setterName = void 0;\n      var isDate = false;\n      switch (unitName) {\n        case 'year':\n          interval = Math.max(1, Math.round(approxInterval / ONE_DAY / 365));\n          getterName = fullYearGetterName(isUTC);\n          setterName = fullYearSetterName(isUTC);\n          break;\n        case 'half-year':\n        case 'quarter':\n        case 'month':\n          interval = getMonthInterval(approxInterval);\n          getterName = monthGetterName(isUTC);\n          setterName = monthSetterName(isUTC);\n          break;\n        case 'week': // PENDING If week is added. Ignore day.\n        case 'half-week':\n        case 'day':\n          interval = getDateInterval(approxInterval, 31); // Use 32 days and let interval been 16\n          getterName = dateGetterName(isUTC);\n          setterName = dateSetterName(isUTC);\n          isDate = true;\n          break;\n        case 'half-day':\n        case 'quarter-day':\n        case 'hour':\n          interval = getHourInterval(approxInterval);\n          getterName = hoursGetterName(isUTC);\n          setterName = hoursSetterName(isUTC);\n          break;\n        case 'minute':\n          interval = getMinutesAndSecondsInterval(approxInterval, true);\n          getterName = minutesGetterName(isUTC);\n          setterName = minutesSetterName(isUTC);\n          break;\n        case 'second':\n          interval = getMinutesAndSecondsInterval(approxInterval, false);\n          getterName = secondsGetterName(isUTC);\n          setterName = secondsSetterName(isUTC);\n          break;\n        case 'millisecond':\n          interval = getMillisecondsInterval(approxInterval);\n          getterName = millisecondsGetterName(isUTC);\n          setterName = millisecondsSetterName(isUTC);\n          break;\n      }\n      // Notice: This expansion by `getFirstTimestampOfUnit` may cause too many ticks and\n      // iteration. e.g., when three levels of ticks is displayed, which can be caused by\n      // data zoom and axis breaks. Thus trim them here.\n      if (endTick >= extent[0] && startTick <= extent[1]) {\n        addTicksInSpan(interval, startTick, endTick, getterName, setterName, isDate, newAddedTicks);\n      }\n      if (unitName === 'year' && levelTicks.length > 1 && i === 0) {\n        // Add nearest years to the left extent.\n        levelTicks.unshift({\n          value: levelTicks[0].value - interval\n        });\n      }\n    }\n    for (var i = 0; i < newAddedTicks.length; i++) {\n      levelTicks.push(newAddedTicks[i]);\n    }\n  }\n  var levelsTicks = [];\n  var currentLevelTicks = [];\n  var tickCount = 0;\n  var lastLevelTickCount = 0;\n  for (var i = 0; i < unitNames.length; ++i) {\n    var primaryTimeUnit = getPrimaryTimeUnit(unitNames[i]);\n    if (!isPrimaryTimeUnit(unitNames[i])) {\n      // TODO\n      continue;\n    }\n    addLevelTicks(unitNames[i], levelsTicks[levelsTicks.length - 1] || [], currentLevelTicks);\n    var nextPrimaryTimeUnit = unitNames[i + 1] ? getPrimaryTimeUnit(unitNames[i + 1]) : null;\n    if (primaryTimeUnit !== nextPrimaryTimeUnit) {\n      if (currentLevelTicks.length) {\n        lastLevelTickCount = tickCount;\n        // Remove the duplicate so the tick count can be precisely.\n        currentLevelTicks.sort(function (a, b) {\n          return a.value - b.value;\n        });\n        var levelTicksRemoveDuplicated = [];\n        for (var i_1 = 0; i_1 < currentLevelTicks.length; ++i_1) {\n          var tickValue = currentLevelTicks[i_1].value;\n          if (i_1 === 0 || currentLevelTicks[i_1 - 1].value !== tickValue) {\n            levelTicksRemoveDuplicated.push(currentLevelTicks[i_1]);\n            if (tickValue >= extent[0] && tickValue <= extent[1]) {\n              tickCount++;\n            }\n          }\n        }\n        var targetTickNum = extentSpanWithBreaks / approxInterval;\n        // Added too much in this level and not too less in last level\n        if (tickCount > targetTickNum * 1.5 && lastLevelTickCount > targetTickNum / 1.5) {\n          break;\n        }\n        // Only treat primary time unit as one level.\n        levelsTicks.push(levelTicksRemoveDuplicated);\n        if (tickCount > targetTickNum || bottomUnitName === unitNames[i]) {\n          break;\n        }\n      }\n      // Reset if next unitName is primary\n      currentLevelTicks = [];\n    }\n  }\n  var levelsTicksInExtent = filter(map(levelsTicks, function (levelTicks) {\n    return filter(levelTicks, function (tick) {\n      return tick.value >= extent[0] && tick.value <= extent[1] && !tick.notAdd;\n    });\n  }), function (levelTicks) {\n    return levelTicks.length > 0;\n  });\n  var ticks = [];\n  var maxLevel = levelsTicksInExtent.length - 1;\n  for (var i = 0; i < levelsTicksInExtent.length; ++i) {\n    var levelTicks = levelsTicksInExtent[i];\n    for (var k = 0; k < levelTicks.length; ++k) {\n      var unit = getUnitFromValue(levelTicks[k].value, isUTC);\n      ticks.push({\n        value: levelTicks[k].value,\n        time: {\n          level: maxLevel - i,\n          upperTimeUnit: unit,\n          lowerTimeUnit: unit\n        }\n      });\n    }\n  }\n  ticks.sort(function (a, b) {\n    return a.value - b.value;\n  });\n  // Remove duplicates\n  var result = [];\n  for (var i = 0; i < ticks.length; ++i) {\n    if (i === 0 || ticks[i].value !== ticks[i - 1].value) {\n      result.push(ticks[i]);\n    }\n  }\n  return result;\n}\nScale.registerClass(TimeScale);\nexport default TimeScale;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,aAAa,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,mCAAmC,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,SAAS,QAAQ,iBAAiB;AAChhB,OAAO,KAAKC,WAAW,MAAM,aAAa;AAC1C,OAAOC,aAAa,MAAM,eAAe;AACzC,OAAOC,KAAK,MAAM,YAAY;AAC9B,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,0BAA0B;AAC/E,SAASC,mBAAmB,QAAQ,YAAY;AAChD;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACnC,OAAOD,EAAE,GAAGC,EAAE,EAAE;IACd,IAAIC,GAAG,GAAGF,EAAE,GAAGC,EAAE,KAAK,CAAC;IACvB,IAAIH,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,EAAE;MACjBC,EAAE,GAAGE,GAAG,GAAG,CAAC;IACd,CAAC,MAAM;MACLD,EAAE,GAAGC,GAAG;IACV;EACF;EACA,OAAOF,EAAE;AACX,CAAC;AACD,IAAIG,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7ChD,SAAS,CAAC+C,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAACE,QAAQ,EAAE;IAC3B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,QAAQ,CAAC,IAAI,IAAI;IAC/CC,KAAK,CAACE,IAAI,GAAG,MAAM;IACnB,OAAOF,KAAK;EACd;EACA;AACF;AACA;EACEH,SAAS,CAACM,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAIC,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;IACtC,OAAOlD,MAAM,CAACgD,IAAI,CAACG,KAAK,EAAEhD,oBAAoB,CAACG,mCAAmC,CAACF,kBAAkB,CAAC,IAAI,CAACgD,aAAa,CAAC,CAAC,CAAC,IAAIjD,oBAAoB,CAACkD,MAAM,EAAEJ,MAAM,EAAE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;EAChM,CAAC;EACDV,SAAS,CAACM,SAAS,CAACQ,iBAAiB,GAAG,UAAUN,IAAI,EAAEO,GAAG,EAAEC,cAAc,EAAE;IAC3E,IAAIC,KAAK,GAAG,IAAI,CAACP,UAAU,CAAC,QAAQ,CAAC;IACrC,IAAIQ,IAAI,GAAG,IAAI,CAACR,UAAU,CAAC,QAAQ,CAAC;IACpC,OAAOjD,aAAa,CAAC+C,IAAI,EAAEO,GAAG,EAAEC,cAAc,EAAEE,IAAI,EAAED,KAAK,CAAC;EAC9D,CAAC;EACD;AACF;AACA;EACEjB,SAAS,CAACM,SAAS,CAACa,QAAQ,GAAG,UAAUC,GAAG,EAAE;IAC5CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC7B,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIC,gBAAgB,GAAGhC,mBAAmB,CAAC,CAAC;IAC5C,IAAIiC,KAAK,GAAG,EAAE;IACd;IACA,IAAI,CAACL,QAAQ,EAAE;MACb,OAAOK,KAAK;IACd;IACA,IAAIjB,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;IACtC,IAAIe,gBAAgB,IAAIL,GAAG,CAACO,UAAU,KAAK,YAAY,EAAE;MACvDlC,mBAAmB,CAAC,CAAC,CAACmC,gBAAgB,CAACF,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,IAAI,CAACN,OAAO,CAAC;MAChF,OAAOE,KAAK;IACd;IACA,IAAIK,WAAW,GAAGlD,gBAAgB,CAAC0C,MAAM,CAAC,CAAC,CAAC,EAAEd,MAAM,CAAC;IACrDiB,KAAK,CAACM,IAAI,CAAC;MACTrB,KAAK,EAAEY,MAAM,CAAC,CAAC,CAAC;MAChBU,IAAI,EAAE;QACJC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAEJ,WAAW;QAC1BK,aAAa,EAAEL;MACjB;IACF,CAAC,CAAC;IACF,IAAIM,UAAU,GAAGC,gBAAgB,CAAC,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC2B,eAAe,EAAE9B,MAAM,EAAEc,MAAM,EAAE,IAAI,CAACiB,wBAAwB,CAAC,CAAC,EAAE,IAAI,CAACX,OAAO,CAAC;IAC1IH,KAAK,GAAGA,KAAK,CAACe,MAAM,CAACJ,UAAU,CAAC;IAChC,IAAIK,WAAW,GAAG7D,gBAAgB,CAAC0C,MAAM,CAAC,CAAC,CAAC,EAAEd,MAAM,CAAC;IACrDiB,KAAK,CAACM,IAAI,CAAC;MACTrB,KAAK,EAAEY,MAAM,CAAC,CAAC,CAAC;MAChBU,IAAI,EAAE;QACJC,KAAK,EAAE,CAAC;QACRC,aAAa,EAAEO,WAAW;QAC1BN,aAAa,EAAEM;MACjB;IACF,CAAC,CAAC;IACF,IAAIzB,KAAK,GAAG,IAAI,CAACP,UAAU,CAAC,QAAQ,CAAC;IACrC,IAAIiC,cAAc,GAAG7D,gBAAgB,CAAC8D,MAAM,GAAG,CAAC;IAChD,IAAIC,QAAQ,GAAG,CAAC;IAChBzD,IAAI,CAACsC,KAAK,EAAE,UAAUlB,IAAI,EAAE;MAC1BmC,cAAc,GAAGG,IAAI,CAACC,GAAG,CAACJ,cAAc,EAAErD,OAAO,CAACR,gBAAgB,EAAE0B,IAAI,CAACyB,IAAI,CAACE,aAAa,CAAC,CAAC;MAC7FU,QAAQ,GAAGC,IAAI,CAACE,GAAG,CAACH,QAAQ,EAAErC,IAAI,CAACyB,IAAI,CAACC,KAAK,CAAC;IAChD,CAAC,CAAC;IACF,IAAIT,gBAAgB,EAAE;MACpBhC,mBAAmB,CAAC,CAAC,CAACwD,iBAAiB,CAAC7B,GAAG,CAAC8B,YAAY,EAAExB,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,UAAUqB,IAAI,EAAE;QACpG,OAAOA,IAAI,CAACxC,KAAK;MACnB,CAAC,EAAE,IAAI,CAAC4B,eAAe,EAAE,IAAI,CAACf,OAAO,CAAC;IACxC;IACA,IAAIC,gBAAgB,IAAIL,GAAG,CAACO,UAAU,KAAK,MAAM,EAAE;MACjDlC,mBAAmB,CAAC,CAAC,CAACmC,gBAAgB,CAACF,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,IAAI,CAACN,OAAO,EAAE,UAAU4B,UAAU,EAAE;QACrG;QACA,IAAIC,iBAAiB,GAAGP,IAAI,CAACE,GAAG,CAAC1D,OAAO,CAACR,gBAAgB,EAAED,gBAAgB,CAACuE,UAAU,CAACE,IAAI,EAAErC,KAAK,CAAC,CAAC,EAAE3B,OAAO,CAACR,gBAAgB,EAAED,gBAAgB,CAACuE,UAAU,CAACG,IAAI,EAAEtC,KAAK,CAAC,CAAC,CAAC;QAC1K,IAAIuC,iBAAiB,GAAG,CAAC;QACzB,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG3E,gBAAgB,CAAC8D,MAAM,EAAEa,OAAO,EAAE,EAAE;UAClE,IAAI,CAACC,gCAAgC,CAAC5E,gBAAgB,CAAC2E,OAAO,CAAC,EAAEL,UAAU,CAACE,IAAI,EAAEF,UAAU,CAACG,IAAI,EAAEtC,KAAK,CAAC,EAAE;YACzGuC,iBAAiB,GAAGC,OAAO;YAC3B;UACF;QACF;QACA,IAAIE,QAAQ,GAAGb,IAAI,CAACC,GAAG,CAACS,iBAAiB,EAAEb,cAAc,CAAC;QAC1D,IAAIiB,QAAQ,GAAGd,IAAI,CAACE,GAAG,CAACW,QAAQ,EAAEN,iBAAiB,CAAC;QACpD,OAAO;UACLnB,KAAK,EAAEW,QAAQ;UACfT,aAAa,EAAEtD,gBAAgB,CAAC8E,QAAQ,CAAC;UACzCzB,aAAa,EAAErD,gBAAgB,CAAC6E,QAAQ;QAC1C,CAAC;MACH,CAAC,CAAC;IACJ;IACA,OAAOjC,KAAK;EACd,CAAC;EACD1B,SAAS,CAACM,SAAS,CAACuD,cAAc,GAAG,UAAUzC,GAAG,EAAE;IAClD,IAAIG,MAAM,GAAG,IAAI,CAACuC,SAAS,CAAC,CAAC;IAC7B;IACA,IAAIvC,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;MAC3B;MACAA,MAAM,CAAC,CAAC,CAAC,IAAIjE,OAAO;MACpBiE,MAAM,CAAC,CAAC,CAAC,IAAIjE,OAAO;IACtB;IACA;IACA,IAAIiE,MAAM,CAAC,CAAC,CAAC,KAAK,CAACwC,QAAQ,IAAIxC,MAAM,CAAC,CAAC,CAAC,KAAKwC,QAAQ,EAAE;MACrD,IAAIC,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAC;MAClB1C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI0C,IAAI,CAACD,CAAC,CAACE,WAAW,CAAC,CAAC,EAAEF,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAEH,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC;MACjE7C,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGjE,OAAO;IACjC;IACA,IAAI,CAAC+G,eAAe,CAAC9C,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC+C,aAAa,CAAClD,GAAG,CAACmD,WAAW,EAAEnD,GAAG,CAACoD,WAAW,EAAEpD,GAAG,CAACqD,WAAW,CAAC;EACvE,CAAC;EACDzE,SAAS,CAACM,SAAS,CAACgE,aAAa,GAAG,UAAUI,aAAa,EAAEF,WAAW,EAAEC,WAAW,EAAE;IACrFC,aAAa,GAAGA,aAAa,IAAI,EAAE;IACnC,IAAIC,IAAI,GAAG,IAAI,CAACnC,wBAAwB,CAAC,CAAC;IAC1C,IAAI,CAACD,eAAe,GAAGoC,IAAI,GAAGD,aAAa;IAC3C,IAAIF,WAAW,IAAI,IAAI,IAAI,IAAI,CAACjC,eAAe,GAAGiC,WAAW,EAAE;MAC7D,IAAI,CAACjC,eAAe,GAAGiC,WAAW;IACpC;IACA,IAAIC,WAAW,IAAI,IAAI,IAAI,IAAI,CAAClC,eAAe,GAAGkC,WAAW,EAAE;MAC7D,IAAI,CAAClC,eAAe,GAAGkC,WAAW;IACpC;IACA,IAAIG,iBAAiB,GAAGC,cAAc,CAACjC,MAAM;IAC7C,IAAI7B,GAAG,GAAG+B,IAAI,CAACC,GAAG,CAACrD,MAAM,CAACmF,cAAc,EAAE,IAAI,CAACtC,eAAe,EAAE,CAAC,EAAEqC,iBAAiB,CAAC,EAAEA,iBAAiB,GAAG,CAAC,CAAC;IAC7G;IACA,IAAI,CAACtD,SAAS,GAAGuD,cAAc,CAAC9D,GAAG,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC+D,kBAAkB,GAAG9F,WAAW,CAAC+F,oBAAoB,CAAC,IAAI,CAACzD,SAAS,CAAC;IAC1E;IACA;IACA,IAAI,CAACV,aAAa,GAAGiE,cAAc,CAAC/B,IAAI,CAACE,GAAG,CAACjC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC;EACDf,SAAS,CAACM,SAAS,CAAC0E,KAAK,GAAG,UAAUC,GAAG,EAAE;IACzC;IACA,OAAO1F,QAAQ,CAAC0F,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC/H,UAAU,CAACgI,SAAS,CAACD,GAAG,CAAC;EACzD,CAAC;EACDjF,SAAS,CAACM,SAAS,CAAC6E,OAAO,GAAG,UAAUF,GAAG,EAAE;IAC3C,OAAOjG,WAAW,CAACmG,OAAO,CAACF,GAAG,EAAE,IAAI,CAACzD,OAAO,CAAC;EAC/C,CAAC;EACDxB,SAAS,CAACM,SAAS,CAAC8E,SAAS,GAAG,UAAUH,GAAG,EAAE;IAC7C,OAAO,IAAI,CAACI,WAAW,CAACD,SAAS,CAACH,GAAG,EAAE,IAAI,CAACzD,OAAO,CAAC;EACtD,CAAC;EACDxB,SAAS,CAACM,SAAS,CAACgF,KAAK,GAAG,UAAUL,GAAG,EAAE;IACzC,OAAO,IAAI,CAACI,WAAW,CAACC,KAAK,CAACL,GAAG,EAAE,IAAI,CAACzD,OAAO,CAAC;EAClD,CAAC;EACDxB,SAAS,CAACK,IAAI,GAAG,MAAM;EACvB,OAAOL,SAAS;AAClB,CAAC,CAACf,aAAa,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI4F,cAAc,GAAG;AACrB;AACA,CAAC,QAAQ,EAAE1H,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAEC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAEC,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,UAAU,EAAEA,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,EAAEC,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,WAAW,EAAEA,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC,MAAM,EAAEA,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,EAAEA,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,SAAS,EAAEA,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,WAAW,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,EAAEA,QAAQ,CAAC,CAAC;AAAA,CAChT;AACD,SAASmG,gCAAgCA,CAAC6B,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAExE,KAAK,EAAE;EACrE,OAAOlC,SAAS,CAAC,IAAIkF,IAAI,CAACuB,MAAM,CAAC,EAAED,IAAI,EAAEtE,KAAK,CAAC,CAACyE,OAAO,CAAC,CAAC,KAAK3G,SAAS,CAAC,IAAIkF,IAAI,CAACwB,MAAM,CAAC,EAAEF,IAAI,EAAEtE,KAAK,CAAC,CAACyE,OAAO,CAAC,CAAC;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,cAAc,EAAEC,WAAW,EAAE;EACpDD,cAAc,IAAItI,OAAO;EACzB,OAAOsI,cAAc,GAAG,EAAE,GAAG;EAC7B;EAAA,EACEA,cAAc,GAAG,GAAG,GAAG,CAAC,CAAC;EAAA,EACzBA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAGA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AAC3D;AACA,SAASE,gBAAgBA,CAACF,cAAc,EAAE;EACxC,IAAIG,gBAAgB,GAAG,EAAE,GAAGzI,OAAO;EACnCsI,cAAc,IAAIG,gBAAgB;EAClC,OAAOH,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACrF;AACA,SAASI,eAAeA,CAACJ,cAAc,EAAE;EACvCA,cAAc,IAAIvI,QAAQ;EAC1B,OAAOuI,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,GAAG,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAClH;AACA,SAASK,4BAA4BA,CAACL,cAAc,EAAEM,SAAS,EAAE;EAC/DN,cAAc,IAAIM,SAAS,GAAG9I,UAAU,GAAGD,UAAU;EACrD,OAAOyI,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,EAAE,GAAG,EAAE,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACxK;AACA,SAASO,uBAAuBA,CAACP,cAAc,EAAE;EAC/C,OAAO1I,UAAU,CAACkJ,IAAI,CAACR,cAAc,EAAE,IAAI,CAAC;AAC9C;AACA;AACA;AACA,SAASS,uBAAuBA,CAACC,SAAS,EAAEC,QAAQ,EAAEtF,KAAK,EAAE;EAC3D,IAAIuF,YAAY,GAAG1D,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE1D,OAAO,CAACR,gBAAgB,EAAEyH,QAAQ,CAAC,GAAG,CAAC,CAAC;EACvE,OAAOxH,SAAS,CAAC,IAAIkF,IAAI,CAACqC,SAAS,CAAC,EAAExH,gBAAgB,CAAC0H,YAAY,CAAC,EAAEvF,KAAK,CAAC,CAACyE,OAAO,CAAC,CAAC;AACxF;AACA,SAASe,0BAA0BA,CAACC,aAAa,EAAEC,kBAAkB,EAAE;EACrE,IAAIC,OAAO,GAAG,IAAI3C,IAAI,CAAC,CAAC,CAAC;EACzB2C,OAAO,CAACF,aAAa,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIG,OAAO,GAAGD,OAAO,CAAClB,OAAO,CAAC,CAAC;EAC/BkB,OAAO,CAACF,aAAa,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAAC;EAC9C,IAAIG,kBAAkB,GAAGF,OAAO,CAAClB,OAAO,CAAC,CAAC,GAAGmB,OAAO;EACpD,OAAO,UAAUE,OAAO,EAAEC,WAAW,EAAE;IACrC;IACA;IACA,OAAOlE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACmE,KAAK,CAAC,CAACD,WAAW,GAAGD,OAAO,IAAID,kBAAkB,CAAC,CAAC;EAC9E,CAAC;AACH;AACA,SAASxE,gBAAgBA,CAAC4E,cAAc,EAAEtB,cAAc,EAAE3E,KAAK,EAAEM,MAAM,EAAE4F,oBAAoB,EAAEC,MAAM,EAAE;EACrG,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,SAAS,GAAG5J,SAAS;EACzB,IAAI6J,IAAI,GAAG,CAAC;EACZ,SAASC,cAAcA,CAACnG,QAAQ,EAAEoG,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEjB,aAAa,EAAEkB,MAAM,EAAEC,GAAG,EAAE;IACvG,IAAIC,oBAAoB,GAAGrB,0BAA0B,CAACC,aAAa,EAAErF,QAAQ,CAAC;IAC9E,IAAI0G,QAAQ,GAAGN,YAAY;IAC3B,IAAIO,IAAI,GAAG,IAAI/D,IAAI,CAAC8D,QAAQ,CAAC;IAC7B;IACA;IACA;IACA,OAAOA,QAAQ,GAAGL,YAAY,IAAIK,QAAQ,IAAIxG,MAAM,CAAC,CAAC,CAAC,EAAE;MACvDsG,GAAG,CAAC7F,IAAI,CAAC;QACPrB,KAAK,EAAEoH;MACT,CAAC,CAAC;MACF,IAAIR,IAAI,EAAE,GAAGF,SAAS,EAAE;QACtB,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzChJ,IAAI,CAAC,kCAAkC,CAAC;QAC1C;QACA;MACF;MACA6I,IAAI,CAACtB,aAAa,CAAC,CAACsB,IAAI,CAACL,aAAa,CAAC,CAAC,CAAC,GAAGtG,QAAQ,CAAC;MACrD0G,QAAQ,GAAGC,IAAI,CAACtC,OAAO,CAAC,CAAC;MACzB,IAAI0B,MAAM,EAAE;QACV,IAAIgB,YAAY,GAAGhB,MAAM,CAACiB,oBAAoB,CAACN,QAAQ,EAAED,oBAAoB,CAAC;QAC9E,IAAIM,YAAY,GAAG,CAAC,EAAE;UACpBJ,IAAI,CAACtB,aAAa,CAAC,CAACsB,IAAI,CAACL,aAAa,CAAC,CAAC,CAAC,GAAGS,YAAY,GAAG/G,QAAQ,CAAC;UACpE0G,QAAQ,GAAGC,IAAI,CAACtC,OAAO,CAAC,CAAC;QAC3B;MACF;IACF;IACA;IACAmC,GAAG,CAAC7F,IAAI,CAAC;MACPrB,KAAK,EAAEoH,QAAQ;MACfO,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAAChC,QAAQ,EAAEiC,cAAc,EAAEC,UAAU,EAAE;IAC3D,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,YAAY,GAAG,CAACH,cAAc,CAAC5F,MAAM;IACzC,IAAIc,gCAAgC,CAAC9F,kBAAkB,CAAC2I,QAAQ,CAAC,EAAEhF,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEN,KAAK,CAAC,EAAE;MAC/F;IACF;IACA,IAAI0H,YAAY,EAAE;MAChBH,cAAc,GAAG,CAAC;QAChB7H,KAAK,EAAE0F,uBAAuB,CAAC9E,MAAM,CAAC,CAAC,CAAC,EAAEgF,QAAQ,EAAEtF,KAAK;MAC3D,CAAC,EAAE;QACDN,KAAK,EAAEY,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;IACA,KAAK,IAAIqH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAAC5F,MAAM,GAAG,CAAC,EAAEgG,CAAC,EAAE,EAAE;MAClD,IAAIC,SAAS,GAAGL,cAAc,CAACI,CAAC,CAAC,CAACjI,KAAK;MACvC,IAAImI,OAAO,GAAGN,cAAc,CAACI,CAAC,GAAG,CAAC,CAAC,CAACjI,KAAK;MACzC,IAAIkI,SAAS,KAAKC,OAAO,EAAE;QACzB;MACF;MACA,IAAIzH,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAI0H,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIpB,MAAM,GAAG,KAAK;MAClB,QAAQrB,QAAQ;QACd,KAAK,MAAM;UACTlF,QAAQ,GAAGyB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACmE,KAAK,CAACrB,cAAc,GAAGtI,OAAO,GAAG,GAAG,CAAC,CAAC;UAClEyL,UAAU,GAAGhL,kBAAkB,CAACkD,KAAK,CAAC;UACtC+H,UAAU,GAAG/K,kBAAkB,CAACgD,KAAK,CAAC;UACtC;QACF,KAAK,WAAW;QAChB,KAAK,SAAS;QACd,KAAK,OAAO;UACVI,QAAQ,GAAGyE,gBAAgB,CAACF,cAAc,CAAC;UAC3CmD,UAAU,GAAGvK,eAAe,CAACyC,KAAK,CAAC;UACnC+H,UAAU,GAAGhL,eAAe,CAACiD,KAAK,CAAC;UACnC;QACF,KAAK,MAAM,CAAC,CAAC;QACb,KAAK,WAAW;QAChB,KAAK,KAAK;UACRI,QAAQ,GAAGsE,eAAe,CAACC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;UAChDmD,UAAU,GAAGtK,cAAc,CAACwC,KAAK,CAAC;UAClC+H,UAAU,GAAG9K,cAAc,CAAC+C,KAAK,CAAC;UAClC2G,MAAM,GAAG,IAAI;UACb;QACF,KAAK,UAAU;QACf,KAAK,aAAa;QAClB,KAAK,MAAM;UACTvG,QAAQ,GAAG2E,eAAe,CAACJ,cAAc,CAAC;UAC1CmD,UAAU,GAAG5K,eAAe,CAAC8C,KAAK,CAAC;UACnC+H,UAAU,GAAG5K,eAAe,CAAC6C,KAAK,CAAC;UACnC;QACF,KAAK,QAAQ;UACXI,QAAQ,GAAG4E,4BAA4B,CAACL,cAAc,EAAE,IAAI,CAAC;UAC7DmD,UAAU,GAAGrK,iBAAiB,CAACuC,KAAK,CAAC;UACrC+H,UAAU,GAAG3K,iBAAiB,CAAC4C,KAAK,CAAC;UACrC;QACF,KAAK,QAAQ;UACXI,QAAQ,GAAG4E,4BAA4B,CAACL,cAAc,EAAE,KAAK,CAAC;UAC9DmD,UAAU,GAAGpK,iBAAiB,CAACsC,KAAK,CAAC;UACrC+H,UAAU,GAAG1K,iBAAiB,CAAC2C,KAAK,CAAC;UACrC;QACF,KAAK,aAAa;UAChBI,QAAQ,GAAG8E,uBAAuB,CAACP,cAAc,CAAC;UAClDmD,UAAU,GAAGnK,sBAAsB,CAACqC,KAAK,CAAC;UAC1C+H,UAAU,GAAGzK,sBAAsB,CAAC0C,KAAK,CAAC;UAC1C;MACJ;MACA;MACA;MACA;MACA,IAAI6H,OAAO,IAAIvH,MAAM,CAAC,CAAC,CAAC,IAAIsH,SAAS,IAAItH,MAAM,CAAC,CAAC,CAAC,EAAE;QAClDiG,cAAc,CAACnG,QAAQ,EAAEwH,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEpB,MAAM,EAAEc,aAAa,CAAC;MAC7F;MACA,IAAInC,QAAQ,KAAK,MAAM,IAAIkC,UAAU,CAAC7F,MAAM,GAAG,CAAC,IAAIgG,CAAC,KAAK,CAAC,EAAE;QAC3D;QACAH,UAAU,CAACQ,OAAO,CAAC;UACjBtI,KAAK,EAAE8H,UAAU,CAAC,CAAC,CAAC,CAAC9H,KAAK,GAAGU;QAC/B,CAAC,CAAC;MACJ;IACF;IACA,KAAK,IAAIuH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAAC9F,MAAM,EAAEgG,CAAC,EAAE,EAAE;MAC7CH,UAAU,CAACzG,IAAI,CAAC0G,aAAa,CAACE,CAAC,CAAC,CAAC;IACnC;EACF;EACA,IAAIM,WAAW,GAAG,EAAE;EACpB,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,SAAS,CAAC1E,MAAM,EAAE,EAAEgG,CAAC,EAAE;IACzC,IAAIU,eAAe,GAAG1L,kBAAkB,CAAC0J,SAAS,CAACsB,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC/K,iBAAiB,CAACyJ,SAAS,CAACsB,CAAC,CAAC,CAAC,EAAE;MACpC;MACA;IACF;IACAL,aAAa,CAACjB,SAAS,CAACsB,CAAC,CAAC,EAAEM,WAAW,CAACA,WAAW,CAACtG,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,EAAEuG,iBAAiB,CAAC;IACzF,IAAII,mBAAmB,GAAGjC,SAAS,CAACsB,CAAC,GAAG,CAAC,CAAC,GAAGhL,kBAAkB,CAAC0J,SAAS,CAACsB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;IACxF,IAAIU,eAAe,KAAKC,mBAAmB,EAAE;MAC3C,IAAIJ,iBAAiB,CAACvG,MAAM,EAAE;QAC5ByG,kBAAkB,GAAGD,SAAS;QAC9B;QACAD,iBAAiB,CAACK,IAAI,CAAC,UAAU7J,CAAC,EAAE8J,CAAC,EAAE;UACrC,OAAO9J,CAAC,CAACgB,KAAK,GAAG8I,CAAC,CAAC9I,KAAK;QAC1B,CAAC,CAAC;QACF,IAAI+I,0BAA0B,GAAG,EAAE;QACnC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,iBAAiB,CAACvG,MAAM,EAAE,EAAE+G,GAAG,EAAE;UACvD,IAAIC,SAAS,GAAGT,iBAAiB,CAACQ,GAAG,CAAC,CAAChJ,KAAK;UAC5C,IAAIgJ,GAAG,KAAK,CAAC,IAAIR,iBAAiB,CAACQ,GAAG,GAAG,CAAC,CAAC,CAAChJ,KAAK,KAAKiJ,SAAS,EAAE;YAC/DF,0BAA0B,CAAC1H,IAAI,CAACmH,iBAAiB,CAACQ,GAAG,CAAC,CAAC;YACvD,IAAIC,SAAS,IAAIrI,MAAM,CAAC,CAAC,CAAC,IAAIqI,SAAS,IAAIrI,MAAM,CAAC,CAAC,CAAC,EAAE;cACpD6H,SAAS,EAAE;YACb;UACF;QACF;QACA,IAAIS,aAAa,GAAG1C,oBAAoB,GAAGvB,cAAc;QACzD;QACA,IAAIwD,SAAS,GAAGS,aAAa,GAAG,GAAG,IAAIR,kBAAkB,GAAGQ,aAAa,GAAG,GAAG,EAAE;UAC/E;QACF;QACA;QACAX,WAAW,CAAClH,IAAI,CAAC0H,0BAA0B,CAAC;QAC5C,IAAIN,SAAS,GAAGS,aAAa,IAAI3C,cAAc,KAAKI,SAAS,CAACsB,CAAC,CAAC,EAAE;UAChE;QACF;MACF;MACA;MACAO,iBAAiB,GAAG,EAAE;IACxB;EACF;EACA,IAAIW,mBAAmB,GAAGzK,MAAM,CAACG,GAAG,CAAC0J,WAAW,EAAE,UAAUT,UAAU,EAAE;IACtE,OAAOpJ,MAAM,CAACoJ,UAAU,EAAE,UAAUjI,IAAI,EAAE;MACxC,OAAOA,IAAI,CAACG,KAAK,IAAIY,MAAM,CAAC,CAAC,CAAC,IAAIf,IAAI,CAACG,KAAK,IAAIY,MAAM,CAAC,CAAC,CAAC,IAAI,CAACf,IAAI,CAAC8H,MAAM;IAC3E,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE,UAAUG,UAAU,EAAE;IACxB,OAAOA,UAAU,CAAC7F,MAAM,GAAG,CAAC;EAC9B,CAAC,CAAC;EACF,IAAIlB,KAAK,GAAG,EAAE;EACd,IAAImB,QAAQ,GAAGiH,mBAAmB,CAAClH,MAAM,GAAG,CAAC;EAC7C,KAAK,IAAIgG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,mBAAmB,CAAClH,MAAM,EAAE,EAAEgG,CAAC,EAAE;IACnD,IAAIH,UAAU,GAAGqB,mBAAmB,CAAClB,CAAC,CAAC;IACvC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,UAAU,CAAC7F,MAAM,EAAE,EAAEmH,CAAC,EAAE;MAC1C,IAAIxE,IAAI,GAAG1G,gBAAgB,CAAC4J,UAAU,CAACsB,CAAC,CAAC,CAACpJ,KAAK,EAAEM,KAAK,CAAC;MACvDS,KAAK,CAACM,IAAI,CAAC;QACTrB,KAAK,EAAE8H,UAAU,CAACsB,CAAC,CAAC,CAACpJ,KAAK;QAC1BsB,IAAI,EAAE;UACJC,KAAK,EAAEW,QAAQ,GAAG+F,CAAC;UACnBzG,aAAa,EAAEoD,IAAI;UACnBnD,aAAa,EAAEmD;QACjB;MACF,CAAC,CAAC;IACJ;EACF;EACA7D,KAAK,CAAC8H,IAAI,CAAC,UAAU7J,CAAC,EAAE8J,CAAC,EAAE;IACzB,OAAO9J,CAAC,CAACgB,KAAK,GAAG8I,CAAC,CAAC9I,KAAK;EAC1B,CAAC,CAAC;EACF;EACA,IAAIqJ,MAAM,GAAG,EAAE;EACf,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlH,KAAK,CAACkB,MAAM,EAAE,EAAEgG,CAAC,EAAE;IACrC,IAAIA,CAAC,KAAK,CAAC,IAAIlH,KAAK,CAACkH,CAAC,CAAC,CAACjI,KAAK,KAAKe,KAAK,CAACkH,CAAC,GAAG,CAAC,CAAC,CAACjI,KAAK,EAAE;MACpDqJ,MAAM,CAAChI,IAAI,CAACN,KAAK,CAACkH,CAAC,CAAC,CAAC;IACvB;EACF;EACA,OAAOoB,MAAM;AACf;AACA9K,KAAK,CAAC+K,aAAa,CAACjK,SAAS,CAAC;AAC9B,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}