{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nimport { bind } from 'zrender/lib/core/util.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\n * @param extent Both extent[0] and extent[1] should be valid number.\n *               Should be extent[0] < extent[1].\n * @param splitNumber splitNumber should be >= 1.\n */\nexport function intervalScaleNiceTicks(extent, spanWithBreaks, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var interval = result.interval = nice(spanWithBreaks / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\n * @return interval precision\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nvar ScaleCalculator = /** @class */function () {\n  function ScaleCalculator() {\n    this.normalize = normalize;\n    this.scale = scale;\n  }\n  ScaleCalculator.prototype.updateMethods = function (brkCtx) {\n    if (brkCtx.hasBreaks()) {\n      this.normalize = bind(brkCtx.normalize, brkCtx);\n      this.scale = bind(brkCtx.scale, brkCtx);\n    } else {\n      this.normalize = normalize;\n      this.scale = scale;\n    }\n  };\n  return ScaleCalculator;\n}();\nexport { ScaleCalculator };\nfunction normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nfunction scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}\nexport function logTransform(base, extent, noClampNegative) {\n  var loggedBase = Math.log(base);\n  return [\n  // log(negative) is NaN, so safe guard here.\n  // PENDING: But even getting a -Infinity still does not make sense in extent.\n  //  Just keep it as is, getting a NaN to make some previous cases works by coincidence.\n  Math.log(noClampNegative ? extent[0] : Math.max(0, extent[0])) / loggedBase, Math.log(noClampNegative ? extent[1] : Math.max(0, extent[1])) / loggedBase];\n}", "map": {"version": 3, "names": ["getPrecision", "round", "nice", "quantityExponent", "bind", "isValueNice", "val", "exp10", "Math", "pow", "abs", "f", "isIntervalOrLogScale", "scale", "type", "intervalScaleNiceTicks", "extent", "spanWithBreaks", "splitNumber", "minInterval", "maxInterval", "result", "interval", "precision", "intervalPrecision", "getIntervalPrecision", "niceTickExtent", "ceil", "floor", "fixExtent", "increaseInterval", "clamp", "idx", "max", "min", "isFinite", "contain", "ScaleCalculator", "normalize", "prototype", "updateMethods", "brkCtx", "hasBreaks", "logTransform", "base", "noClampNegative", "loggedBase", "log"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/scale/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getPrecision, round, nice, quantityExponent } from '../util/number.js';\nimport { bind } from 'zrender/lib/core/util.js';\nexport function isValueNice(val) {\n  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));\n  var f = Math.abs(val / exp10);\n  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;\n}\nexport function isIntervalOrLogScale(scale) {\n  return scale.type === 'interval' || scale.type === 'log';\n}\n/**\n * @param extent Both extent[0] and extent[1] should be valid number.\n *               Should be extent[0] < extent[1].\n * @param splitNumber splitNumber should be >= 1.\n */\nexport function intervalScaleNiceTicks(extent, spanWithBreaks, splitNumber, minInterval, maxInterval) {\n  var result = {};\n  var interval = result.interval = nice(spanWithBreaks / splitNumber, true);\n  if (minInterval != null && interval < minInterval) {\n    interval = result.interval = minInterval;\n  }\n  if (maxInterval != null && interval > maxInterval) {\n    interval = result.interval = maxInterval;\n  }\n  // Tow more digital for tick.\n  var precision = result.intervalPrecision = getIntervalPrecision(interval);\n  // Niced extent inside original extent\n  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];\n  fixExtent(niceTickExtent, extent);\n  return result;\n}\nexport function increaseInterval(interval) {\n  var exp10 = Math.pow(10, quantityExponent(interval));\n  // Increase interval\n  var f = interval / exp10;\n  if (!f) {\n    f = 1;\n  } else if (f === 2) {\n    f = 3;\n  } else if (f === 3) {\n    f = 5;\n  } else {\n    // f is 1 or 5\n    f *= 2;\n  }\n  return round(f * exp10);\n}\n/**\n * @return interval precision\n */\nexport function getIntervalPrecision(interval) {\n  // Tow more digital for tick.\n  return getPrecision(interval) + 2;\n}\nfunction clamp(niceTickExtent, idx, extent) {\n  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);\n}\n// In some cases (e.g., splitNumber is 1), niceTickExtent may be out of extent.\nexport function fixExtent(niceTickExtent, extent) {\n  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);\n  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);\n  clamp(niceTickExtent, 0, extent);\n  clamp(niceTickExtent, 1, extent);\n  if (niceTickExtent[0] > niceTickExtent[1]) {\n    niceTickExtent[0] = niceTickExtent[1];\n  }\n}\nexport function contain(val, extent) {\n  return val >= extent[0] && val <= extent[1];\n}\nvar ScaleCalculator = /** @class */function () {\n  function ScaleCalculator() {\n    this.normalize = normalize;\n    this.scale = scale;\n  }\n  ScaleCalculator.prototype.updateMethods = function (brkCtx) {\n    if (brkCtx.hasBreaks()) {\n      this.normalize = bind(brkCtx.normalize, brkCtx);\n      this.scale = bind(brkCtx.scale, brkCtx);\n    } else {\n      this.normalize = normalize;\n      this.scale = scale;\n    }\n  };\n  return ScaleCalculator;\n}();\nexport { ScaleCalculator };\nfunction normalize(val, extent) {\n  if (extent[1] === extent[0]) {\n    return 0.5;\n  }\n  return (val - extent[0]) / (extent[1] - extent[0]);\n}\nfunction scale(val, extent) {\n  return val * (extent[1] - extent[0]) + extent[0];\n}\nexport function logTransform(base, extent, noClampNegative) {\n  var loggedBase = Math.log(base);\n  return [\n  // log(negative) is NaN, so safe guard here.\n  // PENDING: But even getting a -Infinity still does not make sense in extent.\n  //  Just keep it as is, getting a NaN to make some previous cases works by coincidence.\n  Math.log(noClampNegative ? extent[0] : Math.max(0, extent[0])) / loggedBase, Math.log(noClampNegative ? extent[1] : Math.max(0, extent[1])) / loggedBase];\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAY,EAAEC,KAAK,EAAEC,IAAI,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC/E,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAE;EAC/B,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEN,gBAAgB,CAACK,IAAI,CAACE,GAAG,CAACJ,GAAG,CAAC,CAAC,CAAC;EACzD,IAAIK,CAAC,GAAGH,IAAI,CAACE,GAAG,CAACJ,GAAG,GAAGC,KAAK,CAAC;EAC7B,OAAOI,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC;AAC5D;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAACC,IAAI,KAAK,UAAU,IAAID,KAAK,CAACC,IAAI,KAAK,KAAK;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACpG,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ,GAAGpB,IAAI,CAACe,cAAc,GAAGC,WAAW,EAAE,IAAI,CAAC;EACzE,IAAIC,WAAW,IAAI,IAAI,IAAIG,QAAQ,GAAGH,WAAW,EAAE;IACjDG,QAAQ,GAAGD,MAAM,CAACC,QAAQ,GAAGH,WAAW;EAC1C;EACA,IAAIC,WAAW,IAAI,IAAI,IAAIE,QAAQ,GAAGF,WAAW,EAAE;IACjDE,QAAQ,GAAGD,MAAM,CAACC,QAAQ,GAAGF,WAAW;EAC1C;EACA;EACA,IAAIG,SAAS,GAAGF,MAAM,CAACG,iBAAiB,GAAGC,oBAAoB,CAACH,QAAQ,CAAC;EACzE;EACA,IAAII,cAAc,GAAGL,MAAM,CAACK,cAAc,GAAG,CAACzB,KAAK,CAACO,IAAI,CAACmB,IAAI,CAACX,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAAC,GAAGA,QAAQ,EAAEC,SAAS,CAAC,EAAEtB,KAAK,CAACO,IAAI,CAACoB,KAAK,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAAC,GAAGA,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAC1KM,SAAS,CAACH,cAAc,EAAEV,MAAM,CAAC;EACjC,OAAOK,MAAM;AACf;AACA,OAAO,SAASS,gBAAgBA,CAACR,QAAQ,EAAE;EACzC,IAAIf,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEN,gBAAgB,CAACmB,QAAQ,CAAC,CAAC;EACpD;EACA,IAAIX,CAAC,GAAGW,QAAQ,GAAGf,KAAK;EACxB,IAAI,CAACI,CAAC,EAAE;IACNA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;IAClBA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;IAClBA,CAAC,GAAG,CAAC;EACP,CAAC,MAAM;IACL;IACAA,CAAC,IAAI,CAAC;EACR;EACA,OAAOV,KAAK,CAACU,CAAC,GAAGJ,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA,OAAO,SAASkB,oBAAoBA,CAACH,QAAQ,EAAE;EAC7C;EACA,OAAOtB,YAAY,CAACsB,QAAQ,CAAC,GAAG,CAAC;AACnC;AACA,SAASS,KAAKA,CAACL,cAAc,EAAEM,GAAG,EAAEhB,MAAM,EAAE;EAC1CU,cAAc,CAACM,GAAG,CAAC,GAAGxB,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAAC0B,GAAG,CAACR,cAAc,CAACM,GAAG,CAAC,EAAEhB,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF;AACA;AACA,OAAO,SAASa,SAASA,CAACH,cAAc,EAAEV,MAAM,EAAE;EAChD,CAACmB,QAAQ,CAACT,cAAc,CAAC,CAAC,CAAC,CAAC,KAAKA,cAAc,CAAC,CAAC,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/D,CAACmB,QAAQ,CAACT,cAAc,CAAC,CAAC,CAAC,CAAC,KAAKA,cAAc,CAAC,CAAC,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/De,KAAK,CAACL,cAAc,EAAE,CAAC,EAAEV,MAAM,CAAC;EAChCe,KAAK,CAACL,cAAc,EAAE,CAAC,EAAEV,MAAM,CAAC;EAChC,IAAIU,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,EAAE;IACzCA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC;EACvC;AACF;AACA,OAAO,SAASU,OAAOA,CAAC9B,GAAG,EAAEU,MAAM,EAAE;EACnC,OAAOV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC,IAAIV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC;AAC7C;AACA,IAAIqB,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACzB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACzB,KAAK,GAAGA,KAAK;EACpB;EACAwB,eAAe,CAACE,SAAS,CAACC,aAAa,GAAG,UAAUC,MAAM,EAAE;IAC1D,IAAIA,MAAM,CAACC,SAAS,CAAC,CAAC,EAAE;MACtB,IAAI,CAACJ,SAAS,GAAGlC,IAAI,CAACqC,MAAM,CAACH,SAAS,EAAEG,MAAM,CAAC;MAC/C,IAAI,CAAC5B,KAAK,GAAGT,IAAI,CAACqC,MAAM,CAAC5B,KAAK,EAAE4B,MAAM,CAAC;IACzC,CAAC,MAAM;MACL,IAAI,CAACH,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACzB,KAAK,GAAGA,KAAK;IACpB;EACF,CAAC;EACD,OAAOwB,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,SAASA,eAAe;AACxB,SAASC,SAASA,CAAChC,GAAG,EAAEU,MAAM,EAAE;EAC9B,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3B,OAAO,GAAG;EACZ;EACA,OAAO,CAACV,GAAG,GAAGU,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC;AACpD;AACA,SAASH,KAAKA,CAACP,GAAG,EAAEU,MAAM,EAAE;EAC1B,OAAOV,GAAG,IAAIU,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;AAClD;AACA,OAAO,SAAS2B,YAAYA,CAACC,IAAI,EAAE5B,MAAM,EAAE6B,eAAe,EAAE;EAC1D,IAAIC,UAAU,GAAGtC,IAAI,CAACuC,GAAG,CAACH,IAAI,CAAC;EAC/B,OAAO;EACP;EACA;EACA;EACApC,IAAI,CAACuC,GAAG,CAACF,eAAe,GAAG7B,MAAM,CAAC,CAAC,CAAC,GAAGR,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG8B,UAAU,EAAEtC,IAAI,CAACuC,GAAG,CAACF,eAAe,GAAG7B,MAAM,CAAC,CAAC,CAAC,GAAGR,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAEjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG8B,UAAU,CAAC;AAC3J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}