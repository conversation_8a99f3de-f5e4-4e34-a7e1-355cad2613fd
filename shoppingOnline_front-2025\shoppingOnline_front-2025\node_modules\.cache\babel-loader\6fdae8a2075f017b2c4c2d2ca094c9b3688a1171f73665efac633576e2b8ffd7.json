{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"demo-input-size\"\n};\nconst _hoisted_2 = {\n  style: {\n    \"padding-top\": \"10px\"\n  }\n};\nconst _hoisted_3 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_4 = {\n  key: 0\n};\nconst _hoisted_5 = {\n  key: 1\n};\nconst _hoisted_6 = {\n  class: \"block\",\n  style: {\n    \"flex\": \"0 0 auto\"\n  }\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", null, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_select, {\n    modelValue: $data.searchMode,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.searchMode = $event),\n    placeholder: \"请选择\",\n    style: {\n      \"width\": \"150px\",\n      \"margin-right\": \"10px\"\n    }\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      value: \"id\",\n      label: \"用户id\"\n    }), _createVNode(_component_el_option, {\n      value: \"username\",\n      label: \"账号\"\n    }), _createVNode(_component_el_option, {\n      value: \"nickname\",\n      label: \"昵称\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), $data.searchMode === 'id' ? (_openBlock(), _createBlock(_component_el_input, {\n    key: 0,\n    placeholder: \"请输入用户id\",\n    \"prefix-icon\": \"el-icon-search\",\n    style: {\n      \"width\": \"250px\",\n      \"padding-right\": \"5px\"\n    },\n    modelValue: $data.searchParams.id,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.searchParams.id = $event)\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), $data.searchMode === 'username' ? (_openBlock(), _createBlock(_component_el_input, {\n    key: 1,\n    placeholder: \"请输入账号\",\n    \"prefix-icon\": \"el-icon-search\",\n    style: {\n      \"width\": \"250px\",\n      \"padding-right\": \"5px\"\n    },\n    modelValue: $data.searchParams.username,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.searchParams.username = $event)\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), $data.searchMode === 'nickname' ? (_openBlock(), _createBlock(_component_el_input, {\n    key: 2,\n    placeholder: \"请输入昵称\",\n    \"prefix-icon\": \"el-icon-search\",\n    style: {\n      \"width\": \"250px\",\n      \"padding-right\": \"5px\"\n    },\n    modelValue: $data.searchParams.nickname,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.searchParams.nickname = $event)\n  }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $options.search\n  }, {\n    default: _withCtx(() => [...(_cache[12] || (_cache[12] = [_createTextVNode(\" 搜索 \", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"warning\",\n    onClick: $options.reload\n  }, {\n    default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\" 重置 \", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createCommentVNode(\"          按钮栏\"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $options.handleAdd,\n    style: {\n      \"font-size\": \"18px\"\n    }\n  }, {\n    default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\" 新增\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    type: \"danger\",\n    onClick: $options.delBatch,\n    style: {\n      \"font-size\": \"18px\"\n    }\n  }, {\n    default: _withCtx(() => [...(_cache[15] || (_cache[15] = [_createTextVNode(\" 批量删除\", -1 /* CACHED */)]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _createCommentVNode(\"          弹窗\"), _createVNode(_component_el_dialog, {\n    title: $data.dialogTitle,\n    modelValue: $data.dialogFormVisible,\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.dialogFormVisible = $event)\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n      onClick: _cache[10] || (_cache[10] = $event => $data.dialogFormVisible = false),\n      style: {\n        \"font-size\": \"20px\"\n      }\n    }, {\n      default: _withCtx(() => [...(_cache[17] || (_cache[17] = [_createTextVNode(\"取消\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $options.save,\n      style: {\n        \"font-size\": \"20px\"\n      }\n    }, {\n      default: _withCtx(() => [...(_cache[18] || (_cache[18] = [_createTextVNode(\"确定\", -1 /* CACHED */)]))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      \"label-width\": \"50px\",\n      style: {\n        \"padding\": \"0 60px\"\n      }\n    }, {\n      default: _withCtx(() => [$data.dialogTitle == '新增用户' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"账号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.user.username,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.user.username = $event),\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), $data.dialogTitle == '新增用户' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"密码\"\n      }, {\n        default: _withCtx(() => [...(_cache[16] || (_cache[16] = [_createTextVNode(\" 123456 \", -1 /* CACHED */)]))]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"昵称\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.user.nickname,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.user.nickname = $event),\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"身份\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $data.user.role,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.user.role = $event),\n          placeholder: \"请选择\"\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.roleOptions, item => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.value,\n              label: item.label,\n              value: item.value\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"电话\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.user.phone,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.user.phone = $event),\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.user.email,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.user.email = $event),\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"地址\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $data.user.address,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.user.address = $event),\n          autocomplete: \"off\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"title\", \"modelValue\"]), _createCommentVNode(\"          表格\"), _createVNode(_component_el_table, {\n    data: $data.tableData,\n    \"background-color\": \"black\",\n    onSelectionChange: $options.handleSelectionChange\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_table_column, {\n      type: \"selection\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"id\",\n      label: \"id\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"username\",\n      label: \"账号\",\n      width: \"150\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"身份\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.role === 'user' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_4, \"用户\")) : _createCommentVNode(\"v-if\", true), scope.row.role === 'admin' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, \"管理员\")) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"nickname\",\n      label: \"昵称\",\n      width: \"180\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"phone\",\n      label: \"电话\",\n      width: \"180\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"email\",\n      label: \"邮箱\",\n      width: \"180\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"address\",\n      label: \"地址\",\n      width: \"350\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      width: \"250\",\n      fixed: \"right\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        style: {\n          \"font-size\": \"18px\"\n        },\n        type: \"success\",\n        onClick: $event => $options.handleEdit(scope.row)\n      }, {\n        default: _withCtx(() => [...(_cache[19] || (_cache[19] = [_createTextVNode(\" 编辑 \", -1 /* CACHED */)]))]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        style: {\n          \"font-size\": \"18px\"\n        },\n        type: \"danger\",\n        onClick: $event => $options.handleDelete(scope.row.id)\n      }, {\n        default: _withCtx(() => [...(_cache[20] || (_cache[20] = [_createTextVNode(\" 删除 \", -1 /* CACHED */)]))]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"]), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_pagination, {\n    \"current-page\": $data.currentPage,\n    \"page-sizes\": [3, 5, 8, 10],\n    \"page-size\": $data.pageSize,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    total: $data.total,\n    onSizeChange: $options.handleSizeChange,\n    onCurrentChange: $options.handleCurrentPage\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_select", "$data", "searchMode", "$event", "placeholder", "_component_el_option", "value", "label", "_createBlock", "_component_el_input", "searchParams", "id", "username", "nickname", "_component_el_button", "type", "onClick", "$options", "search", "_cache", "reload", "_createCommentVNode", "_hoisted_2", "handleAdd", "delBatch", "_component_el_dialog", "title", "dialogTitle", "dialogFormVisible", "footer", "_withCtx", "_hoisted_3", "save", "_component_el_form", "_component_el_form_item", "user", "autocomplete", "role", "_Fragment", "_renderList", "roleOptions", "item", "key", "phone", "email", "address", "_component_el_table", "data", "tableData", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "prop", "width", "default", "scope", "row", "_hoisted_4", "_hoisted_5", "fixed", "handleEdit", "handleDelete", "_hoisted_6", "_component_el_pagination", "currentPage", "pageSize", "layout", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentPage"], "sources": ["E:\\shixi 8.25\\work1\\shopping-front\\shoppingOnline_front-2025\\shoppingOnline_front-2025\\shoppingOnline_front-2025\\src\\views\\manage\\User.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"demo-input-size\">\r\n      <el-select\r\n        v-model=\"searchMode\"\r\n        placeholder=\"请选择\"\r\n        style=\"width: 150px; margin-right: 10px\"\r\n      >\r\n        <el-option value=\"id\" label=\"用户id\"></el-option>\r\n        <el-option value=\"username\" label=\"账号\"></el-option>\r\n        <el-option value=\"nickname\" label=\"昵称\"></el-option>\r\n      </el-select>\r\n      <el-input\r\n        v-if=\"searchMode === 'id'\"\r\n        placeholder=\"请输入用户id\"\r\n        prefix-icon=\"el-icon-search\"\r\n        style=\"width: 250px; padding-right: 5px\"\r\n        v-model=\"searchParams.id\"\r\n      ></el-input>\r\n      <el-input\r\n        v-if=\"searchMode === 'username'\"\r\n        placeholder=\"请输入账号\"\r\n        prefix-icon=\"el-icon-search\"\r\n        style=\"width: 250px; padding-right: 5px\"\r\n        v-model=\"searchParams.username\"\r\n      ></el-input>\r\n      <el-input\r\n        v-if=\"searchMode === 'nickname'\"\r\n        placeholder=\"请输入昵称\"\r\n        prefix-icon=\"el-icon-search\"\r\n        style=\"width: 250px; padding-right: 5px\"\r\n        v-model=\"searchParams.nickname\"\r\n      ></el-input>\r\n      <el-button type=\"primary\" @click=\"search\"> 搜索 </el-button>\r\n      <el-button type=\"warning\" @click=\"reload\"> 重置 </el-button>\r\n    </div>\r\n    <!--          按钮栏-->\r\n    <div style=\"padding-top: 10px\">\r\n      <el-button type=\"primary\" @click=\"handleAdd\" style=\"font-size: 18px\">\r\n        新增</el-button\r\n      >\r\n      <el-button type=\"danger\" @click=\"delBatch\" style=\"font-size: 18px\">\r\n        批量删除</el-button\r\n      >\r\n    </div>\r\n    <!--          弹窗-->\r\n    <el-dialog :title=\"dialogTitle\" v-model=\"dialogFormVisible\">\r\n      <el-form label-width=\"50px\" style=\"padding: 0 60px\">\r\n        <el-form-item label=\"账号\" v-if=\"dialogTitle == '新增用户'\">\r\n          <el-input v-model=\"user.username\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" v-if=\"dialogTitle == '新增用户'\">\r\n          123456\r\n        </el-form-item>\r\n        <el-form-item label=\"昵称\">\r\n          <el-input v-model=\"user.nickname\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"身份\">\r\n          <el-select v-model=\"user.role\" placeholder=\"请选择\">\r\n            <el-option\r\n              v-for=\"item in roleOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"电话\">\r\n          <el-input v-model=\"user.phone\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\">\r\n          <el-input v-model=\"user.email\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"地址\">\r\n          <el-input v-model=\"user.address\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogFormVisible = false\" style=\"font-size: 20px\"\r\n            >取消</el-button\r\n          >\r\n          <el-button type=\"primary\" @click=\"save\" style=\"font-size: 20px\"\r\n            >确定</el-button\r\n          >\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!--          表格-->\r\n    <el-table\r\n      :data=\"tableData\"\r\n      background-color=\"black\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\"></el-table-column>\r\n      <el-table-column prop=\"id\" label=\"id\" width=\"100\"></el-table-column>\r\n      <el-table-column\r\n        prop=\"username\"\r\n        label=\"账号\"\r\n        width=\"150\"\r\n      ></el-table-column>\r\n      <el-table-column label=\"身份\" width=\"150\">\r\n        <template v-slot:default=\"scope\">\r\n          <span v-if=\"scope.row.role === 'user'\">用户</span>\r\n          <span v-if=\"scope.row.role === 'admin'\">管理员</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"nickname\"\r\n        label=\"昵称\"\r\n        width=\"180\"\r\n      ></el-table-column>\r\n      <el-table-column prop=\"phone\" label=\"电话\" width=\"180\"></el-table-column>\r\n      <el-table-column prop=\"email\" label=\"邮箱\" width=\"180\"></el-table-column>\r\n      <el-table-column\r\n        prop=\"address\"\r\n        label=\"地址\"\r\n        width=\"350\"\r\n      ></el-table-column>\r\n      <el-table-column label=\"操作\" width=\"250\" fixed=\"right\">\r\n        <template v-slot:default=\"scope\">\r\n          <el-button\r\n            style=\"font-size: 18px\"\r\n            type=\"success\"\r\n            @click=\"handleEdit(scope.row)\"\r\n          >\r\n            编辑\r\n          </el-button>\r\n          <el-button\r\n            style=\"font-size: 18px\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete(scope.row.id)\"\r\n          >\r\n            删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <div class=\"block\" style=\"flex: 0 0 auto\">\r\n      <el-pagination\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[3, 5, 8, 10]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentPage\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport md5 from \"js-md5\";\r\nimport { apiRequest } from \"@/utils/request\";\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nexport default {\r\n  name: \"User\",\r\n\r\n  created() {\r\n    //加载用户信息\r\n    this.load();\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      roleOptions: [\r\n        {\r\n          value: \"admin\",\r\n          label: \"管理员\",\r\n        },\r\n        {\r\n          value: \"user\",\r\n          label: \"用户\",\r\n        },\r\n      ],\r\n      roleValue: \"\",\r\n      total: 0,\r\n      pageSize: 5,\r\n      currentPage: 1,\r\n      searchMode: \"id\",\r\n      searchParams: {\r\n        id: \"\",\r\n        username: \"\",\r\n        nickname: \"\",\r\n      },\r\n      dialogFormVisible: false,\r\n      dialogTitle: \"\",\r\n      user: {\r\n        username: \"\",\r\n        nickname: \"\",\r\n        newPassword: \"\",\r\n        role: \"\",\r\n        phone: \"\",\r\n        email: \"\",\r\n        address: \"\",\r\n      },\r\n      multipleSelection: [],\r\n    };\r\n  },\r\n  methods: {\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.load();\r\n    },\r\n    handleCurrentPage(currentPage) {\r\n      this.currentPage = currentPage;\r\n      this.load();\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.multipleSelection = val;\r\n    },\r\n    //加载用户信息\r\n    async load() {\r\n      try {\r\n        const res = await apiRequest({\r\n          url: \"/userAPI/user/page\",\r\n          method: \"get\",\r\n          params: {\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.searchParams.id,\r\n            username: this.searchParams.username,\r\n            nickname: this.searchParams.nickname,\r\n          },\r\n        });\r\n        if (res.code === \"200\") {\r\n          this.tableData = res.data.records;\r\n          this.total = res.data.total;\r\n        } else {\r\n          ElMessage.error(res.msg || \"获取用户信息失败\");\r\n        }\r\n      } catch (e) {\r\n        console.error(e);\r\n        ElMessage({\r\n          showClose: true,\r\n          message: e.message || e,\r\n          type: \"error\",\r\n          duration: 5000,\r\n        });\r\n      }\r\n    },\r\n    search() {\r\n      this.currentPage = 1;\r\n      this.load();\r\n    },\r\n    reload() {\r\n      this.searchParams.id = \"\";\r\n      this.searchParams.username = \"\";\r\n      this.searchParams.nickname = \"\";\r\n      this.load();\r\n    },\r\n\r\n    //插入或修改\r\n    async save() {\r\n      if (this.dialogTitle == \"新增用户\") {\r\n        if (this.user.username.trim() == \"\") {\r\n          this.$message.error(\"账号不能为空\");\r\n          return;\r\n        }\r\n        this.user.newPassword = md5(\"123456\");\r\n      }\r\n      if (this.user.nickname.trim() == \"\") {\r\n        this.$message.error(\"昵称不能为空\");\r\n        return;\r\n      }\r\n      if (this.user.role.trim() == \"\") {\r\n        this.$message.error(\"身份不能为空\");\r\n        return;\r\n      }\r\n      if (this.user.phone.trim() == \"\") {\r\n        this.$message.error(\"电话不能为空\");\r\n        return;\r\n      }\r\n      if (this.user.email.trim() == \"\") {\r\n        this.$message.error(\"邮箱不能为空\");\r\n        return;\r\n      }\r\n      this.dialogTitle = \"新增用户\";\r\n      const submitData = {\r\n        id: this.user.id,\r\n        username: this.user.username,\r\n        nickname: this.user.nickname,\r\n        newPassword: this.user.newPassword,\r\n        role: this.user.role,\r\n        phone: this.user.phone,\r\n        email: this.user.email,\r\n        address: this.user.address,\r\n      };\r\n\r\n      try {\r\n        const res = await apiRequest({\r\n          url: \"/userAPI/saveUpdate\",\r\n          method: \"post\",\r\n          data: submitData,\r\n        });\r\n        if (res.code === \"200\") {\r\n          (this.dialogFormVisible = false), this.load();\r\n        } else {\r\n          ElMessage.error(res.msg);\r\n        }\r\n      } catch (e) {\r\n        console.error(e);\r\n        ElMessage({\r\n          showClose: true,\r\n          message: e.message || e,\r\n          type: \"error\",\r\n          duration: 5000,\r\n        });\r\n      }\r\n    },\r\n    handleAdd() {\r\n      this.dialogTitle = \"新增用户\";\r\n      this.dialogFormVisible = true;\r\n      this.user = {\r\n        username: \"\",\r\n        nickname: \"\",\r\n        newPassword: \"\",\r\n        role: \"\",\r\n        phone: \"\",\r\n        email: \"\",\r\n        address: \"\",\r\n      };\r\n    },\r\n    //编辑\r\n    handleEdit(row) {\r\n      this.user = JSON.parse(JSON.stringify(row));\r\n      this.dialogTitle = \"编辑用户\";\r\n      this.dialogFormVisible = true;\r\n    },\r\n    //删除\r\n    async handleDelete(id) {\r\n      try {\r\n        await this.$confirm(\"确认删除该用户吗?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        });\r\n        const res = await apiRequest({\r\n          url: \"/userAPI/user/\" + id,\r\n          method: \"delete\",\r\n        });\r\n        console.log(\"res：\", res); // 使用对象打印，不要用字符串拼接\r\n        if (res.code === \"200\") {\r\n          ElMessage.success(\"删除成功\");\r\n          this.load(); // 刷新列表\r\n        } else {\r\n          ElMessage.error(res.msg || \"操作失败\");\r\n        }\r\n      } catch (e) {\r\n        // 用户取消删除 或 请求失败\r\n        if (e !== \"cancel\") {\r\n          console.error(\"请求出错：\", e);\r\n          ElMessage({\r\n            type: \"error\",\r\n            message: \"网络异常，请重试\",\r\n            duration: 5000,\r\n          });\r\n        }\r\n        // 如果是取消，不提示错误\r\n      }\r\n    },\r\n    // 批量删除\r\n    async delBatch() {\r\n      // 检查是否有选中项\r\n      if (!this.multipleSelection.length) {\r\n        ElMessage.warning(\"请先选择要删除的用户\");\r\n        return;\r\n      }\r\n      const ids = this.multipleSelection.map((v) => v.id);\r\n      console.log(\"选中的用户ID：\", ids);\r\n      try {\r\n        // 弹出确认框\r\n        await ElMessageBox.confirm(\"确认删除这些用户吗？\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        });\r\n        // 发起批量删除请求\r\n        const res = await apiRequest({\r\n          url: \"/userAPI/user/del/batch\",\r\n          method: \"post\",\r\n          data: ids, // 假设后端接收一个 ID 数组\r\n        });\r\n        console.log(\"后端响应：\", res);\r\n        // 判断业务状态码\r\n        if (res.code === \"200\") {\r\n          ElMessage.success(\"批量删除成功\");\r\n          this.load(); // 刷新列表\r\n        } else {\r\n          ElMessage.error(res.msg || \"批量删除失败\");\r\n        }\r\n      } catch (e) {\r\n        // 用户取消或请求失败\r\n        if (e !== \"cancel\") {\r\n          console.error(\"批量删除请求异常：\", e);\r\n          ElMessage({\r\n            type: \"error\",\r\n            message: \"网络异常，请重试\",\r\n            duration: 5000,\r\n          });\r\n        } else {\r\n          ElMessage.info(\"已取消删除\");\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n</style>"], "mappings": ";;EAESA,KAAK,EAAC;AAAiB;;EAmCvBC,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;EA2CrBD,KAAK,EAAC;AAAe;;;;;;;;EA6DzBA,KAAK,EAAC,OAAO;EAACC,KAAsB,EAAtB;IAAA;EAAA;;;;;;;;;;;;;uBA5IrBC,mBAAA,CAwJM,cAvJJC,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJC,YAAA,CAQYC,oBAAA;gBAPDC,KAAA,CAAAC,UAAU;+DAAVD,KAAA,CAAAC,UAAU,GAAAC,MAAA;IACnBC,WAAW,EAAC,KAAK;IACjBT,KAAwC,EAAxC;MAAA;MAAA;IAAA;;sBAEA,MAA+C,CAA/CI,YAAA,CAA+CM,oBAAA;MAApCC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC5BR,YAAA,CAAmDM,oBAAA;MAAxCC,KAAK,EAAC,UAAU;MAACC,KAAK,EAAC;QAClCR,YAAA,CAAmDM,oBAAA;MAAxCC,KAAK,EAAC,UAAU;MAACC,KAAK,EAAC;;;qCAG5BN,KAAA,CAAAC,UAAU,a,cADlBM,YAAA,CAMYC,mBAAA;;IAJVL,WAAW,EAAC,SAAS;IACrB,aAAW,EAAC,gBAAgB;IAC5BT,KAAwC,EAAxC;MAAA;MAAA;IAAA,CAAwC;gBAC/BM,KAAA,CAAAS,YAAY,CAACC,EAAE;+DAAfV,KAAA,CAAAS,YAAY,CAACC,EAAE,GAAAR,MAAA;gFAGlBF,KAAA,CAAAC,UAAU,mB,cADlBM,YAAA,CAMYC,mBAAA;;IAJVL,WAAW,EAAC,OAAO;IACnB,aAAW,EAAC,gBAAgB;IAC5BT,KAAwC,EAAxC;MAAA;MAAA;IAAA,CAAwC;gBAC/BM,KAAA,CAAAS,YAAY,CAACE,QAAQ;+DAArBX,KAAA,CAAAS,YAAY,CAACE,QAAQ,GAAAT,MAAA;gFAGxBF,KAAA,CAAAC,UAAU,mB,cADlBM,YAAA,CAMYC,mBAAA;;IAJVL,WAAW,EAAC,OAAO;IACnB,aAAW,EAAC,gBAAgB;IAC5BT,KAAwC,EAAxC;MAAA;MAAA;IAAA,CAAwC;gBAC/BM,KAAA,CAAAS,YAAY,CAACG,QAAQ;+DAArBZ,KAAA,CAAAS,YAAY,CAACG,QAAQ,GAAAV,MAAA;gFAEhCJ,YAAA,CAA0De,oBAAA;IAA/CC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAC;;sBAAQ,MAAI,KAAAC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;kCAC9CpB,YAAA,CAA0De,oBAAA;IAA/CC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAG;;sBAAQ,MAAI,KAAAD,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,mB;;oCAEhDE,mBAAA,iBAAoB,EACpBxB,mBAAA,CAOM,OAPNyB,UAOM,GANJvB,YAAA,CACwBe,oBAAA;IADbC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAEC,QAAA,CAAAM,SAAS;IAAE5B,KAAuB,EAAvB;MAAA;IAAA;;sBAAwB,MACjE,KAAAwB,MAAA,SAAAA,MAAA,Q,iBADiE,KACjE,mB;;kCAEJpB,YAAA,CAC0Be,oBAAA;IADfC,IAAI,EAAC,QAAQ;IAAEC,OAAK,EAAEC,QAAA,CAAAO,QAAQ;IAAE7B,KAAuB,EAAvB;MAAA;IAAA;;sBAAwB,MAC7D,KAAAwB,MAAA,SAAAA,MAAA,Q,iBAD6D,OAC7D,mB;;oCAGRE,mBAAA,gBAAmB,EACnBtB,YAAA,CA2CY0B,oBAAA;IA3CAC,KAAK,EAAEzB,KAAA,CAAA0B,WAAW;gBAAW1B,KAAA,CAAA2B,iBAAiB;iEAAjB3B,KAAA,CAAA2B,iBAAiB,GAAAzB,MAAA;;IAiC7C0B,MAAM,EAAAC,QAAA,CACf,MAOM,CAPNjC,mBAAA,CAOM,OAPNkC,UAOM,GANJhC,YAAA,CAC6Be,oBAAA;MADjBE,OAAK,EAAAG,MAAA,SAAAA,MAAA,OAAAhB,MAAA,IAAEF,KAAA,CAAA2B,iBAAiB;MAAUjC,KAAuB,EAAvB;QAAA;MAAA;;wBAC3C,MAAE,KAAAwB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;QAELpB,YAAA,CAC6Be,oBAAA;MADlBC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,QAAA,CAAAe,IAAI;MAAErC,KAAuB,EAAvB;QAAA;MAAA;;wBACrC,MAAE,KAAAwB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,mB;;;sBAtCT,MA+BU,CA/BVpB,YAAA,CA+BUkC,kBAAA;MA/BD,aAAW,EAAC,MAAM;MAACtC,KAAuB,EAAvB;QAAA;MAAA;;wBAS1B,MAGJ,CAXmCM,KAAA,CAAA0B,WAAW,c,cAA1CnB,YAAA,CAEe0B,uBAAA;;QAFD3B,KAAK,EAAC;;0BAClB,MAAgE,CAAhER,YAAA,CAAgEU,mBAAA;sBAA7CR,KAAA,CAAAkC,IAAI,CAACvB,QAAQ;qEAAbX,KAAA,CAAAkC,IAAI,CAACvB,QAAQ,GAAAT,MAAA;UAAEiC,YAAY,EAAC;;;+CAElBnC,KAAA,CAAA0B,WAAW,c,cAA1CnB,YAAA,CAEe0B,uBAAA;;QAFD3B,KAAK,EAAC;;0BAAkC,MAEtD,KAAAY,MAAA,SAAAA,MAAA,Q,iBAFsD,UAEtD,mB;;+CACApB,YAAA,CAEemC,uBAAA;QAFD3B,KAAK,EAAC;MAAI;0BACtB,MAAgE,CAAhER,YAAA,CAAgEU,mBAAA;sBAA7CR,KAAA,CAAAkC,IAAI,CAACtB,QAAQ;qEAAbZ,KAAA,CAAAkC,IAAI,CAACtB,QAAQ,GAAAV,MAAA;UAAEiC,YAAY,EAAC;;;UAEjDrC,YAAA,CAUemC,uBAAA;QAVD3B,KAAK,EAAC;MAAI;0BACtB,MAQY,CARZR,YAAA,CAQYC,oBAAA;sBARQC,KAAA,CAAAkC,IAAI,CAACE,IAAI;qEAATpC,KAAA,CAAAkC,IAAI,CAACE,IAAI,GAAAlC,MAAA;UAAEC,WAAW,EAAC;;4BAEvC,MAA2B,E,kBAD7BR,mBAAA,CAMY0C,SAAA,QAAAC,WAAA,CALKtC,KAAA,CAAAuC,WAAW,EAAnBC,IAAI;iCADbjC,YAAA,CAMYH,oBAAA;cAJTqC,GAAG,EAAED,IAAI,CAACnC,KAAK;cACfC,KAAK,EAAEkC,IAAI,CAAClC,KAAK;cACjBD,KAAK,EAAEmC,IAAI,CAACnC;;;;;;UAMnBP,YAAA,CAEemC,uBAAA;QAFD3B,KAAK,EAAC;MAAI;0BACtB,MAA6D,CAA7DR,YAAA,CAA6DU,mBAAA;sBAA1CR,KAAA,CAAAkC,IAAI,CAACQ,KAAK;qEAAV1C,KAAA,CAAAkC,IAAI,CAACQ,KAAK,GAAAxC,MAAA;UAAEiC,YAAY,EAAC;;;UAE9CrC,YAAA,CAEemC,uBAAA;QAFD3B,KAAK,EAAC;MAAI;0BACtB,MAA6D,CAA7DR,YAAA,CAA6DU,mBAAA;sBAA1CR,KAAA,CAAAkC,IAAI,CAACS,KAAK;qEAAV3C,KAAA,CAAAkC,IAAI,CAACS,KAAK,GAAAzC,MAAA;UAAEiC,YAAY,EAAC;;;UAE9CrC,YAAA,CAEemC,uBAAA;QAFD3B,KAAK,EAAC;MAAI;0BACtB,MAA+D,CAA/DR,YAAA,CAA+DU,mBAAA;sBAA5CR,KAAA,CAAAkC,IAAI,CAACU,OAAO;qEAAZ5C,KAAA,CAAAkC,IAAI,CAACU,OAAO,GAAA1C,MAAA;UAAEiC,YAAY,EAAC;;;;;;;8CAepDf,mBAAA,gBAAmB,EACnBtB,YAAA,CAgDW+C,mBAAA;IA/CRC,IAAI,EAAE9C,KAAA,CAAA+C,SAAS;IAChB,kBAAgB,EAAC,OAAO;IACvBC,iBAAgB,EAAEhC,QAAA,CAAAiC;;sBAEnB,MAAoD,CAApDnD,YAAA,CAAoDoD,0BAAA;MAAnCpC,IAAI,EAAC;IAAW,IACjChB,YAAA,CAAoEoD,0BAAA;MAAnDC,IAAI,EAAC,IAAI;MAAC7C,KAAK,EAAC,IAAI;MAAC8C,KAAK,EAAC;QAC5CtD,YAAA,CAImBoD,0BAAA;MAHjBC,IAAI,EAAC,UAAU;MACf7C,KAAK,EAAC,IAAI;MACV8C,KAAK,EAAC;QAERtD,YAAA,CAKkBoD,0BAAA;MALD5C,KAAK,EAAC,IAAI;MAAC8C,KAAK,EAAC;;MACfC,OAAO,EAAAxB,QAAA,CAQ7ByB,KARoC,KACjBA,KAAK,CAACC,GAAG,CAACnB,IAAI,e,cAA1BzC,mBAAA,CAAgD,QAAA6D,UAAA,EAAT,IAAE,K,mCAC7BF,KAAK,CAACC,GAAG,CAACnB,IAAI,gB,cAA1BzC,mBAAA,CAAkD,QAAA8D,UAAA,EAAV,KAAG,K;;QAG/C3D,YAAA,CAImBoD,0BAAA;MAHjBC,IAAI,EAAC,UAAU;MACf7C,KAAK,EAAC,IAAI;MACV8C,KAAK,EAAC;QAERtD,YAAA,CAAuEoD,0BAAA;MAAtDC,IAAI,EAAC,OAAO;MAAC7C,KAAK,EAAC,IAAI;MAAC8C,KAAK,EAAC;QAC/CtD,YAAA,CAAuEoD,0BAAA;MAAtDC,IAAI,EAAC,OAAO;MAAC7C,KAAK,EAAC,IAAI;MAAC8C,KAAK,EAAC;QAC/CtD,YAAA,CAImBoD,0BAAA;MAHjBC,IAAI,EAAC,SAAS;MACd7C,KAAK,EAAC,IAAI;MACV8C,KAAK,EAAC;QAERtD,YAAA,CAiBkBoD,0BAAA;MAjBD5C,KAAK,EAAC,IAAI;MAAC8C,KAAK,EAAC,KAAK;MAACM,KAAK,EAAC;;MAC3BL,OAAO,EAAAxB,QAAA,CAOVyB,KAPiB,KAC7BxD,YAAA,CAMYe,oBAAA;QALVnB,KAAuB,EAAvB;UAAA;QAAA,CAAuB;QACvBoB,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAb,MAAA,IAAEc,QAAA,CAAA2C,UAAU,CAACL,KAAK,CAACC,GAAG;;0BAC7B,MAED,KAAArC,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,mB;;wDACApB,YAAA,CAMYe,oBAAA;QALVnB,KAAuB,EAAvB;UAAA;QAAA,CAAuB;QACvBoB,IAAI,EAAC,QAAQ;QACZC,OAAK,EAAAb,MAAA,IAAEc,QAAA,CAAA4C,YAAY,CAACN,KAAK,CAACC,GAAG,CAAC7C,EAAE;;0BAClC,MAED,KAAAQ,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,mB;;;;;;oDAINtB,mBAAA,CAWM,OAXNiE,UAWM,GAVJ/D,YAAA,CASgBgE,wBAAA;IARb,cAAY,EAAE9D,KAAA,CAAA+D,WAAW;IACzB,YAAU,EAAE,aAAa;IACzB,WAAS,EAAE/D,KAAA,CAAAgE,QAAQ;IACpBC,MAAM,EAAC,yCAAyC;IAC/CC,KAAK,EAAElE,KAAA,CAAAkE,KAAK;IACZC,YAAW,EAAEnD,QAAA,CAAAoD,gBAAgB;IAC7BC,eAAc,EAAErD,QAAA,CAAAsD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}