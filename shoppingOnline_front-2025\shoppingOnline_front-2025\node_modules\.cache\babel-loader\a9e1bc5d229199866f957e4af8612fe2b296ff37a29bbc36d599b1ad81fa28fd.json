{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { onIrrelevantElement } from './cursorHelper.js';\nimport * as graphicUtil from '../../util/graphic.js';\nexport function makeRectPanelClipPath(rect) {\n  rect = normalizeRect(rect);\n  return function (localPoints) {\n    return graphicUtil.clipPointsByRect(localPoints, rect);\n  };\n}\nexport function makeLinearBrushOtherExtent(rect, specifiedXYIndex) {\n  rect = normalizeRect(rect);\n  return function (xyIndex) {\n    var idx = specifiedXYIndex != null ? specifiedXYIndex : xyIndex;\n    var brushWidth = idx ? rect.width : rect.height;\n    var base = idx ? rect.x : rect.y;\n    return [base, base + (brushWidth || 0)];\n  };\n}\nexport function makeRectIsTargetByCursor(rect, api, targetModel) {\n  var boundingRect = normalizeRect(rect);\n  return function (e, localCursorPoint) {\n    return boundingRect.contain(localCursorPoint[0], localCursorPoint[1]) && !onIrrelevantElement(e, api, targetModel);\n  };\n}\n// Consider width/height is negative.\nfunction normalizeRect(rect) {\n  return BoundingRect.create(rect);\n}", "map": {"version": 3, "names": ["BoundingRect", "onIrrelevantElement", "graphicUtil", "makeRectPanelClipPath", "rect", "normalizeRect", "localPoints", "clipPointsByRect", "makeLinearBrushOtherExtent", "specifiedXYIndex", "xyIndex", "idx", "brushWidth", "width", "height", "base", "x", "y", "makeRectIsTargetByCursor", "api", "targetModel", "boundingRect", "e", "localCursorPoint", "contain", "create"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/helper/brushHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { onIrrelevantElement } from './cursorHelper.js';\nimport * as graphicUtil from '../../util/graphic.js';\nexport function makeRectPanelClipPath(rect) {\n  rect = normalizeRect(rect);\n  return function (localPoints) {\n    return graphicUtil.clipPointsByRect(localPoints, rect);\n  };\n}\nexport function makeLinearBrushOtherExtent(rect, specifiedXYIndex) {\n  rect = normalizeRect(rect);\n  return function (xyIndex) {\n    var idx = specifiedXYIndex != null ? specifiedXYIndex : xyIndex;\n    var brushWidth = idx ? rect.width : rect.height;\n    var base = idx ? rect.x : rect.y;\n    return [base, base + (brushWidth || 0)];\n  };\n}\nexport function makeRectIsTargetByCursor(rect, api, targetModel) {\n  var boundingRect = normalizeRect(rect);\n  return function (e, localCursorPoint) {\n    return boundingRect.contain(localCursorPoint[0], localCursorPoint[1]) && !onIrrelevantElement(e, api, targetModel);\n  };\n}\n// Consider width/height is negative.\nfunction normalizeRect(rect) {\n  return BoundingRect.create(rect);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,YAAY,MAAM,kCAAkC;AAC3D,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,WAAW,MAAM,uBAAuB;AACpD,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1CA,IAAI,GAAGC,aAAa,CAACD,IAAI,CAAC;EAC1B,OAAO,UAAUE,WAAW,EAAE;IAC5B,OAAOJ,WAAW,CAACK,gBAAgB,CAACD,WAAW,EAAEF,IAAI,CAAC;EACxD,CAAC;AACH;AACA,OAAO,SAASI,0BAA0BA,CAACJ,IAAI,EAAEK,gBAAgB,EAAE;EACjEL,IAAI,GAAGC,aAAa,CAACD,IAAI,CAAC;EAC1B,OAAO,UAAUM,OAAO,EAAE;IACxB,IAAIC,GAAG,GAAGF,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAGC,OAAO;IAC/D,IAAIE,UAAU,GAAGD,GAAG,GAAGP,IAAI,CAACS,KAAK,GAAGT,IAAI,CAACU,MAAM;IAC/C,IAAIC,IAAI,GAAGJ,GAAG,GAAGP,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACa,CAAC;IAChC,OAAO,CAACF,IAAI,EAAEA,IAAI,IAAIH,UAAU,IAAI,CAAC,CAAC,CAAC;EACzC,CAAC;AACH;AACA,OAAO,SAASM,wBAAwBA,CAACd,IAAI,EAAEe,GAAG,EAAEC,WAAW,EAAE;EAC/D,IAAIC,YAAY,GAAGhB,aAAa,CAACD,IAAI,CAAC;EACtC,OAAO,UAAUkB,CAAC,EAAEC,gBAAgB,EAAE;IACpC,OAAOF,YAAY,CAACG,OAAO,CAACD,gBAAgB,CAAC,CAAC,CAAC,EAAEA,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAACtB,mBAAmB,CAACqB,CAAC,EAAEH,GAAG,EAAEC,WAAW,CAAC;EACpH,CAAC;AACH;AACA;AACA,SAASf,aAAaA,CAACD,IAAI,EAAE;EAC3B,OAAOJ,YAAY,CAACyB,MAAM,CAACrB,IAAI,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}