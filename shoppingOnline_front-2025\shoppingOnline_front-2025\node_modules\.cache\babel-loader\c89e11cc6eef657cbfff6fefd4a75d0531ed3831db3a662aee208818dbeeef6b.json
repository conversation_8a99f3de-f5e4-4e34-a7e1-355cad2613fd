{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../../util/model.js';\n/**\n * @caveat Do not import other `src/component/thumbnail/*` files.\n *  This file should be decoupled from them for sake of the size consideration.\n */\n/**\n * FIXME: This is a temporary implmentation. May need refactor to decouple\n *  the direct call from series.graph to thumbnail.\n */\nvar inner = makeInner();\nexport function getThumbnailBridge(model) {\n  if (model) {\n    return inner(model).bridge;\n  }\n}\nexport function injectThumbnailBridge(model, thumbnailBridge) {\n  if (model) {\n    inner(model).bridge = thumbnailBridge;\n  }\n}\n;", "map": {"version": 3, "names": ["makeInner", "inner", "get<PERSON><PERSON><PERSON>nailBridge", "model", "bridge", "injectThumbnailBridge", "thumbnail<PERSON><PERSON>"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/helper/thumbnailBridge.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { makeInner } from '../../util/model.js';\n/**\n * @caveat Do not import other `src/component/thumbnail/*` files.\n *  This file should be decoupled from them for sake of the size consideration.\n */\n/**\n * FIXME: This is a temporary implmentation. May need refactor to decouple\n *  the direct call from series.graph to thumbnail.\n */\nvar inner = makeInner();\nexport function getThumbnailBridge(model) {\n  if (model) {\n    return inner(model).bridge;\n  }\n}\nexport function injectThumbnailBridge(model, thumbnailBridge) {\n  if (model) {\n    inner(model).bridge = thumbnailBridge;\n  }\n}\n;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,OAAO,SAASE,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAIA,KAAK,EAAE;IACT,OAAOF,KAAK,CAACE,KAAK,CAAC,CAACC,MAAM;EAC5B;AACF;AACA,OAAO,SAASC,qBAAqBA,CAACF,KAAK,EAAEG,eAAe,EAAE;EAC5D,IAAIH,KAAK,EAAE;IACTF,KAAK,CAACE,KAAK,CAAC,CAACC,MAAM,GAAGE,eAAe;EACvC;AACF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}