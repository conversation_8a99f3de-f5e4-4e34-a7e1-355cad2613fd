{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport AxisBuilder from '../../component/axis/AxisBuilder.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\n/**\n * [__CAUTION__]\n *  MUST guarantee: if only the input `rect` and `axis.extent` changed,\n *  only `layout.position` changes.\n *  This character is replied on `grid.contain` calculation in `AxisBuilder`.\n *  @see updateCartesianAxisViewCommonPartBuilder\n *\n * Can only be called after coordinate system creation stage.\n * (Can be called before coordinate system update stage).\n */\nexport function layout(rect, axisModel, opt) {\n  opt = opt || {};\n  var axis = axisModel.axis;\n  var layout = {};\n  var otherAxisOnZeroOf = axis.getAxesOnZeroOf()[0];\n  var rawAxisPosition = axis.position;\n  var axisPosition = otherAxisOnZeroOf ? 'onZero' : rawAxisPosition;\n  var axisDim = axis.dim;\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var idx = {\n    left: 0,\n    right: 1,\n    top: 0,\n    bottom: 1,\n    onZero: 2\n  };\n  var axisOffset = axisModel.get('offset') || 0;\n  var posBound = axisDim === 'x' ? [rectBound[2] - axisOffset, rectBound[3] + axisOffset] : [rectBound[0] - axisOffset, rectBound[1] + axisOffset];\n  if (otherAxisOnZeroOf) {\n    var onZeroCoord = otherAxisOnZeroOf.toGlobalCoord(otherAxisOnZeroOf.dataToCoord(0));\n    posBound[idx.onZero] = Math.max(Math.min(onZeroCoord, posBound[1]), posBound[0]);\n  }\n  // Axis position\n  layout.position = [axisDim === 'y' ? posBound[idx[axisPosition]] : rectBound[0], axisDim === 'x' ? posBound[idx[axisPosition]] : rectBound[3]];\n  // Axis rotation\n  layout.rotation = Math.PI / 2 * (axisDim === 'x' ? 0 : 1);\n  // Tick and label direction, x y is axisDim\n  var dirMap = {\n    top: -1,\n    bottom: 1,\n    left: -1,\n    right: 1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = dirMap[rawAxisPosition];\n  layout.labelOffset = otherAxisOnZeroOf ? posBound[idx[rawAxisPosition]] - posBound[idx.onZero] : 0;\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  // Special label rotation\n  var labelRotate = axisModel.get(['axisLabel', 'rotate']);\n  layout.labelRotate = axisPosition === 'top' ? -labelRotate : labelRotate;\n  // Over splitLine and splitArea\n  layout.z2 = 1;\n  return layout;\n}\nexport function isCartesian2DDeclaredSeries(seriesModel) {\n  return seriesModel.get('coordinateSystem') === 'cartesian2d';\n}\n/**\n * Note: If pie (or other similar series) use cartesian2d, here\n *  option `seriesModel.get('coordinateSystem') === 'cartesian2d'`\n *  and `seriesModel.coordinateSystem !== cartesian2dCoordSysInstance`\n *  and `seriesModel.boxCoordinateSystem === cartesian2dCoordSysInstance`,\n *  the logic below is probably wrong, therefore skip it temporarily.\n */\nexport function isCartesian2DInjectedAsDataCoordSys(seriesModel) {\n  return seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'cartesian2d';\n}\nexport function findAxisModels(seriesModel) {\n  var axisModelMap = {\n    xAxisModel: null,\n    yAxisModel: null\n  };\n  zrUtil.each(axisModelMap, function (v, key) {\n    var axisType = key.replace(/Model$/, '');\n    var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!axisModel) {\n        throw new Error(axisType + ' \"' + zrUtil.retrieve3(seriesModel.get(axisType + 'Index'), seriesModel.get(axisType + 'Id'), 0) + '\" not found');\n      }\n    }\n    axisModelMap[key] = axisModel;\n  });\n  return axisModelMap;\n}\nexport function createCartesianAxisViewCommonPartBuilder(gridRect, cartesians, axisModel, api, ctx, defaultNameMoveOverlap) {\n  var layoutResult = layout(gridRect, axisModel);\n  var axisLineAutoShow = false;\n  var axisTickAutoShow = false;\n  // Not show axisTick or axisLine if other axis is category / time\n  for (var i = 0; i < cartesians.length; i++) {\n    if (isIntervalOrLogScale(cartesians[i].getOtherAxis(axisModel.axis).scale)) {\n      // Still show axis tick or axisLine if other axis is value / log\n      axisLineAutoShow = axisTickAutoShow = true;\n      if (axisModel.axis.type === 'category' && axisModel.axis.onBand) {\n        axisTickAutoShow = false;\n      }\n    }\n  }\n  layoutResult.axisLineAutoShow = axisLineAutoShow;\n  layoutResult.axisTickAutoShow = axisTickAutoShow;\n  layoutResult.defaultNameMoveOverlap = defaultNameMoveOverlap;\n  return new AxisBuilder(axisModel, api, layoutResult, ctx);\n}\nexport function updateCartesianAxisViewCommonPartBuilder(axisBuilder, gridRect, axisModel) {\n  var newRaw = layout(gridRect, axisModel);\n  if (process.env.NODE_ENV !== 'production') {\n    var oldRaw_1 = axisBuilder.__getRawCfg();\n    zrUtil.each(zrUtil.keys(newRaw), function (prop) {\n      if (prop !== 'position' && prop !== 'labelOffset') {\n        zrUtil.assert(newRaw[prop] === oldRaw_1[prop]);\n      }\n    });\n  }\n  axisBuilder.updateCfg(newRaw);\n}", "map": {"version": 3, "names": ["zrUtil", "SINGLE_REFERRING", "AxisBuilder", "isIntervalOrLogScale", "layout", "rect", "axisModel", "opt", "axis", "otherAxisOnZeroOf", "getAxesOnZeroOf", "rawAxisPosition", "position", "axisPosition", "axisDim", "dim", "rectBound", "x", "width", "y", "height", "idx", "left", "right", "top", "bottom", "onZero", "axisOffset", "get", "posBound", "onZeroCoord", "toGlobalCoord", "dataToCoord", "Math", "max", "min", "rotation", "PI", "dirMap", "labelDirection", "tickDirection", "nameDirection", "labelOffset", "retrieve", "labelInside", "labelRotate", "z2", "isCartesian2DDeclaredSeries", "seriesModel", "isCartesian2DInjectedAsDataCoordSys", "coordinateSystem", "type", "findAxisModels", "axisModelMap", "xAxisModel", "yAxisModel", "each", "v", "key", "axisType", "replace", "getReferringComponents", "models", "process", "env", "NODE_ENV", "Error", "retrieve3", "createCartesianAxisViewCommonPartBuilder", "gridRect", "cartesians", "api", "ctx", "defaultNameMoveOverlap", "layoutResult", "axisLineAutoShow", "axisTickAutoShow", "i", "length", "getOtherAxis", "scale", "onBand", "updateCartesianAxisViewCommonPartBuilder", "axisBuilder", "newRaw", "oldRaw_1", "__getRawCfg", "keys", "prop", "assert", "updateCfg"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/cartesian/cartesianAxisHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nimport AxisBuilder from '../../component/axis/AxisBuilder.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\n/**\n * [__CAUTION__]\n *  MUST guarantee: if only the input `rect` and `axis.extent` changed,\n *  only `layout.position` changes.\n *  This character is replied on `grid.contain` calculation in `AxisBuilder`.\n *  @see updateCartesianAxisViewCommonPartBuilder\n *\n * Can only be called after coordinate system creation stage.\n * (Can be called before coordinate system update stage).\n */\nexport function layout(rect, axisModel, opt) {\n  opt = opt || {};\n  var axis = axisModel.axis;\n  var layout = {};\n  var otherAxisOnZeroOf = axis.getAxesOnZeroOf()[0];\n  var rawAxisPosition = axis.position;\n  var axisPosition = otherAxisOnZeroOf ? 'onZero' : rawAxisPosition;\n  var axisDim = axis.dim;\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var idx = {\n    left: 0,\n    right: 1,\n    top: 0,\n    bottom: 1,\n    onZero: 2\n  };\n  var axisOffset = axisModel.get('offset') || 0;\n  var posBound = axisDim === 'x' ? [rectBound[2] - axisOffset, rectBound[3] + axisOffset] : [rectBound[0] - axisOffset, rectBound[1] + axisOffset];\n  if (otherAxisOnZeroOf) {\n    var onZeroCoord = otherAxisOnZeroOf.toGlobalCoord(otherAxisOnZeroOf.dataToCoord(0));\n    posBound[idx.onZero] = Math.max(Math.min(onZeroCoord, posBound[1]), posBound[0]);\n  }\n  // Axis position\n  layout.position = [axisDim === 'y' ? posBound[idx[axisPosition]] : rectBound[0], axisDim === 'x' ? posBound[idx[axisPosition]] : rectBound[3]];\n  // Axis rotation\n  layout.rotation = Math.PI / 2 * (axisDim === 'x' ? 0 : 1);\n  // Tick and label direction, x y is axisDim\n  var dirMap = {\n    top: -1,\n    bottom: 1,\n    left: -1,\n    right: 1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = dirMap[rawAxisPosition];\n  layout.labelOffset = otherAxisOnZeroOf ? posBound[idx[rawAxisPosition]] - posBound[idx.onZero] : 0;\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  // Special label rotation\n  var labelRotate = axisModel.get(['axisLabel', 'rotate']);\n  layout.labelRotate = axisPosition === 'top' ? -labelRotate : labelRotate;\n  // Over splitLine and splitArea\n  layout.z2 = 1;\n  return layout;\n}\nexport function isCartesian2DDeclaredSeries(seriesModel) {\n  return seriesModel.get('coordinateSystem') === 'cartesian2d';\n}\n/**\n * Note: If pie (or other similar series) use cartesian2d, here\n *  option `seriesModel.get('coordinateSystem') === 'cartesian2d'`\n *  and `seriesModel.coordinateSystem !== cartesian2dCoordSysInstance`\n *  and `seriesModel.boxCoordinateSystem === cartesian2dCoordSysInstance`,\n *  the logic below is probably wrong, therefore skip it temporarily.\n */\nexport function isCartesian2DInjectedAsDataCoordSys(seriesModel) {\n  return seriesModel.coordinateSystem && seriesModel.coordinateSystem.type === 'cartesian2d';\n}\nexport function findAxisModels(seriesModel) {\n  var axisModelMap = {\n    xAxisModel: null,\n    yAxisModel: null\n  };\n  zrUtil.each(axisModelMap, function (v, key) {\n    var axisType = key.replace(/Model$/, '');\n    var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!axisModel) {\n        throw new Error(axisType + ' \"' + zrUtil.retrieve3(seriesModel.get(axisType + 'Index'), seriesModel.get(axisType + 'Id'), 0) + '\" not found');\n      }\n    }\n    axisModelMap[key] = axisModel;\n  });\n  return axisModelMap;\n}\nexport function createCartesianAxisViewCommonPartBuilder(gridRect, cartesians, axisModel, api, ctx, defaultNameMoveOverlap) {\n  var layoutResult = layout(gridRect, axisModel);\n  var axisLineAutoShow = false;\n  var axisTickAutoShow = false;\n  // Not show axisTick or axisLine if other axis is category / time\n  for (var i = 0; i < cartesians.length; i++) {\n    if (isIntervalOrLogScale(cartesians[i].getOtherAxis(axisModel.axis).scale)) {\n      // Still show axis tick or axisLine if other axis is value / log\n      axisLineAutoShow = axisTickAutoShow = true;\n      if (axisModel.axis.type === 'category' && axisModel.axis.onBand) {\n        axisTickAutoShow = false;\n      }\n    }\n  }\n  layoutResult.axisLineAutoShow = axisLineAutoShow;\n  layoutResult.axisTickAutoShow = axisTickAutoShow;\n  layoutResult.defaultNameMoveOverlap = defaultNameMoveOverlap;\n  return new AxisBuilder(axisModel, api, layoutResult, ctx);\n}\nexport function updateCartesianAxisViewCommonPartBuilder(axisBuilder, gridRect, axisModel) {\n  var newRaw = layout(gridRect, axisModel);\n  if (process.env.NODE_ENV !== 'production') {\n    var oldRaw_1 = axisBuilder.__getRawCfg();\n    zrUtil.each(zrUtil.keys(newRaw), function (prop) {\n      if (prop !== 'position' && prop !== 'labelOffset') {\n        zrUtil.assert(newRaw[prop] === oldRaw_1[prop]);\n      }\n    });\n  }\n  axisBuilder.updateCfg(newRaw);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAE;EAC3CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAIK,iBAAiB,GAAGD,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,IAAIC,eAAe,GAAGH,IAAI,CAACI,QAAQ;EACnC,IAAIC,YAAY,GAAGJ,iBAAiB,GAAG,QAAQ,GAAGE,eAAe;EACjE,IAAIG,OAAO,GAAGN,IAAI,CAACO,GAAG;EACtB,IAAIC,SAAS,GAAG,CAACX,IAAI,CAACY,CAAC,EAAEZ,IAAI,CAACY,CAAC,GAAGZ,IAAI,CAACa,KAAK,EAAEb,IAAI,CAACc,CAAC,EAAEd,IAAI,CAACc,CAAC,GAAGd,IAAI,CAACe,MAAM,CAAC;EAC3E,IAAIC,GAAG,GAAG;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACD,IAAIC,UAAU,GAAGrB,SAAS,CAACsB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;EAC7C,IAAIC,QAAQ,GAAGf,OAAO,KAAK,GAAG,GAAG,CAACE,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,EAAEX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,CAAC,GAAG,CAACX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,EAAEX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,CAAC;EAChJ,IAAIlB,iBAAiB,EAAE;IACrB,IAAIqB,WAAW,GAAGrB,iBAAiB,CAACsB,aAAa,CAACtB,iBAAiB,CAACuB,WAAW,CAAC,CAAC,CAAC,CAAC;IACnFH,QAAQ,CAACR,GAAG,CAACK,MAAM,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,WAAW,EAAED,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClF;EACA;EACAzB,MAAM,CAACQ,QAAQ,GAAG,CAACE,OAAO,KAAK,GAAG,GAAGe,QAAQ,CAACR,GAAG,CAACR,YAAY,CAAC,CAAC,GAAGG,SAAS,CAAC,CAAC,CAAC,EAAEF,OAAO,KAAK,GAAG,GAAGe,QAAQ,CAACR,GAAG,CAACR,YAAY,CAAC,CAAC,GAAGG,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9I;EACAZ,MAAM,CAACgC,QAAQ,GAAGH,IAAI,CAACI,EAAE,GAAG,CAAC,IAAIvB,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EACzD;EACA,IAAIwB,MAAM,GAAG;IACXd,GAAG,EAAE,CAAC,CAAC;IACPC,MAAM,EAAE,CAAC;IACTH,IAAI,EAAE,CAAC,CAAC;IACRC,KAAK,EAAE;EACT,CAAC;EACDnB,MAAM,CAACmC,cAAc,GAAGnC,MAAM,CAACoC,aAAa,GAAGpC,MAAM,CAACqC,aAAa,GAAGH,MAAM,CAAC3B,eAAe,CAAC;EAC7FP,MAAM,CAACsC,WAAW,GAAGjC,iBAAiB,GAAGoB,QAAQ,CAACR,GAAG,CAACV,eAAe,CAAC,CAAC,GAAGkB,QAAQ,CAACR,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC;EAClG,IAAIpB,SAAS,CAACsB,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,EAAE;IACzCxB,MAAM,CAACoC,aAAa,GAAG,CAACpC,MAAM,CAACoC,aAAa;EAC9C;EACA,IAAIxC,MAAM,CAAC2C,QAAQ,CAACpC,GAAG,CAACqC,WAAW,EAAEtC,SAAS,CAACsB,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE;IAC5ExB,MAAM,CAACmC,cAAc,GAAG,CAACnC,MAAM,CAACmC,cAAc;EAChD;EACA;EACA,IAAIM,WAAW,GAAGvC,SAAS,CAACsB,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;EACxDxB,MAAM,CAACyC,WAAW,GAAGhC,YAAY,KAAK,KAAK,GAAG,CAACgC,WAAW,GAAGA,WAAW;EACxE;EACAzC,MAAM,CAAC0C,EAAE,GAAG,CAAC;EACb,OAAO1C,MAAM;AACf;AACA,OAAO,SAAS2C,2BAA2BA,CAACC,WAAW,EAAE;EACvD,OAAOA,WAAW,CAACpB,GAAG,CAAC,kBAAkB,CAAC,KAAK,aAAa;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASqB,mCAAmCA,CAACD,WAAW,EAAE;EAC/D,OAAOA,WAAW,CAACE,gBAAgB,IAAIF,WAAW,CAACE,gBAAgB,CAACC,IAAI,KAAK,aAAa;AAC5F;AACA,OAAO,SAASC,cAAcA,CAACJ,WAAW,EAAE;EAC1C,IAAIK,YAAY,GAAG;IACjBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC;EACDvD,MAAM,CAACwD,IAAI,CAACH,YAAY,EAAE,UAAUI,CAAC,EAAEC,GAAG,EAAE;IAC1C,IAAIC,QAAQ,GAAGD,GAAG,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IACxC,IAAItD,SAAS,GAAG0C,WAAW,CAACa,sBAAsB,CAACF,QAAQ,EAAE1D,gBAAgB,CAAC,CAAC6D,MAAM,CAAC,CAAC,CAAC;IACxF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAC3D,SAAS,EAAE;QACd,MAAM,IAAI4D,KAAK,CAACP,QAAQ,GAAG,IAAI,GAAG3D,MAAM,CAACmE,SAAS,CAACnB,WAAW,CAACpB,GAAG,CAAC+B,QAAQ,GAAG,OAAO,CAAC,EAAEX,WAAW,CAACpB,GAAG,CAAC+B,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;MAC/I;IACF;IACAN,YAAY,CAACK,GAAG,CAAC,GAAGpD,SAAS;EAC/B,CAAC,CAAC;EACF,OAAO+C,YAAY;AACrB;AACA,OAAO,SAASe,wCAAwCA,CAACC,QAAQ,EAAEC,UAAU,EAAEhE,SAAS,EAAEiE,GAAG,EAAEC,GAAG,EAAEC,sBAAsB,EAAE;EAC1H,IAAIC,YAAY,GAAGtE,MAAM,CAACiE,QAAQ,EAAE/D,SAAS,CAAC;EAC9C,IAAIqE,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,gBAAgB,GAAG,KAAK;EAC5B;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,UAAU,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAI1E,oBAAoB,CAACmE,UAAU,CAACO,CAAC,CAAC,CAACE,YAAY,CAACzE,SAAS,CAACE,IAAI,CAAC,CAACwE,KAAK,CAAC,EAAE;MAC1E;MACAL,gBAAgB,GAAGC,gBAAgB,GAAG,IAAI;MAC1C,IAAItE,SAAS,CAACE,IAAI,CAAC2C,IAAI,KAAK,UAAU,IAAI7C,SAAS,CAACE,IAAI,CAACyE,MAAM,EAAE;QAC/DL,gBAAgB,GAAG,KAAK;MAC1B;IACF;EACF;EACAF,YAAY,CAACC,gBAAgB,GAAGA,gBAAgB;EAChDD,YAAY,CAACE,gBAAgB,GAAGA,gBAAgB;EAChDF,YAAY,CAACD,sBAAsB,GAAGA,sBAAsB;EAC5D,OAAO,IAAIvE,WAAW,CAACI,SAAS,EAAEiE,GAAG,EAAEG,YAAY,EAAEF,GAAG,CAAC;AAC3D;AACA,OAAO,SAASU,wCAAwCA,CAACC,WAAW,EAAEd,QAAQ,EAAE/D,SAAS,EAAE;EACzF,IAAI8E,MAAM,GAAGhF,MAAM,CAACiE,QAAQ,EAAE/D,SAAS,CAAC;EACxC,IAAIyD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIoB,QAAQ,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;IACxCtF,MAAM,CAACwD,IAAI,CAACxD,MAAM,CAACuF,IAAI,CAACH,MAAM,CAAC,EAAE,UAAUI,IAAI,EAAE;MAC/C,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,aAAa,EAAE;QACjDxF,MAAM,CAACyF,MAAM,CAACL,MAAM,CAACI,IAAI,CAAC,KAAKH,QAAQ,CAACG,IAAI,CAAC,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;EACAL,WAAW,CAACO,SAAS,CAACN,MAAM,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}