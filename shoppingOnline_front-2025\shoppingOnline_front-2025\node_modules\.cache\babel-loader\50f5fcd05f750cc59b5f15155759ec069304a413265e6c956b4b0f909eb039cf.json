{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { extend, retrieve3 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { getLabelStatesModels, setLabelStyle } from '../../label/labelStyle.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { getECData } from '../../util/innerStore.js';\nvar ChordPiece = /** @class */function (_super) {\n  __extends(ChordPiece, _super);\n  function ChordPiece(data, idx, startAngle) {\n    var _this = _super.call(this) || this;\n    getECData(_this).dataType = 'node';\n    _this.z2 = 2;\n    var text = new graphic.Text();\n    _this.setTextContent(text);\n    _this.updateData(data, idx, startAngle, true);\n    return _this;\n  }\n  ChordPiece.prototype.updateData = function (data, idx, startAngle, firstCreate) {\n    var sector = this;\n    var node = data.graph.getNodeByIndex(idx);\n    var seriesModel = data.hostModel;\n    var itemModel = node.getModel();\n    var emphasisModel = itemModel.getModel('emphasis');\n    // layout position is the center of the sector\n    var layout = data.getItemLayout(idx);\n    var shape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    var el = this;\n    // Ignore NaN data.\n    if (isNaN(shape.startAngle)) {\n      // Use NaN shape to avoid drawing shape.\n      el.setShape(shape);\n      return;\n    }\n    if (firstCreate) {\n      el.setShape(shape);\n    } else {\n      graphic.updateProps(el, {\n        shape: shape\n      }, seriesModel, idx);\n    }\n    var sectorShape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    sector.setShape(sectorShape);\n    sector.useStyle(data.getItemVisual(idx, 'style'));\n    setStatesStylesFromModel(sector, itemModel);\n    this._updateLabel(seriesModel, itemModel, node);\n    data.setItemGraphicEl(idx, el);\n    setStatesStylesFromModel(el, itemModel, 'itemStyle');\n    // Add focus/blur states handling\n    var focus = emphasisModel.get('focus');\n    toggleHoverEmphasis(this, focus === 'adjacency' ? node.getAdjacentDataIndices() : focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  ChordPiece.prototype._updateLabel = function (seriesModel, itemModel, node) {\n    var label = this.getTextContent();\n    var layout = node.getLayout();\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var dx = Math.cos(midAngle);\n    var dy = Math.sin(midAngle);\n    var normalLabelModel = itemModel.getModel('label');\n    label.ignore = !normalLabelModel.get('show');\n    // Set label style\n    var labelStateModels = getLabelStatesModels(itemModel);\n    var style = node.getVisual('style');\n    setLabelStyle(label, labelStateModels, {\n      labelFetcher: {\n        getFormattedLabel: function (dataIndex, stateName, dataType, labelDimIndex, formatter, extendParams) {\n          return seriesModel.getFormattedLabel(dataIndex, stateName, 'node', labelDimIndex,\n          // ensure edgeLabel formatter is provided\n          // to prevent the inheritance from `label.formatter` of the series\n          retrieve3(formatter, labelStateModels.normal && labelStateModels.normal.get('formatter'), itemModel.get('name')), extendParams);\n        }\n      },\n      labelDataIndex: node.dataIndex,\n      defaultText: node.dataIndex + '',\n      inheritColor: style.fill,\n      defaultOpacity: style.opacity,\n      defaultOutsidePosition: 'startArc'\n    });\n    // Set label position\n    var labelPosition = normalLabelModel.get('position') || 'outside';\n    var labelPadding = normalLabelModel.get('distance') || 0;\n    var r;\n    if (labelPosition === 'outside') {\n      r = layout.r + labelPadding;\n    } else {\n      r = (layout.r + layout.r0) / 2;\n    }\n    this.textConfig = {\n      inside: labelPosition !== 'outside'\n    };\n    var align = labelPosition !== 'outside' ? normalLabelModel.get('align') || 'center' : dx > 0 ? 'left' : 'right';\n    var verticalAlign = labelPosition !== 'outside' ? normalLabelModel.get('verticalAlign') || 'middle' : dy > 0 ? 'top' : 'bottom';\n    label.attr({\n      x: dx * r + layout.cx,\n      y: dy * r + layout.cy,\n      rotation: 0,\n      style: {\n        align: align,\n        verticalAlign: verticalAlign\n      }\n    });\n  };\n  return ChordPiece;\n}(graphic.Sector);\nexport default ChordPiece;", "map": {"version": 3, "names": ["__extends", "extend", "retrieve3", "graphic", "getSectorCornerRadius", "getLabelStatesModels", "setLabelStyle", "setStatesStylesFromModel", "toggleHoverEmphasis", "getECData", "Chord<PERSON><PERSON>ce", "_super", "data", "idx", "startAngle", "_this", "call", "dataType", "z2", "text", "Text", "setTextContent", "updateData", "prototype", "firstCreate", "sector", "node", "graph", "getNodeByIndex", "seriesModel", "hostModel", "itemModel", "getModel", "emphasisModel", "layout", "getItemLayout", "shape", "el", "isNaN", "setShape", "updateProps", "sectorShape", "useStyle", "getItemVisual", "_updateLabel", "setItemGraphicEl", "focus", "get", "getAdjacentDataIndices", "label", "getTextContent", "getLayout", "midAngle", "endAngle", "dx", "Math", "cos", "dy", "sin", "normalLabelModel", "ignore", "labelStateModels", "style", "getVisual", "labelFetcher", "getFormattedLabel", "dataIndex", "stateName", "labelDimIndex", "formatter", "extendParams", "normal", "labelDataIndex", "defaultText", "inheritColor", "fill", "defaultOpacity", "opacity", "defaultOutsidePosition", "labelPosition", "labelPadding", "r", "r0", "textConfig", "inside", "align", "verticalAlign", "attr", "x", "cx", "y", "cy", "rotation", "Sector"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/chart/chord/ChordPiece.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { extend, retrieve3 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nimport { getLabelStatesModels, setLabelStyle } from '../../label/labelStyle.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { getECData } from '../../util/innerStore.js';\nvar ChordPiece = /** @class */function (_super) {\n  __extends(ChordPiece, _super);\n  function ChordPiece(data, idx, startAngle) {\n    var _this = _super.call(this) || this;\n    getECData(_this).dataType = 'node';\n    _this.z2 = 2;\n    var text = new graphic.Text();\n    _this.setTextContent(text);\n    _this.updateData(data, idx, startAngle, true);\n    return _this;\n  }\n  ChordPiece.prototype.updateData = function (data, idx, startAngle, firstCreate) {\n    var sector = this;\n    var node = data.graph.getNodeByIndex(idx);\n    var seriesModel = data.hostModel;\n    var itemModel = node.getModel();\n    var emphasisModel = itemModel.getModel('emphasis');\n    // layout position is the center of the sector\n    var layout = data.getItemLayout(idx);\n    var shape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    var el = this;\n    // Ignore NaN data.\n    if (isNaN(shape.startAngle)) {\n      // Use NaN shape to avoid drawing shape.\n      el.setShape(shape);\n      return;\n    }\n    if (firstCreate) {\n      el.setShape(shape);\n    } else {\n      graphic.updateProps(el, {\n        shape: shape\n      }, seriesModel, idx);\n    }\n    var sectorShape = extend(getSectorCornerRadius(itemModel.getModel('itemStyle'), layout, true), layout);\n    sector.setShape(sectorShape);\n    sector.useStyle(data.getItemVisual(idx, 'style'));\n    setStatesStylesFromModel(sector, itemModel);\n    this._updateLabel(seriesModel, itemModel, node);\n    data.setItemGraphicEl(idx, el);\n    setStatesStylesFromModel(el, itemModel, 'itemStyle');\n    // Add focus/blur states handling\n    var focus = emphasisModel.get('focus');\n    toggleHoverEmphasis(this, focus === 'adjacency' ? node.getAdjacentDataIndices() : focus, emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  ChordPiece.prototype._updateLabel = function (seriesModel, itemModel, node) {\n    var label = this.getTextContent();\n    var layout = node.getLayout();\n    var midAngle = (layout.startAngle + layout.endAngle) / 2;\n    var dx = Math.cos(midAngle);\n    var dy = Math.sin(midAngle);\n    var normalLabelModel = itemModel.getModel('label');\n    label.ignore = !normalLabelModel.get('show');\n    // Set label style\n    var labelStateModels = getLabelStatesModels(itemModel);\n    var style = node.getVisual('style');\n    setLabelStyle(label, labelStateModels, {\n      labelFetcher: {\n        getFormattedLabel: function (dataIndex, stateName, dataType, labelDimIndex, formatter, extendParams) {\n          return seriesModel.getFormattedLabel(dataIndex, stateName, 'node', labelDimIndex,\n          // ensure edgeLabel formatter is provided\n          // to prevent the inheritance from `label.formatter` of the series\n          retrieve3(formatter, labelStateModels.normal && labelStateModels.normal.get('formatter'), itemModel.get('name')), extendParams);\n        }\n      },\n      labelDataIndex: node.dataIndex,\n      defaultText: node.dataIndex + '',\n      inheritColor: style.fill,\n      defaultOpacity: style.opacity,\n      defaultOutsidePosition: 'startArc'\n    });\n    // Set label position\n    var labelPosition = normalLabelModel.get('position') || 'outside';\n    var labelPadding = normalLabelModel.get('distance') || 0;\n    var r;\n    if (labelPosition === 'outside') {\n      r = layout.r + labelPadding;\n    } else {\n      r = (layout.r + layout.r0) / 2;\n    }\n    this.textConfig = {\n      inside: labelPosition !== 'outside'\n    };\n    var align = labelPosition !== 'outside' ? normalLabelModel.get('align') || 'center' : dx > 0 ? 'left' : 'right';\n    var verticalAlign = labelPosition !== 'outside' ? normalLabelModel.get('verticalAlign') || 'middle' : dy > 0 ? 'top' : 'bottom';\n    label.attr({\n      x: dx * r + layout.cx,\n      y: dy * r + layout.cy,\n      rotation: 0,\n      style: {\n        align: align,\n        verticalAlign: verticalAlign\n      }\n    });\n  };\n  return ChordPiece;\n}(graphic.Sector);\nexport default ChordPiece;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,MAAM,EAAEC,SAAS,QAAQ,0BAA0B;AAC5D,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,EAAEC,aAAa,QAAQ,2BAA2B;AAC/E,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CX,SAAS,CAACU,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACE,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAE;IACzC,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCP,SAAS,CAACM,KAAK,CAAC,CAACE,QAAQ,GAAG,MAAM;IAClCF,KAAK,CAACG,EAAE,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,IAAIhB,OAAO,CAACiB,IAAI,CAAC,CAAC;IAC7BL,KAAK,CAACM,cAAc,CAACF,IAAI,CAAC;IAC1BJ,KAAK,CAACO,UAAU,CAACV,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAE,IAAI,CAAC;IAC7C,OAAOC,KAAK;EACd;EACAL,UAAU,CAACa,SAAS,CAACD,UAAU,GAAG,UAAUV,IAAI,EAAEC,GAAG,EAAEC,UAAU,EAAEU,WAAW,EAAE;IAC9E,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIC,IAAI,GAAGd,IAAI,CAACe,KAAK,CAACC,cAAc,CAACf,GAAG,CAAC;IACzC,IAAIgB,WAAW,GAAGjB,IAAI,CAACkB,SAAS;IAChC,IAAIC,SAAS,GAAGL,IAAI,CAACM,QAAQ,CAAC,CAAC;IAC/B,IAAIC,aAAa,GAAGF,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC;IAClD;IACA,IAAIE,MAAM,GAAGtB,IAAI,CAACuB,aAAa,CAACtB,GAAG,CAAC;IACpC,IAAIuB,KAAK,GAAGnC,MAAM,CAACG,qBAAqB,CAAC2B,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAEE,MAAM,EAAE,IAAI,CAAC,EAAEA,MAAM,CAAC;IAChG,IAAIG,EAAE,GAAG,IAAI;IACb;IACA,IAAIC,KAAK,CAACF,KAAK,CAACtB,UAAU,CAAC,EAAE;MAC3B;MACAuB,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC;MAClB;IACF;IACA,IAAIZ,WAAW,EAAE;MACfa,EAAE,CAACE,QAAQ,CAACH,KAAK,CAAC;IACpB,CAAC,MAAM;MACLjC,OAAO,CAACqC,WAAW,CAACH,EAAE,EAAE;QACtBD,KAAK,EAAEA;MACT,CAAC,EAAEP,WAAW,EAAEhB,GAAG,CAAC;IACtB;IACA,IAAI4B,WAAW,GAAGxC,MAAM,CAACG,qBAAqB,CAAC2B,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAEE,MAAM,EAAE,IAAI,CAAC,EAAEA,MAAM,CAAC;IACtGT,MAAM,CAACc,QAAQ,CAACE,WAAW,CAAC;IAC5BhB,MAAM,CAACiB,QAAQ,CAAC9B,IAAI,CAAC+B,aAAa,CAAC9B,GAAG,EAAE,OAAO,CAAC,CAAC;IACjDN,wBAAwB,CAACkB,MAAM,EAAEM,SAAS,CAAC;IAC3C,IAAI,CAACa,YAAY,CAACf,WAAW,EAAEE,SAAS,EAAEL,IAAI,CAAC;IAC/Cd,IAAI,CAACiC,gBAAgB,CAAChC,GAAG,EAAEwB,EAAE,CAAC;IAC9B9B,wBAAwB,CAAC8B,EAAE,EAAEN,SAAS,EAAE,WAAW,CAAC;IACpD;IACA,IAAIe,KAAK,GAAGb,aAAa,CAACc,GAAG,CAAC,OAAO,CAAC;IACtCvC,mBAAmB,CAAC,IAAI,EAAEsC,KAAK,KAAK,WAAW,GAAGpB,IAAI,CAACsB,sBAAsB,CAAC,CAAC,GAAGF,KAAK,EAAEb,aAAa,CAACc,GAAG,CAAC,WAAW,CAAC,EAAEd,aAAa,CAACc,GAAG,CAAC,UAAU,CAAC,CAAC;EACzJ,CAAC;EACDrC,UAAU,CAACa,SAAS,CAACqB,YAAY,GAAG,UAAUf,WAAW,EAAEE,SAAS,EAAEL,IAAI,EAAE;IAC1E,IAAIuB,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACjC,IAAIhB,MAAM,GAAGR,IAAI,CAACyB,SAAS,CAAC,CAAC;IAC7B,IAAIC,QAAQ,GAAG,CAAClB,MAAM,CAACpB,UAAU,GAAGoB,MAAM,CAACmB,QAAQ,IAAI,CAAC;IACxD,IAAIC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC;IAC3B,IAAIK,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACN,QAAQ,CAAC;IAC3B,IAAIO,gBAAgB,GAAG5B,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC;IAClDiB,KAAK,CAACW,MAAM,GAAG,CAACD,gBAAgB,CAACZ,GAAG,CAAC,MAAM,CAAC;IAC5C;IACA,IAAIc,gBAAgB,GAAGxD,oBAAoB,CAAC0B,SAAS,CAAC;IACtD,IAAI+B,KAAK,GAAGpC,IAAI,CAACqC,SAAS,CAAC,OAAO,CAAC;IACnCzD,aAAa,CAAC2C,KAAK,EAAEY,gBAAgB,EAAE;MACrCG,YAAY,EAAE;QACZC,iBAAiB,EAAE,SAAAA,CAAUC,SAAS,EAAEC,SAAS,EAAElD,QAAQ,EAAEmD,aAAa,EAAEC,SAAS,EAAEC,YAAY,EAAE;UACnG,OAAOzC,WAAW,CAACoC,iBAAiB,CAACC,SAAS,EAAEC,SAAS,EAAE,MAAM,EAAEC,aAAa;UAChF;UACA;UACAlE,SAAS,CAACmE,SAAS,EAAER,gBAAgB,CAACU,MAAM,IAAIV,gBAAgB,CAACU,MAAM,CAACxB,GAAG,CAAC,WAAW,CAAC,EAAEhB,SAAS,CAACgB,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEuB,YAAY,CAAC;QACjI;MACF,CAAC;MACDE,cAAc,EAAE9C,IAAI,CAACwC,SAAS;MAC9BO,WAAW,EAAE/C,IAAI,CAACwC,SAAS,GAAG,EAAE;MAChCQ,YAAY,EAAEZ,KAAK,CAACa,IAAI;MACxBC,cAAc,EAAEd,KAAK,CAACe,OAAO;MAC7BC,sBAAsB,EAAE;IAC1B,CAAC,CAAC;IACF;IACA,IAAIC,aAAa,GAAGpB,gBAAgB,CAACZ,GAAG,CAAC,UAAU,CAAC,IAAI,SAAS;IACjE,IAAIiC,YAAY,GAAGrB,gBAAgB,CAACZ,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;IACxD,IAAIkC,CAAC;IACL,IAAIF,aAAa,KAAK,SAAS,EAAE;MAC/BE,CAAC,GAAG/C,MAAM,CAAC+C,CAAC,GAAGD,YAAY;IAC7B,CAAC,MAAM;MACLC,CAAC,GAAG,CAAC/C,MAAM,CAAC+C,CAAC,GAAG/C,MAAM,CAACgD,EAAE,IAAI,CAAC;IAChC;IACA,IAAI,CAACC,UAAU,GAAG;MAChBC,MAAM,EAAEL,aAAa,KAAK;IAC5B,CAAC;IACD,IAAIM,KAAK,GAAGN,aAAa,KAAK,SAAS,GAAGpB,gBAAgB,CAACZ,GAAG,CAAC,OAAO,CAAC,IAAI,QAAQ,GAAGO,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;IAC/G,IAAIgC,aAAa,GAAGP,aAAa,KAAK,SAAS,GAAGpB,gBAAgB,CAACZ,GAAG,CAAC,eAAe,CAAC,IAAI,QAAQ,GAAGU,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,QAAQ;IAC/HR,KAAK,CAACsC,IAAI,CAAC;MACTC,CAAC,EAAElC,EAAE,GAAG2B,CAAC,GAAG/C,MAAM,CAACuD,EAAE;MACrBC,CAAC,EAAEjC,EAAE,GAAGwB,CAAC,GAAG/C,MAAM,CAACyD,EAAE;MACrBC,QAAQ,EAAE,CAAC;MACX9B,KAAK,EAAE;QACLuB,KAAK,EAAEA,KAAK;QACZC,aAAa,EAAEA;MACjB;IACF,CAAC,CAAC;EACJ,CAAC;EACD,OAAO5E,UAAU;AACnB,CAAC,CAACP,OAAO,CAAC0F,MAAM,CAAC;AACjB,eAAenF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}