{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as echarts from '../../core/echarts.js';\nimport { noop } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nexport function take(zr, resourceKey, userKey) {\n  inner(zr)[resourceKey] = userKey;\n}\nexport function release(zr, resourceKey, userKey) {\n  var store = inner(zr);\n  var uKey = store[resourceKey];\n  if (uKey === userKey) {\n    store[resourceKey] = null;\n  }\n}\nexport function isTaken(zr, resourceKey) {\n  return !!inner(zr)[resourceKey];\n}\n/**\n * payload: {\n *     type: 'takeGlobalCursor',\n *     key: 'dataZoomSelect', or 'brush', or ...,\n *         If no userKey, release global cursor.\n * }\n */\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'takeGlobalCursor',\n  event: 'globalCursorTaken',\n  update: 'update'\n}, noop);", "map": {"version": 3, "names": ["echarts", "noop", "makeInner", "inner", "take", "zr", "resourceKey", "<PERSON><PERSON><PERSON>", "release", "store", "u<PERSON>ey", "isTaken", "registerAction", "type", "event", "update"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/helper/interactionMutex.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as echarts from '../../core/echarts.js';\nimport { noop } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nexport function take(zr, resourceKey, userKey) {\n  inner(zr)[resourceKey] = userKey;\n}\nexport function release(zr, resourceKey, userKey) {\n  var store = inner(zr);\n  var uKey = store[resourceKey];\n  if (uKey === userKey) {\n    store[resourceKey] = null;\n  }\n}\nexport function isTaken(zr, resourceKey) {\n  return !!inner(zr)[resourceKey];\n}\n/**\n * payload: {\n *     type: 'takeGlobalCursor',\n *     key: 'dataZoomSelect', or 'brush', or ...,\n *         If no userKey, release global cursor.\n * }\n */\n// TODO: SELF REGISTERED.\necharts.registerAction({\n  type: 'takeGlobalCursor',\n  event: 'globalCursorTaken',\n  update: 'update'\n}, noop);"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,OAAO,SAASE,IAAIA,CAACC,EAAE,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC7CJ,KAAK,CAACE,EAAE,CAAC,CAACC,WAAW,CAAC,GAAGC,OAAO;AAClC;AACA,OAAO,SAASC,OAAOA,CAACH,EAAE,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAChD,IAAIE,KAAK,GAAGN,KAAK,CAACE,EAAE,CAAC;EACrB,IAAIK,IAAI,GAAGD,KAAK,CAACH,WAAW,CAAC;EAC7B,IAAII,IAAI,KAAKH,OAAO,EAAE;IACpBE,KAAK,CAACH,WAAW,CAAC,GAAG,IAAI;EAC3B;AACF;AACA,OAAO,SAASK,OAAOA,CAACN,EAAE,EAAEC,WAAW,EAAE;EACvC,OAAO,CAAC,CAACH,KAAK,CAACE,EAAE,CAAC,CAACC,WAAW,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAN,OAAO,CAACY,cAAc,CAAC;EACrBC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,mBAAmB;EAC1BC,MAAM,EAAE;AACV,CAAC,EAAEd,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}