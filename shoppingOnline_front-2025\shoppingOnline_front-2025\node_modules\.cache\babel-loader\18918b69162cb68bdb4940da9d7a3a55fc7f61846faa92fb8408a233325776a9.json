{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { calcZ2Range } from '../../util/graphic.js';\n/**\n * [CAVEAT]: the call order of `ThumbnailView['render']` and other\n *  `ChartView['render']/ComponentView['render']` is not guaranteed.\n */\nvar ThumbnailBridgeImpl = /** @class */function () {\n  function ThumbnailBridgeImpl(thumbnailModel) {\n    this._thumbnailModel = thumbnailModel;\n  }\n  ThumbnailBridgeImpl.prototype.reset = function (api) {\n    this._renderVersion = api.getMainProcessVersion();\n  };\n  ThumbnailBridgeImpl.prototype.renderContent = function (opt) {\n    var thumbnailView = opt.api.getViewOfComponentModel(this._thumbnailModel);\n    if (!thumbnailView) {\n      return;\n    }\n    opt.group.silent = true;\n    thumbnailView.renderContent({\n      group: opt.group,\n      targetTrans: opt.targetTrans,\n      z2Range: calcZ2Range(opt.group),\n      roamType: opt.roamType,\n      viewportRect: opt.viewportRect,\n      renderVersion: this._renderVersion\n    });\n  };\n  ThumbnailBridgeImpl.prototype.updateWindow = function (targetTrans, api) {\n    var thumbnailView = api.getViewOfComponentModel(this._thumbnailModel);\n    if (!thumbnailView) {\n      return;\n    }\n    thumbnailView.updateWindow({\n      targetTrans: targetTrans,\n      renderVersion: this._renderVersion\n    });\n  };\n  return ThumbnailBridgeImpl;\n}();\nexport { ThumbnailBridgeImpl };", "map": {"version": 3, "names": ["calcZ2Range", "ThumbnailBridgeImpl", "thumbnail<PERSON>odel", "_thumbnailModel", "prototype", "reset", "api", "_renderVersion", "getMainProcessVersion", "renderContent", "opt", "thumbnail<PERSON>iew", "getViewOfComponentModel", "group", "silent", "targetTrans", "z2Range", "roamType", "viewportRect", "renderVersion", "updateWindow"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/thumbnail/ThumbnailBridgeImpl.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { calcZ2Range } from '../../util/graphic.js';\n/**\n * [CAVEAT]: the call order of `ThumbnailView['render']` and other\n *  `ChartView['render']/ComponentView['render']` is not guaranteed.\n */\nvar ThumbnailBridgeImpl = /** @class */function () {\n  function ThumbnailBridgeImpl(thumbnailModel) {\n    this._thumbnailModel = thumbnailModel;\n  }\n  ThumbnailBridgeImpl.prototype.reset = function (api) {\n    this._renderVersion = api.getMainProcessVersion();\n  };\n  ThumbnailBridgeImpl.prototype.renderContent = function (opt) {\n    var thumbnailView = opt.api.getViewOfComponentModel(this._thumbnailModel);\n    if (!thumbnailView) {\n      return;\n    }\n    opt.group.silent = true;\n    thumbnailView.renderContent({\n      group: opt.group,\n      targetTrans: opt.targetTrans,\n      z2Range: calcZ2Range(opt.group),\n      roamType: opt.roamType,\n      viewportRect: opt.viewportRect,\n      renderVersion: this._renderVersion\n    });\n  };\n  ThumbnailBridgeImpl.prototype.updateWindow = function (targetTrans, api) {\n    var thumbnailView = api.getViewOfComponentModel(this._thumbnailModel);\n    if (!thumbnailView) {\n      return;\n    }\n    thumbnailView.updateWindow({\n      targetTrans: targetTrans,\n      renderVersion: this._renderVersion\n    });\n  };\n  return ThumbnailBridgeImpl;\n}();\nexport { ThumbnailBridgeImpl };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,uBAAuB;AACnD;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,aAAa,YAAY;EACjD,SAASA,mBAAmBA,CAACC,cAAc,EAAE;IAC3C,IAAI,CAACC,eAAe,GAAGD,cAAc;EACvC;EACAD,mBAAmB,CAACG,SAAS,CAACC,KAAK,GAAG,UAAUC,GAAG,EAAE;IACnD,IAAI,CAACC,cAAc,GAAGD,GAAG,CAACE,qBAAqB,CAAC,CAAC;EACnD,CAAC;EACDP,mBAAmB,CAACG,SAAS,CAACK,aAAa,GAAG,UAAUC,GAAG,EAAE;IAC3D,IAAIC,aAAa,GAAGD,GAAG,CAACJ,GAAG,CAACM,uBAAuB,CAAC,IAAI,CAACT,eAAe,CAAC;IACzE,IAAI,CAACQ,aAAa,EAAE;MAClB;IACF;IACAD,GAAG,CAACG,KAAK,CAACC,MAAM,GAAG,IAAI;IACvBH,aAAa,CAACF,aAAa,CAAC;MAC1BI,KAAK,EAAEH,GAAG,CAACG,KAAK;MAChBE,WAAW,EAAEL,GAAG,CAACK,WAAW;MAC5BC,OAAO,EAAEhB,WAAW,CAACU,GAAG,CAACG,KAAK,CAAC;MAC/BI,QAAQ,EAAEP,GAAG,CAACO,QAAQ;MACtBC,YAAY,EAAER,GAAG,CAACQ,YAAY;MAC9BC,aAAa,EAAE,IAAI,CAACZ;IACtB,CAAC,CAAC;EACJ,CAAC;EACDN,mBAAmB,CAACG,SAAS,CAACgB,YAAY,GAAG,UAAUL,WAAW,EAAET,GAAG,EAAE;IACvE,IAAIK,aAAa,GAAGL,GAAG,CAACM,uBAAuB,CAAC,IAAI,CAACT,eAAe,CAAC;IACrE,IAAI,CAACQ,aAAa,EAAE;MAClB;IACF;IACAA,aAAa,CAACS,YAAY,CAAC;MACzBL,WAAW,EAAEA,WAAW;MACxBI,aAAa,EAAE,IAAI,CAACZ;IACtB,CAAC,CAAC;EACJ,CAAC;EACD,OAAON,mBAAmB;AAC5B,CAAC,CAAC,CAAC;AACH,SAASA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}