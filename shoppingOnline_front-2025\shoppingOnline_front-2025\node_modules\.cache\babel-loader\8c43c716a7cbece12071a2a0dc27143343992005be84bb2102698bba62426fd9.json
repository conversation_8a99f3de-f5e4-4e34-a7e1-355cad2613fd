{"ast": null, "code": "import { cubicAt, cubicRootAt } from '../core/curve.js';\nimport { trim } from '../core/util.js';\nvar regexp = /cubic-bezier\\(([0-9,\\.e ]+)\\)/;\nexport function createCubicEasingFunc(cubicEasingStr) {\n  var cubic = cubicEasingStr && regexp.exec(cubicEasingStr);\n  if (cubic) {\n    var points = cubic[1].split(',');\n    var a_1 = +trim(points[0]);\n    var b_1 = +trim(points[1]);\n    var c_1 = +trim(points[2]);\n    var d_1 = +trim(points[3]);\n    if (isNaN(a_1 + b_1 + c_1 + d_1)) {\n      return;\n    }\n    var roots_1 = [];\n    return function (p) {\n      return p <= 0 ? 0 : p >= 1 ? 1 : cubicRootAt(0, a_1, c_1, 1, p, roots_1) && cubicAt(0, b_1, d_1, 1, roots_1[0]);\n    };\n  }\n}", "map": {"version": 3, "names": ["cubicAt", "cubicRootAt", "trim", "regexp", "createCubicEasingFunc", "cubicEasingStr", "cubic", "exec", "points", "split", "a_1", "b_1", "c_1", "d_1", "isNaN", "roots_1", "p"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/animation/cubicEasing.js"], "sourcesContent": ["import { cubicAt, cubicRootAt } from '../core/curve.js';\nimport { trim } from '../core/util.js';\nvar regexp = /cubic-bezier\\(([0-9,\\.e ]+)\\)/;\nexport function createCubicEasingFunc(cubicEasingStr) {\n    var cubic = cubicEasingStr && regexp.exec(cubicEasingStr);\n    if (cubic) {\n        var points = cubic[1].split(',');\n        var a_1 = +trim(points[0]);\n        var b_1 = +trim(points[1]);\n        var c_1 = +trim(points[2]);\n        var d_1 = +trim(points[3]);\n        if (isNaN(a_1 + b_1 + c_1 + d_1)) {\n            return;\n        }\n        var roots_1 = [];\n        return function (p) {\n            return p <= 0\n                ? 0 : p >= 1\n                ? 1\n                : cubicRootAt(0, a_1, c_1, 1, p, roots_1) && cubicAt(0, b_1, d_1, 1, roots_1[0]);\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,IAAI,QAAQ,iBAAiB;AACtC,IAAIC,MAAM,GAAG,+BAA+B;AAC5C,OAAO,SAASC,qBAAqBA,CAACC,cAAc,EAAE;EAClD,IAAIC,KAAK,GAAGD,cAAc,IAAIF,MAAM,CAACI,IAAI,CAACF,cAAc,CAAC;EACzD,IAAIC,KAAK,EAAE;IACP,IAAIE,MAAM,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,GAAG,CAAC;IAChC,IAAIC,GAAG,GAAG,CAACR,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIG,GAAG,GAAG,CAACT,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAII,GAAG,GAAG,CAACV,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIK,GAAG,GAAG,CAACX,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIM,KAAK,CAACJ,GAAG,GAAGC,GAAG,GAAGC,GAAG,GAAGC,GAAG,CAAC,EAAE;MAC9B;IACJ;IACA,IAAIE,OAAO,GAAG,EAAE;IAChB,OAAO,UAAUC,CAAC,EAAE;MAChB,OAAOA,CAAC,IAAI,CAAC,GACP,CAAC,GAAGA,CAAC,IAAI,CAAC,GACV,CAAC,GACDf,WAAW,CAAC,CAAC,EAAES,GAAG,EAAEE,GAAG,EAAE,CAAC,EAAEI,CAAC,EAAED,OAAO,CAAC,IAAIf,OAAO,CAAC,CAAC,EAAEW,GAAG,EAAEE,GAAG,EAAE,CAAC,EAAEE,OAAO,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}