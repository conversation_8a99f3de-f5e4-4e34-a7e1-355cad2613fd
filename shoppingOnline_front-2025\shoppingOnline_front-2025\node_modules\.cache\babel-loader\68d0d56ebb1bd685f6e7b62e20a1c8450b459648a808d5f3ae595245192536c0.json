{"ast": null, "code": "var LN2 = Math.log(2);\nfunction determinant(rows, rank, rowStart, rowMask, colMask, detCache) {\n  var cacheKey = rowMask + '-' + colMask;\n  var fullRank = rows.length;\n  if (detCache.hasOwnProperty(cacheKey)) {\n    return detCache[cacheKey];\n  }\n  if (rank === 1) {\n    var colStart = Math.round(Math.log((1 << fullRank) - 1 & ~colMask) / LN2);\n    return rows[rowStart][colStart];\n  }\n  var subRowMask = rowMask | 1 << rowStart;\n  var subRowStart = rowStart + 1;\n  while (rowMask & 1 << subRowStart) {\n    subRowStart++;\n  }\n  var sum = 0;\n  for (var j = 0, colLocalIdx = 0; j < fullRank; j++) {\n    var colTag = 1 << j;\n    if (!(colTag & colMask)) {\n      sum += (colLocalIdx % 2 ? -1 : 1) * rows[rowStart][j] * determinant(rows, rank - 1, subRowStart, subRowMask, colMask | colTag, detCache);\n      colLocalIdx++;\n    }\n  }\n  detCache[cacheKey] = sum;\n  return sum;\n}\nexport function buildTransformer(src, dest) {\n  var mA = [[src[0], src[1], 1, 0, 0, 0, -dest[0] * src[0], -dest[0] * src[1]], [0, 0, 0, src[0], src[1], 1, -dest[1] * src[0], -dest[1] * src[1]], [src[2], src[3], 1, 0, 0, 0, -dest[2] * src[2], -dest[2] * src[3]], [0, 0, 0, src[2], src[3], 1, -dest[3] * src[2], -dest[3] * src[3]], [src[4], src[5], 1, 0, 0, 0, -dest[4] * src[4], -dest[4] * src[5]], [0, 0, 0, src[4], src[5], 1, -dest[5] * src[4], -dest[5] * src[5]], [src[6], src[7], 1, 0, 0, 0, -dest[6] * src[6], -dest[6] * src[7]], [0, 0, 0, src[6], src[7], 1, -dest[7] * src[6], -dest[7] * src[7]]];\n  var detCache = {};\n  var det = determinant(mA, 8, 0, 0, 0, detCache);\n  if (det === 0) {\n    return;\n  }\n  var vh = [];\n  for (var i = 0; i < 8; i++) {\n    for (var j = 0; j < 8; j++) {\n      vh[j] == null && (vh[j] = 0);\n      vh[j] += ((i + j) % 2 ? -1 : 1) * determinant(mA, 7, i === 0 ? 1 : 0, 1 << i, 1 << j, detCache) / det * dest[i];\n    }\n  }\n  return function (out, srcPointX, srcPointY) {\n    var pk = srcPointX * vh[6] + srcPointY * vh[7] + 1;\n    out[0] = (srcPointX * vh[0] + srcPointY * vh[1] + vh[2]) / pk;\n    out[1] = (srcPointX * vh[3] + srcPointY * vh[4] + vh[5]) / pk;\n  };\n}", "map": {"version": 3, "names": ["LN2", "Math", "log", "determinant", "rows", "rank", "rowStart", "rowMask", "colMask", "det<PERSON><PERSON>", "cache<PERSON>ey", "fullRank", "length", "hasOwnProperty", "colStart", "round", "subRowMask", "subRowStart", "sum", "j", "colLocalIdx", "colTag", "buildTransformer", "src", "dest", "mA", "det", "vh", "i", "out", "srcPointX", "srcPointY", "pk"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/core/fourPointsTransform.js"], "sourcesContent": ["var LN2 = Math.log(2);\nfunction determinant(rows, rank, rowStart, rowMask, colMask, detCache) {\n    var cacheKey = rowMask + '-' + colMask;\n    var fullRank = rows.length;\n    if (detCache.hasOwnProperty(cacheKey)) {\n        return detCache[cacheKey];\n    }\n    if (rank === 1) {\n        var colStart = Math.round(Math.log(((1 << fullRank) - 1) & ~colMask) / LN2);\n        return rows[rowStart][colStart];\n    }\n    var subRowMask = rowMask | (1 << rowStart);\n    var subRowStart = rowStart + 1;\n    while (rowMask & (1 << subRowStart)) {\n        subRowStart++;\n    }\n    var sum = 0;\n    for (var j = 0, colLocalIdx = 0; j < fullRank; j++) {\n        var colTag = 1 << j;\n        if (!(colTag & colMask)) {\n            sum += (colLocalIdx % 2 ? -1 : 1) * rows[rowStart][j]\n                * determinant(rows, rank - 1, subRowStart, subRowMask, colMask | colTag, detCache);\n            colLocalIdx++;\n        }\n    }\n    detCache[cacheKey] = sum;\n    return sum;\n}\nexport function buildTransformer(src, dest) {\n    var mA = [\n        [src[0], src[1], 1, 0, 0, 0, -dest[0] * src[0], -dest[0] * src[1]],\n        [0, 0, 0, src[0], src[1], 1, -dest[1] * src[0], -dest[1] * src[1]],\n        [src[2], src[3], 1, 0, 0, 0, -dest[2] * src[2], -dest[2] * src[3]],\n        [0, 0, 0, src[2], src[3], 1, -dest[3] * src[2], -dest[3] * src[3]],\n        [src[4], src[5], 1, 0, 0, 0, -dest[4] * src[4], -dest[4] * src[5]],\n        [0, 0, 0, src[4], src[5], 1, -dest[5] * src[4], -dest[5] * src[5]],\n        [src[6], src[7], 1, 0, 0, 0, -dest[6] * src[6], -dest[6] * src[7]],\n        [0, 0, 0, src[6], src[7], 1, -dest[7] * src[6], -dest[7] * src[7]]\n    ];\n    var detCache = {};\n    var det = determinant(mA, 8, 0, 0, 0, detCache);\n    if (det === 0) {\n        return;\n    }\n    var vh = [];\n    for (var i = 0; i < 8; i++) {\n        for (var j = 0; j < 8; j++) {\n            vh[j] == null && (vh[j] = 0);\n            vh[j] += ((i + j) % 2 ? -1 : 1)\n                * determinant(mA, 7, i === 0 ? 1 : 0, 1 << i, 1 << j, detCache)\n                / det * dest[i];\n        }\n    }\n    return function (out, srcPointX, srcPointY) {\n        var pk = srcPointX * vh[6] + srcPointY * vh[7] + 1;\n        out[0] = (srcPointX * vh[0] + srcPointY * vh[1] + vh[2]) / pk;\n        out[1] = (srcPointX * vh[3] + srcPointY * vh[4] + vh[5]) / pk;\n    };\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AACrB,SAASC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACnE,IAAIC,QAAQ,GAAGH,OAAO,GAAG,GAAG,GAAGC,OAAO;EACtC,IAAIG,QAAQ,GAAGP,IAAI,CAACQ,MAAM;EAC1B,IAAIH,QAAQ,CAACI,cAAc,CAACH,QAAQ,CAAC,EAAE;IACnC,OAAOD,QAAQ,CAACC,QAAQ,CAAC;EAC7B;EACA,IAAIL,IAAI,KAAK,CAAC,EAAE;IACZ,IAAIS,QAAQ,GAAGb,IAAI,CAACc,KAAK,CAACd,IAAI,CAACC,GAAG,CAAE,CAAC,CAAC,IAAIS,QAAQ,IAAI,CAAC,GAAI,CAACH,OAAO,CAAC,GAAGR,GAAG,CAAC;IAC3E,OAAOI,IAAI,CAACE,QAAQ,CAAC,CAACQ,QAAQ,CAAC;EACnC;EACA,IAAIE,UAAU,GAAGT,OAAO,GAAI,CAAC,IAAID,QAAS;EAC1C,IAAIW,WAAW,GAAGX,QAAQ,GAAG,CAAC;EAC9B,OAAOC,OAAO,GAAI,CAAC,IAAIU,WAAY,EAAE;IACjCA,WAAW,EAAE;EACjB;EACA,IAAIC,GAAG,GAAG,CAAC;EACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,EAAED,CAAC,GAAGR,QAAQ,EAAEQ,CAAC,EAAE,EAAE;IAChD,IAAIE,MAAM,GAAG,CAAC,IAAIF,CAAC;IACnB,IAAI,EAAEE,MAAM,GAAGb,OAAO,CAAC,EAAE;MACrBU,GAAG,IAAI,CAACE,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIhB,IAAI,CAACE,QAAQ,CAAC,CAACa,CAAC,CAAC,GAC/ChB,WAAW,CAACC,IAAI,EAAEC,IAAI,GAAG,CAAC,EAAEY,WAAW,EAAED,UAAU,EAAER,OAAO,GAAGa,MAAM,EAAEZ,QAAQ,CAAC;MACtFW,WAAW,EAAE;IACjB;EACJ;EACAX,QAAQ,CAACC,QAAQ,CAAC,GAAGQ,GAAG;EACxB,OAAOA,GAAG;AACd;AACA,OAAO,SAASI,gBAAgBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACxC,IAAIC,EAAE,GAAG,CACL,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC,CACrE;EACD,IAAId,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIiB,GAAG,GAAGvB,WAAW,CAACsB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEhB,QAAQ,CAAC;EAC/C,IAAIiB,GAAG,KAAK,CAAC,EAAE;IACX;EACJ;EACA,IAAIC,EAAE,GAAG,EAAE;EACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxB,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBQ,EAAE,CAACR,CAAC,CAAC,IAAI,IAAI,KAAKQ,EAAE,CAACR,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5BQ,EAAE,CAACR,CAAC,CAAC,IAAI,CAAC,CAACS,CAAC,GAAGT,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IACxBhB,WAAW,CAACsB,EAAE,EAAE,CAAC,EAAEG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAIA,CAAC,EAAE,CAAC,IAAIT,CAAC,EAAEV,QAAQ,CAAC,GAC7DiB,GAAG,GAAGF,IAAI,CAACI,CAAC,CAAC;IACvB;EACJ;EACA,OAAO,UAAUC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACxC,IAAIC,EAAE,GAAGF,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS,GAAGJ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAClDE,GAAG,CAAC,CAAC,CAAC,GAAG,CAACC,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS,GAAGJ,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,IAAIK,EAAE;IAC7DH,GAAG,CAAC,CAAC,CAAC,GAAG,CAACC,SAAS,GAAGH,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS,GAAGJ,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,IAAIK,EAAE;EACjE,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}