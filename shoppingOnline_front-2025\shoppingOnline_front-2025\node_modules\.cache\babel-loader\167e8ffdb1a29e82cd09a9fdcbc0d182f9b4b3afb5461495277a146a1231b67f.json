{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nimport { invert } from './core/matrix.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n  obj[key] = true;\n  return obj;\n}, {\n  ignore: false\n});\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar tmpInnerTextTrans = [];\nvar Element = function () {\n  function Element(props) {\n    this.id = guid();\n    this.animators = [];\n    this.currentStates = [];\n    this.states = {};\n    this._init(props);\n  }\n  Element.prototype._init = function (props) {\n    this.attr(props);\n  };\n  Element.prototype.drift = function (dx, dy, e) {\n    switch (this.draggable) {\n      case 'horizontal':\n        dy = 0;\n        break;\n      case 'vertical':\n        dx = 0;\n        break;\n    }\n    var m = this.transform;\n    if (!m) {\n      m = this.transform = [1, 0, 0, 1, 0, 0];\n    }\n    m[4] += dx;\n    m[5] += dy;\n    this.decomposeTransform();\n    this.markRedraw();\n  };\n  Element.prototype.beforeUpdate = function () {};\n  Element.prototype.afterUpdate = function () {};\n  Element.prototype.update = function () {\n    this.updateTransform();\n    if (this.__dirty) {\n      this.updateInnerText();\n    }\n  };\n  Element.prototype.updateInnerText = function (forceUpdate) {\n    var textEl = this._textContent;\n    if (textEl && (!textEl.ignore || forceUpdate)) {\n      if (!this.textConfig) {\n        this.textConfig = {};\n      }\n      var textConfig = this.textConfig;\n      var isLocal = textConfig.local;\n      var innerTransformable = textEl.innerTransformable;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var textStyleChanged = false;\n      innerTransformable.parent = isLocal ? this : null;\n      var innerOrigin = false;\n      innerTransformable.copyTransform(textEl);\n      var hasPosition = textConfig.position != null;\n      var autoOverflowArea = textConfig.autoOverflowArea;\n      var layoutRect = void 0;\n      if (autoOverflowArea || hasPosition) {\n        layoutRect = tmpBoundingRect;\n        if (textConfig.layoutRect) {\n          layoutRect.copy(textConfig.layoutRect);\n        } else {\n          layoutRect.copy(this.getBoundingRect());\n        }\n        if (!isLocal) {\n          layoutRect.applyTransform(this.transform);\n        }\n      }\n      if (hasPosition) {\n        if (this.calculateTextPosition) {\n          this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        } else {\n          calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n        }\n        innerTransformable.x = tmpTextPosCalcRes.x;\n        innerTransformable.y = tmpTextPosCalcRes.y;\n        textAlign = tmpTextPosCalcRes.align;\n        textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n        var textOrigin = textConfig.origin;\n        if (textOrigin && textConfig.rotation != null) {\n          var relOriginX = void 0;\n          var relOriginY = void 0;\n          if (textOrigin === 'center') {\n            relOriginX = layoutRect.width * 0.5;\n            relOriginY = layoutRect.height * 0.5;\n          } else {\n            relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n            relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n          }\n          innerOrigin = true;\n          innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n          innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n        }\n      }\n      if (textConfig.rotation != null) {\n        innerTransformable.rotation = textConfig.rotation;\n      }\n      var textOffset = textConfig.offset;\n      if (textOffset) {\n        innerTransformable.x += textOffset[0];\n        innerTransformable.y += textOffset[1];\n        if (!innerOrigin) {\n          innerTransformable.originX = -textOffset[0];\n          innerTransformable.originY = -textOffset[1];\n        }\n      }\n      var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n      if (autoOverflowArea) {\n        var overflowRect = innerTextDefaultStyle.overflowRect = innerTextDefaultStyle.overflowRect || new BoundingRect(0, 0, 0, 0);\n        innerTransformable.getLocalTransform(tmpInnerTextTrans);\n        invert(tmpInnerTextTrans, tmpInnerTextTrans);\n        BoundingRect.copy(overflowRect, layoutRect);\n        overflowRect.applyTransform(tmpInnerTextTrans);\n      } else {\n        innerTextDefaultStyle.overflowRect = null;\n      }\n      var isInside = textConfig.inside == null ? typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0 : textConfig.inside;\n      var textFill = void 0;\n      var textStroke = void 0;\n      var autoStroke = void 0;\n      if (isInside && this.canBeInsideText()) {\n        textFill = textConfig.insideFill;\n        textStroke = textConfig.insideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getInsideTextFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getInsideTextStroke(textFill);\n          autoStroke = true;\n        }\n      } else {\n        textFill = textConfig.outsideFill;\n        textStroke = textConfig.outsideStroke;\n        if (textFill == null || textFill === 'auto') {\n          textFill = this.getOutsideFill();\n        }\n        if (textStroke == null || textStroke === 'auto') {\n          textStroke = this.getOutsideStroke(textFill);\n          autoStroke = true;\n        }\n      }\n      textFill = textFill || '#000';\n      if (textFill !== innerTextDefaultStyle.fill || textStroke !== innerTextDefaultStyle.stroke || autoStroke !== innerTextDefaultStyle.autoStroke || textAlign !== innerTextDefaultStyle.align || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n        textStyleChanged = true;\n        innerTextDefaultStyle.fill = textFill;\n        innerTextDefaultStyle.stroke = textStroke;\n        innerTextDefaultStyle.autoStroke = autoStroke;\n        innerTextDefaultStyle.align = textAlign;\n        innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n        textEl.setDefaultTextStyle(innerTextDefaultStyle);\n      }\n      textEl.__dirty |= REDRAW_BIT;\n      if (textStyleChanged) {\n        textEl.dirtyStyle(true);\n      }\n    }\n  };\n  Element.prototype.canBeInsideText = function () {\n    return true;\n  };\n  Element.prototype.getInsideTextFill = function () {\n    return '#fff';\n  };\n  Element.prototype.getInsideTextStroke = function (textFill) {\n    return '#000';\n  };\n  Element.prototype.getOutsideFill = function () {\n    return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n  };\n  Element.prototype.getOutsideStroke = function (textFill) {\n    var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n    var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n    if (!colorArr) {\n      colorArr = [255, 255, 255, 1];\n    }\n    var alpha = colorArr[3];\n    var isDark = this.__zr.isDarkMode();\n    for (var i = 0; i < 3; i++) {\n      colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n    }\n    colorArr[3] = 1;\n    return stringify(colorArr, 'rgba');\n  };\n  Element.prototype.traverse = function (cb, context) {};\n  Element.prototype.attrKV = function (key, value) {\n    if (key === 'textConfig') {\n      this.setTextConfig(value);\n    } else if (key === 'textContent') {\n      this.setTextContent(value);\n    } else if (key === 'clipPath') {\n      this.setClipPath(value);\n    } else if (key === 'extra') {\n      this.extra = this.extra || {};\n      extend(this.extra, value);\n    } else {\n      this[key] = value;\n    }\n  };\n  Element.prototype.hide = function () {\n    this.ignore = true;\n    this.markRedraw();\n  };\n  Element.prototype.show = function () {\n    this.ignore = false;\n    this.markRedraw();\n  };\n  Element.prototype.attr = function (keyOrObj, value) {\n    if (typeof keyOrObj === 'string') {\n      this.attrKV(keyOrObj, value);\n    } else if (isObject(keyOrObj)) {\n      var obj = keyOrObj;\n      var keysArr = keys(obj);\n      for (var i = 0; i < keysArr.length; i++) {\n        var key = keysArr[i];\n        this.attrKV(key, keyOrObj[key]);\n      }\n    }\n    this.markRedraw();\n    return this;\n  };\n  Element.prototype.saveCurrentToNormalState = function (toState) {\n    this._innerSaveToNormal(toState);\n    var normalState = this._normalState;\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      var fromStateTransition = animator.__fromStateTransition;\n      if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n        continue;\n      }\n      var targetName = animator.targetName;\n      var target = targetName ? normalState[targetName] : normalState;\n      animator.saveTo(target);\n    }\n  };\n  Element.prototype._innerSaveToNormal = function (toState) {\n    var normalState = this._normalState;\n    if (!normalState) {\n      normalState = this._normalState = {};\n    }\n    if (toState.textConfig && !normalState.textConfig) {\n      normalState.textConfig = this.textConfig;\n    }\n    this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n  };\n  Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n    for (var i = 0; i < primaryKeys.length; i++) {\n      var key = primaryKeys[i];\n      if (toState[key] != null && !(key in normalState)) {\n        normalState[key] = this[key];\n      }\n    }\n  };\n  Element.prototype.hasState = function () {\n    return this.currentStates.length > 0;\n  };\n  Element.prototype.getState = function (name) {\n    return this.states[name];\n  };\n  Element.prototype.ensureState = function (name) {\n    var states = this.states;\n    if (!states[name]) {\n      states[name] = {};\n    }\n    return states[name];\n  };\n  Element.prototype.clearStates = function (noAnimation) {\n    this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n  };\n  Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n    var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n    var hasStates = this.hasState();\n    if (!hasStates && toNormalState) {\n      return;\n    }\n    var currentStates = this.currentStates;\n    var animationCfg = this.stateTransition;\n    if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n      return;\n    }\n    var state;\n    if (this.stateProxy && !toNormalState) {\n      state = this.stateProxy(stateName);\n    }\n    if (!state) {\n      state = this.states && this.states[stateName];\n    }\n    if (!state && !toNormalState) {\n      logError(\"State \" + stateName + \" not exists.\");\n      return;\n    }\n    if (!toNormalState) {\n      this.saveCurrentToNormalState(state);\n    }\n    var useHoverLayer = !!(state && state.hoverLayer || forceUseHoverLayer);\n    if (useHoverLayer) {\n      this._toggleHoverLayerFlag(true);\n    }\n    this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (textGuide) {\n      textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n    }\n    if (toNormalState) {\n      this.currentStates = [];\n      this._normalState = {};\n    } else {\n      if (!keepCurrentStates) {\n        this.currentStates = [stateName];\n      } else {\n        this.currentStates.push(stateName);\n      }\n    }\n    this._updateAnimationTargets();\n    this.markRedraw();\n    if (!useHoverLayer && this.__inHover) {\n      this._toggleHoverLayerFlag(false);\n      this.__dirty &= ~REDRAW_BIT;\n    }\n    return state;\n  };\n  Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n    if (!states.length) {\n      this.clearStates();\n    } else {\n      var stateObjects = [];\n      var currentStates = this.currentStates;\n      var len = states.length;\n      var notChange = len === currentStates.length;\n      if (notChange) {\n        for (var i = 0; i < len; i++) {\n          if (states[i] !== currentStates[i]) {\n            notChange = false;\n            break;\n          }\n        }\n      }\n      if (notChange) {\n        return;\n      }\n      for (var i = 0; i < len; i++) {\n        var stateName = states[i];\n        var stateObj = void 0;\n        if (this.stateProxy) {\n          stateObj = this.stateProxy(stateName, states);\n        }\n        if (!stateObj) {\n          stateObj = this.states[stateName];\n        }\n        if (stateObj) {\n          stateObjects.push(stateObj);\n        }\n      }\n      var lastStateObj = stateObjects[len - 1];\n      var useHoverLayer = !!(lastStateObj && lastStateObj.hoverLayer || forceUseHoverLayer);\n      if (useHoverLayer) {\n        this._toggleHoverLayerFlag(true);\n      }\n      var mergedState = this._mergeStates(stateObjects);\n      var animationCfg = this.stateTransition;\n      this.saveCurrentToNormalState(mergedState);\n      this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n      var textContent = this._textContent;\n      var textGuide = this._textGuide;\n      if (textContent) {\n        textContent.useStates(states, noAnimation, useHoverLayer);\n      }\n      if (textGuide) {\n        textGuide.useStates(states, noAnimation, useHoverLayer);\n      }\n      this._updateAnimationTargets();\n      this.currentStates = states.slice();\n      this.markRedraw();\n      if (!useHoverLayer && this.__inHover) {\n        this._toggleHoverLayerFlag(false);\n        this.__dirty &= ~REDRAW_BIT;\n      }\n    }\n  };\n  Element.prototype.isSilent = function () {\n    var el = this;\n    while (el) {\n      if (el.silent) {\n        return true;\n      }\n      var hostEl = el.__hostTarget;\n      el = hostEl ? el.ignoreHostSilent ? null : hostEl : el.parent;\n    }\n    return false;\n  };\n  Element.prototype._updateAnimationTargets = function () {\n    for (var i = 0; i < this.animators.length; i++) {\n      var animator = this.animators[i];\n      if (animator.targetName) {\n        animator.changeTarget(this[animator.targetName]);\n      }\n    }\n  };\n  Element.prototype.removeState = function (state) {\n    var idx = indexOf(this.currentStates, state);\n    if (idx >= 0) {\n      var currentStates = this.currentStates.slice();\n      currentStates.splice(idx, 1);\n      this.useStates(currentStates);\n    }\n  };\n  Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n    var currentStates = this.currentStates.slice();\n    var idx = indexOf(currentStates, oldState);\n    var newStateExists = indexOf(currentStates, newState) >= 0;\n    if (idx >= 0) {\n      if (!newStateExists) {\n        currentStates[idx] = newState;\n      } else {\n        currentStates.splice(idx, 1);\n      }\n    } else if (forceAdd && !newStateExists) {\n      currentStates.push(newState);\n    }\n    this.useStates(currentStates);\n  };\n  Element.prototype.toggleState = function (state, enable) {\n    if (enable) {\n      this.useState(state, true);\n    } else {\n      this.removeState(state);\n    }\n  };\n  Element.prototype._mergeStates = function (states) {\n    var mergedState = {};\n    var mergedTextConfig;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      extend(mergedState, state);\n      if (state.textConfig) {\n        mergedTextConfig = mergedTextConfig || {};\n        extend(mergedTextConfig, state.textConfig);\n      }\n    }\n    if (mergedTextConfig) {\n      mergedState.textConfig = mergedTextConfig;\n    }\n    return mergedState;\n  };\n  Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    if (state && state.textConfig) {\n      this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n      extend(this.textConfig, state.textConfig);\n    } else if (needsRestoreToNormal) {\n      if (normalState.textConfig) {\n        this.textConfig = normalState.textConfig;\n      }\n    }\n    var transitionTarget = {};\n    var hasTransition = false;\n    for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n      var key = PRIMARY_STATES_KEYS[i];\n      var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n      if (state && state[key] != null) {\n        if (propNeedsTransition) {\n          hasTransition = true;\n          transitionTarget[key] = state[key];\n        } else {\n          this[key] = state[key];\n        }\n      } else if (needsRestoreToNormal) {\n        if (normalState[key] != null) {\n          if (propNeedsTransition) {\n            hasTransition = true;\n            transitionTarget[key] = normalState[key];\n          } else {\n            this[key] = normalState[key];\n          }\n        }\n      }\n    }\n    if (!transition) {\n      for (var i = 0; i < this.animators.length; i++) {\n        var animator = this.animators[i];\n        var targetName = animator.targetName;\n        if (!animator.getLoop()) {\n          animator.__changeFinalValue(targetName ? (state || normalState)[targetName] : state || normalState);\n        }\n      }\n    }\n    if (hasTransition) {\n      this._transitionState(stateName, transitionTarget, animationCfg);\n    }\n  };\n  Element.prototype._attachComponent = function (componentEl) {\n    if (componentEl.__zr && !componentEl.__hostTarget) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Text element has been added to zrender.');\n      }\n      return;\n    }\n    if (componentEl === this) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('Recursive component attachment.');\n      }\n      return;\n    }\n    var zr = this.__zr;\n    if (zr) {\n      componentEl.addSelfToZr(zr);\n    }\n    componentEl.__zr = zr;\n    componentEl.__hostTarget = this;\n  };\n  Element.prototype._detachComponent = function (componentEl) {\n    if (componentEl.__zr) {\n      componentEl.removeSelfFromZr(componentEl.__zr);\n    }\n    componentEl.__zr = null;\n    componentEl.__hostTarget = null;\n  };\n  Element.prototype.getClipPath = function () {\n    return this._clipPath;\n  };\n  Element.prototype.setClipPath = function (clipPath) {\n    if (this._clipPath && this._clipPath !== clipPath) {\n      this.removeClipPath();\n    }\n    this._attachComponent(clipPath);\n    this._clipPath = clipPath;\n    this.markRedraw();\n  };\n  Element.prototype.removeClipPath = function () {\n    var clipPath = this._clipPath;\n    if (clipPath) {\n      this._detachComponent(clipPath);\n      this._clipPath = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextContent = function () {\n    return this._textContent;\n  };\n  Element.prototype.setTextContent = function (textEl) {\n    var previousTextContent = this._textContent;\n    if (previousTextContent === textEl) {\n      return;\n    }\n    if (previousTextContent && previousTextContent !== textEl) {\n      this.removeTextContent();\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (textEl.__zr && !textEl.__hostTarget) {\n        throw new Error('Text element has been added to zrender.');\n      }\n    }\n    textEl.innerTransformable = new Transformable();\n    this._attachComponent(textEl);\n    this._textContent = textEl;\n    this.markRedraw();\n  };\n  Element.prototype.setTextConfig = function (cfg) {\n    if (!this.textConfig) {\n      this.textConfig = {};\n    }\n    extend(this.textConfig, cfg);\n    this.markRedraw();\n  };\n  Element.prototype.removeTextConfig = function () {\n    this.textConfig = null;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextContent = function () {\n    var textEl = this._textContent;\n    if (textEl) {\n      textEl.innerTransformable = null;\n      this._detachComponent(textEl);\n      this._textContent = null;\n      this._innerTextDefaultStyle = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.getTextGuideLine = function () {\n    return this._textGuide;\n  };\n  Element.prototype.setTextGuideLine = function (guideLine) {\n    if (this._textGuide && this._textGuide !== guideLine) {\n      this.removeTextGuideLine();\n    }\n    this._attachComponent(guideLine);\n    this._textGuide = guideLine;\n    this.markRedraw();\n  };\n  Element.prototype.removeTextGuideLine = function () {\n    var textGuide = this._textGuide;\n    if (textGuide) {\n      this._detachComponent(textGuide);\n      this._textGuide = null;\n      this.markRedraw();\n    }\n  };\n  Element.prototype.markRedraw = function () {\n    this.__dirty |= REDRAW_BIT;\n    var zr = this.__zr;\n    if (zr) {\n      if (this.__inHover) {\n        zr.refreshHover();\n      } else {\n        zr.refresh();\n      }\n    }\n    if (this.__hostTarget) {\n      this.__hostTarget.markRedraw();\n    }\n  };\n  Element.prototype.dirty = function () {\n    this.markRedraw();\n  };\n  Element.prototype._toggleHoverLayerFlag = function (inHover) {\n    this.__inHover = inHover;\n    var textContent = this._textContent;\n    var textGuide = this._textGuide;\n    if (textContent) {\n      textContent.__inHover = inHover;\n    }\n    if (textGuide) {\n      textGuide.__inHover = inHover;\n    }\n  };\n  Element.prototype.addSelfToZr = function (zr) {\n    if (this.__zr === zr) {\n      return;\n    }\n    this.__zr = zr;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.addAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.addSelfToZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.addSelfToZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.addSelfToZr(zr);\n    }\n  };\n  Element.prototype.removeSelfFromZr = function (zr) {\n    if (!this.__zr) {\n      return;\n    }\n    this.__zr = null;\n    var animators = this.animators;\n    if (animators) {\n      for (var i = 0; i < animators.length; i++) {\n        zr.animation.removeAnimator(animators[i]);\n      }\n    }\n    if (this._clipPath) {\n      this._clipPath.removeSelfFromZr(zr);\n    }\n    if (this._textContent) {\n      this._textContent.removeSelfFromZr(zr);\n    }\n    if (this._textGuide) {\n      this._textGuide.removeSelfFromZr(zr);\n    }\n  };\n  Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n    var target = key ? this[key] : this;\n    if (process.env.NODE_ENV !== 'production') {\n      if (!target) {\n        logError('Property \"' + key + '\" is not existed in element ' + this.id);\n        return;\n      }\n    }\n    var animator = new Animator(target, loop, allowDiscreteAnimation);\n    key && (animator.targetName = key);\n    this.addAnimator(animator, key);\n    return animator;\n  };\n  Element.prototype.addAnimator = function (animator, key) {\n    var zr = this.__zr;\n    var el = this;\n    animator.during(function () {\n      el.updateDuringAnimation(key);\n    }).done(function () {\n      var animators = el.animators;\n      var idx = indexOf(animators, animator);\n      if (idx >= 0) {\n        animators.splice(idx, 1);\n      }\n    });\n    this.animators.push(animator);\n    if (zr) {\n      zr.animation.addAnimator(animator);\n    }\n    zr && zr.wakeUp();\n  };\n  Element.prototype.updateDuringAnimation = function (key) {\n    this.markRedraw();\n  };\n  Element.prototype.stopAnimation = function (scope, forwardToLast) {\n    var animators = this.animators;\n    var len = animators.length;\n    var leftAnimators = [];\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      if (!scope || scope === animator.scope) {\n        animator.stop(forwardToLast);\n      } else {\n        leftAnimators.push(animator);\n      }\n    }\n    this.animators = leftAnimators;\n    return this;\n  };\n  Element.prototype.animateTo = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps);\n  };\n  Element.prototype.animateFrom = function (target, cfg, animationProps) {\n    animateTo(this, target, cfg, animationProps, true);\n  };\n  Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n    var animators = animateTo(this, target, cfg, animationProps);\n    for (var i = 0; i < animators.length; i++) {\n      animators[i].__fromStateTransition = stateName;\n    }\n  };\n  Element.prototype.getBoundingRect = function () {\n    return null;\n  };\n  Element.prototype.getPaintRect = function () {\n    return null;\n  };\n  Element.initDefaultProps = function () {\n    var elProto = Element.prototype;\n    elProto.type = 'element';\n    elProto.name = '';\n    elProto.ignore = elProto.silent = elProto.ignoreHostSilent = elProto.isGroup = elProto.draggable = elProto.dragging = elProto.ignoreClip = elProto.__inHover = false;\n    elProto.__dirty = REDRAW_BIT;\n    var logs = {};\n    function logDeprecatedError(key, xKey, yKey) {\n      if (!logs[key + xKey + yKey]) {\n        console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n        logs[key + xKey + yKey] = true;\n      }\n    }\n    function createLegacyProperty(key, privateKey, xKey, yKey) {\n      Object.defineProperty(elProto, key, {\n        get: function () {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          if (!this[privateKey]) {\n            var pos = this[privateKey] = [];\n            enhanceArray(this, pos);\n          }\n          return this[privateKey];\n        },\n        set: function (pos) {\n          if (process.env.NODE_ENV !== 'production') {\n            logDeprecatedError(key, xKey, yKey);\n          }\n          this[xKey] = pos[0];\n          this[yKey] = pos[1];\n          this[privateKey] = pos;\n          enhanceArray(this, pos);\n        }\n      });\n      function enhanceArray(self, pos) {\n        Object.defineProperty(pos, 0, {\n          get: function () {\n            return self[xKey];\n          },\n          set: function (val) {\n            self[xKey] = val;\n          }\n        });\n        Object.defineProperty(pos, 1, {\n          get: function () {\n            return self[yKey];\n          },\n          set: function (val) {\n            self[yKey] = val;\n          }\n        });\n      }\n    }\n    if (Object.defineProperty) {\n      createLegacyProperty('position', '_legacyPos', 'x', 'y');\n      createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n      createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n    }\n  }();\n  return Element;\n}();\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n  cfg = cfg || {};\n  var animators = [];\n  animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n  var finishCount = animators.length;\n  var doneHappened = false;\n  var cfgDone = cfg.done;\n  var cfgAborted = cfg.aborted;\n  var doneCb = function () {\n    doneHappened = true;\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  var abortedCb = function () {\n    finishCount--;\n    if (finishCount <= 0) {\n      doneHappened ? cfgDone && cfgDone() : cfgAborted && cfgAborted();\n    }\n  };\n  if (!finishCount) {\n    cfgDone && cfgDone();\n  }\n  if (animators.length > 0 && cfg.during) {\n    animators[0].during(function (target, percent) {\n      cfg.during(percent);\n    });\n  }\n  for (var i = 0; i < animators.length; i++) {\n    var animator = animators[i];\n    if (doneCb) {\n      animator.done(doneCb);\n    }\n    if (abortedCb) {\n      animator.aborted(abortedCb);\n    }\n    if (cfg.force) {\n      animator.duration(cfg.duration);\n    }\n    animator.start(cfg.easing);\n  }\n  return animators;\n}\nfunction copyArrShallow(source, target, len) {\n  for (var i = 0; i < len; i++) {\n    source[i] = target[i];\n  }\n}\nfunction is2DArray(value) {\n  return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n  if (isArrayLike(source[key])) {\n    if (!isArrayLike(target[key])) {\n      target[key] = [];\n    }\n    if (isTypedArray(source[key])) {\n      var len = source[key].length;\n      if (target[key].length !== len) {\n        target[key] = new source[key].constructor(len);\n        copyArrShallow(target[key], source[key], len);\n      }\n    } else {\n      var sourceArr = source[key];\n      var targetArr = target[key];\n      var len0 = sourceArr.length;\n      if (is2DArray(sourceArr)) {\n        var len1 = sourceArr[0].length;\n        for (var i = 0; i < len0; i++) {\n          if (!targetArr[i]) {\n            targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n          } else {\n            copyArrShallow(targetArr[i], sourceArr[i], len1);\n          }\n        }\n      } else {\n        copyArrShallow(targetArr, sourceArr, len0);\n      }\n      targetArr.length = sourceArr.length;\n    }\n  } else {\n    target[key] = source[key];\n  }\n}\nfunction isValueSame(val1, val2) {\n  return val1 === val2 || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n  var len = arr0.length;\n  if (len !== arr1.length) {\n    return false;\n  }\n  for (var i = 0; i < len; i++) {\n    if (arr0[i] !== arr1[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n  var targetKeys = keys(target);\n  var duration = cfg.duration;\n  var delay = cfg.delay;\n  var additive = cfg.additive;\n  var setToFinal = cfg.setToFinal;\n  var animateAll = !isObject(animationProps);\n  var existsAnimators = animatable.animators;\n  var animationKeys = [];\n  for (var k = 0; k < targetKeys.length; k++) {\n    var innerKey = targetKeys[k];\n    var targetVal = target[innerKey];\n    if (targetVal != null && animateObj[innerKey] != null && (animateAll || animationProps[innerKey])) {\n      if (isObject(targetVal) && !isArrayLike(targetVal) && !isGradientObject(targetVal)) {\n        if (topKey) {\n          if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n          }\n          continue;\n        }\n        animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n      } else {\n        animationKeys.push(innerKey);\n      }\n    } else if (!reverse) {\n      animateObj[innerKey] = targetVal;\n      animatable.updateDuringAnimation(topKey);\n      animationKeys.push(innerKey);\n    }\n  }\n  var keyLen = animationKeys.length;\n  if (!additive && keyLen) {\n    for (var i = 0; i < existsAnimators.length; i++) {\n      var animator = existsAnimators[i];\n      if (animator.targetName === topKey) {\n        var allAborted = animator.stopTracks(animationKeys);\n        if (allAborted) {\n          var idx = indexOf(existsAnimators, animator);\n          existsAnimators.splice(idx, 1);\n        }\n      }\n    }\n  }\n  if (!cfg.force) {\n    animationKeys = filter(animationKeys, function (key) {\n      return !isValueSame(target[key], animateObj[key]);\n    });\n    keyLen = animationKeys.length;\n  }\n  if (keyLen > 0 || cfg.force && !animators.length) {\n    var revertedSource = void 0;\n    var reversedTarget = void 0;\n    var sourceClone = void 0;\n    if (reverse) {\n      reversedTarget = {};\n      if (setToFinal) {\n        revertedSource = {};\n      }\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        reversedTarget[innerKey] = animateObj[innerKey];\n        if (setToFinal) {\n          revertedSource[innerKey] = target[innerKey];\n        } else {\n          animateObj[innerKey] = target[innerKey];\n        }\n      }\n    } else if (setToFinal) {\n      sourceClone = {};\n      for (var i = 0; i < keyLen; i++) {\n        var innerKey = animationKeys[i];\n        sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n        copyValue(animateObj, target, innerKey);\n      }\n    }\n    var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) {\n      return animator.targetName === topKey;\n    }) : null);\n    animator.targetName = topKey;\n    if (cfg.scope) {\n      animator.scope = cfg.scope;\n    }\n    if (setToFinal && revertedSource) {\n      animator.whenWithKeys(0, revertedSource, animationKeys);\n    }\n    if (sourceClone) {\n      animator.whenWithKeys(0, sourceClone, animationKeys);\n    }\n    animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n    animatable.addAnimator(animator, topKey);\n    animators.push(animator);\n  }\n}\nexport default Element;", "map": {"version": 3, "names": ["Transformable", "TRANSFORMABLE_PROPS", "Animator", "cloneValue", "BoundingRect", "Eventful", "calculateTextPosition", "parsePercent", "guid", "isObject", "keys", "extend", "indexOf", "logError", "mixin", "isArrayLike", "isTypedArray", "isGradientObject", "filter", "reduce", "LIGHT_LABEL_COLOR", "DARK_LABEL_COLOR", "parse", "stringify", "REDRAW_BIT", "invert", "PRESERVED_NORMAL_STATE", "PRIMARY_STATES_KEYS", "concat", "DEFAULT_ANIMATABLE_MAP", "obj", "key", "ignore", "tmpTextPosCalcRes", "tmpBoundingRect", "tmpInnerTextTrans", "Element", "props", "id", "animators", "currentStates", "states", "_init", "prototype", "attr", "drift", "dx", "dy", "e", "draggable", "m", "transform", "decomposeTransform", "mark<PERSON><PERSON><PERSON>", "beforeUpdate", "afterUpdate", "update", "updateTransform", "__dirty", "updateInnerText", "forceUpdate", "textEl", "_textContent", "textConfig", "isLocal", "local", "innerTransformable", "textAlign", "textVerticalAlign", "textStyleChanged", "parent", "innerOrigin", "copyTransform", "hasPosition", "position", "autoOverflowArea", "layoutRect", "copy", "getBoundingRect", "applyTransform", "x", "y", "align", "verticalAlign", "<PERSON><PERSON><PERSON><PERSON>", "origin", "rotation", "relOriginX", "relOriginY", "width", "height", "originX", "originY", "textOffset", "offset", "innerTextDefaultStyle", "_innerTextDefaultStyle", "overflowRect", "getLocalTransform", "isInside", "inside", "textFill", "textStroke", "autoStroke", "canBeInsideText", "insideFill", "insideStroke", "getInsideTextFill", "getInsideTextStroke", "outsideFill", "outsideStroke", "getOutsideFill", "getOutsideStroke", "fill", "stroke", "setDefaultTextStyle", "dirtyStyle", "__zr", "isDarkMode", "backgroundColor", "getBackgroundColor", "colorArr", "alpha", "isDark", "i", "traverse", "cb", "context", "attrKV", "value", "setTextConfig", "setTextContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extra", "hide", "show", "key<PERSON>r<PERSON><PERSON><PERSON>", "keysArr", "length", "saveCurrentToNormalState", "toState", "_innerSaveToNormal", "normalState", "_normalState", "animator", "fromStateTransition", "__fromStateTransition", "getLoop", "targetName", "target", "saveTo", "_savePrimaryToNormal", "primaryKeys", "hasState", "getState", "name", "ensureState", "clearStates", "noAnimation", "useState", "stateName", "keepCurrentStates", "forceUseHoverLayer", "toNormalState", "hasStates", "animationCfg", "stateTransition", "state", "stateProxy", "useHoverLayer", "hoverLayer", "_toggleHoverLayerFlag", "_applyStateObj", "__inHover", "duration", "textContent", "textGuide", "_textGuide", "push", "_updateAnimationTargets", "useStates", "stateObjects", "len", "notChange", "stateObj", "lastStateObj", "mergedState", "_mergeStates", "join", "slice", "isSilent", "el", "silent", "hostEl", "__host<PERSON><PERSON>get", "ignoreHostSilent", "change<PERSON>arget", "removeState", "idx", "splice", "replaceState", "oldState", "newState", "forceAdd", "newStateExists", "toggleState", "enable", "mergedTextConfig", "transition", "needsRestoreToNormal", "<PERSON><PERSON><PERSON><PERSON>", "hasTransition", "propNeedsTransition", "__changeFinalValue", "_transitionState", "_attachComponent", "componentEl", "process", "env", "NODE_ENV", "Error", "zr", "addSelfToZr", "_detachComponent", "removeSelfFromZr", "getClipPath", "_clipPath", "clipPath", "removeClip<PERSON>ath", "getTextContent", "previousTextContent", "removeTextContent", "cfg", "removeTextConfig", "getTextGuideLine", "setTextGuideLine", "guideLine", "removeTextGuideLine", "refreshHover", "refresh", "dirty", "inHover", "animation", "addAnimator", "removeAnimator", "animate", "loop", "allowDiscreteAnimation", "during", "updateDuringAnimation", "done", "wakeUp", "stopAnimation", "scope", "forwardToLast", "leftAnimators", "stop", "animateTo", "animationProps", "animateFrom", "getPaintRect", "initDefaultProps", "elProto", "type", "isGroup", "dragging", "ignoreClip", "logs", "logDeprecatedError", "xKey", "y<PERSON><PERSON>", "console", "warn", "createLegacyProperty", "privateKey", "Object", "defineProperty", "get", "pos", "enhanceArray", "set", "self", "val", "animatable", "reverse", "animateToShallow", "finishCount", "<PERSON><PERSON><PERSON><PERSON>ed", "cfgDone", "cfgAborted", "aborted", "doneCb", "abortedCb", "percent", "force", "start", "easing", "copyArrShallow", "source", "is2DArray", "copyValue", "constructor", "sourceArr", "targetArr", "len0", "len1", "Array", "call", "isValueSame", "val1", "val2", "is1DArraySame", "arr0", "arr1", "top<PERSON>ey", "animateObj", "targetKeys", "delay", "additive", "setToFinal", "animateAll", "existsAnimators", "animationKeys", "k", "innerKey", "targetVal", "keyLen", "allAborted", "stopTracks", "revertedSource", "<PERSON><PERSON><PERSON><PERSON>", "sourceClone", "when<PERSON>ith<PERSON>eys"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/Element.js"], "sourcesContent": ["import Transformable, { TRANSFORMABLE_PROPS } from './core/Transformable.js';\nimport Animator, { cloneValue } from './animation/Animator.js';\nimport BoundingRect from './core/BoundingRect.js';\nimport Eventful from './core/Eventful.js';\nimport { calculateTextPosition, parsePercent } from './contain/text.js';\nimport { guid, isObject, keys, extend, indexOf, logError, mixin, isArrayLike, isTypedArray, isGradientObject, filter, reduce } from './core/util.js';\nimport { LIGHT_LABEL_COLOR, DARK_LABEL_COLOR } from './config.js';\nimport { parse, stringify } from './tool/color.js';\nimport { REDRAW_BIT } from './graphic/constants.js';\nimport { invert } from './core/matrix.js';\nexport var PRESERVED_NORMAL_STATE = '__zr_normal__';\nvar PRIMARY_STATES_KEYS = TRANSFORMABLE_PROPS.concat(['ignore']);\nvar DEFAULT_ANIMATABLE_MAP = reduce(TRANSFORMABLE_PROPS, function (obj, key) {\n    obj[key] = true;\n    return obj;\n}, { ignore: false });\nvar tmpTextPosCalcRes = {};\nvar tmpBoundingRect = new BoundingRect(0, 0, 0, 0);\nvar tmpInnerTextTrans = [];\nvar Element = (function () {\n    function Element(props) {\n        this.id = guid();\n        this.animators = [];\n        this.currentStates = [];\n        this.states = {};\n        this._init(props);\n    }\n    Element.prototype._init = function (props) {\n        this.attr(props);\n    };\n    Element.prototype.drift = function (dx, dy, e) {\n        switch (this.draggable) {\n            case 'horizontal':\n                dy = 0;\n                break;\n            case 'vertical':\n                dx = 0;\n                break;\n        }\n        var m = this.transform;\n        if (!m) {\n            m = this.transform = [1, 0, 0, 1, 0, 0];\n        }\n        m[4] += dx;\n        m[5] += dy;\n        this.decomposeTransform();\n        this.markRedraw();\n    };\n    Element.prototype.beforeUpdate = function () { };\n    Element.prototype.afterUpdate = function () { };\n    Element.prototype.update = function () {\n        this.updateTransform();\n        if (this.__dirty) {\n            this.updateInnerText();\n        }\n    };\n    Element.prototype.updateInnerText = function (forceUpdate) {\n        var textEl = this._textContent;\n        if (textEl && (!textEl.ignore || forceUpdate)) {\n            if (!this.textConfig) {\n                this.textConfig = {};\n            }\n            var textConfig = this.textConfig;\n            var isLocal = textConfig.local;\n            var innerTransformable = textEl.innerTransformable;\n            var textAlign = void 0;\n            var textVerticalAlign = void 0;\n            var textStyleChanged = false;\n            innerTransformable.parent = isLocal ? this : null;\n            var innerOrigin = false;\n            innerTransformable.copyTransform(textEl);\n            var hasPosition = textConfig.position != null;\n            var autoOverflowArea = textConfig.autoOverflowArea;\n            var layoutRect = void 0;\n            if (autoOverflowArea || hasPosition) {\n                layoutRect = tmpBoundingRect;\n                if (textConfig.layoutRect) {\n                    layoutRect.copy(textConfig.layoutRect);\n                }\n                else {\n                    layoutRect.copy(this.getBoundingRect());\n                }\n                if (!isLocal) {\n                    layoutRect.applyTransform(this.transform);\n                }\n            }\n            if (hasPosition) {\n                if (this.calculateTextPosition) {\n                    this.calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                else {\n                    calculateTextPosition(tmpTextPosCalcRes, textConfig, layoutRect);\n                }\n                innerTransformable.x = tmpTextPosCalcRes.x;\n                innerTransformable.y = tmpTextPosCalcRes.y;\n                textAlign = tmpTextPosCalcRes.align;\n                textVerticalAlign = tmpTextPosCalcRes.verticalAlign;\n                var textOrigin = textConfig.origin;\n                if (textOrigin && textConfig.rotation != null) {\n                    var relOriginX = void 0;\n                    var relOriginY = void 0;\n                    if (textOrigin === 'center') {\n                        relOriginX = layoutRect.width * 0.5;\n                        relOriginY = layoutRect.height * 0.5;\n                    }\n                    else {\n                        relOriginX = parsePercent(textOrigin[0], layoutRect.width);\n                        relOriginY = parsePercent(textOrigin[1], layoutRect.height);\n                    }\n                    innerOrigin = true;\n                    innerTransformable.originX = -innerTransformable.x + relOriginX + (isLocal ? 0 : layoutRect.x);\n                    innerTransformable.originY = -innerTransformable.y + relOriginY + (isLocal ? 0 : layoutRect.y);\n                }\n            }\n            if (textConfig.rotation != null) {\n                innerTransformable.rotation = textConfig.rotation;\n            }\n            var textOffset = textConfig.offset;\n            if (textOffset) {\n                innerTransformable.x += textOffset[0];\n                innerTransformable.y += textOffset[1];\n                if (!innerOrigin) {\n                    innerTransformable.originX = -textOffset[0];\n                    innerTransformable.originY = -textOffset[1];\n                }\n            }\n            var innerTextDefaultStyle = this._innerTextDefaultStyle || (this._innerTextDefaultStyle = {});\n            if (autoOverflowArea) {\n                var overflowRect = innerTextDefaultStyle.overflowRect =\n                    innerTextDefaultStyle.overflowRect || new BoundingRect(0, 0, 0, 0);\n                innerTransformable.getLocalTransform(tmpInnerTextTrans);\n                invert(tmpInnerTextTrans, tmpInnerTextTrans);\n                BoundingRect.copy(overflowRect, layoutRect);\n                overflowRect.applyTransform(tmpInnerTextTrans);\n            }\n            else {\n                innerTextDefaultStyle.overflowRect = null;\n            }\n            var isInside = textConfig.inside == null\n                ? (typeof textConfig.position === 'string' && textConfig.position.indexOf('inside') >= 0)\n                : textConfig.inside;\n            var textFill = void 0;\n            var textStroke = void 0;\n            var autoStroke = void 0;\n            if (isInside && this.canBeInsideText()) {\n                textFill = textConfig.insideFill;\n                textStroke = textConfig.insideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getInsideTextFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getInsideTextStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            else {\n                textFill = textConfig.outsideFill;\n                textStroke = textConfig.outsideStroke;\n                if (textFill == null || textFill === 'auto') {\n                    textFill = this.getOutsideFill();\n                }\n                if (textStroke == null || textStroke === 'auto') {\n                    textStroke = this.getOutsideStroke(textFill);\n                    autoStroke = true;\n                }\n            }\n            textFill = textFill || '#000';\n            if (textFill !== innerTextDefaultStyle.fill\n                || textStroke !== innerTextDefaultStyle.stroke\n                || autoStroke !== innerTextDefaultStyle.autoStroke\n                || textAlign !== innerTextDefaultStyle.align\n                || textVerticalAlign !== innerTextDefaultStyle.verticalAlign) {\n                textStyleChanged = true;\n                innerTextDefaultStyle.fill = textFill;\n                innerTextDefaultStyle.stroke = textStroke;\n                innerTextDefaultStyle.autoStroke = autoStroke;\n                innerTextDefaultStyle.align = textAlign;\n                innerTextDefaultStyle.verticalAlign = textVerticalAlign;\n                textEl.setDefaultTextStyle(innerTextDefaultStyle);\n            }\n            textEl.__dirty |= REDRAW_BIT;\n            if (textStyleChanged) {\n                textEl.dirtyStyle(true);\n            }\n        }\n    };\n    Element.prototype.canBeInsideText = function () {\n        return true;\n    };\n    Element.prototype.getInsideTextFill = function () {\n        return '#fff';\n    };\n    Element.prototype.getInsideTextStroke = function (textFill) {\n        return '#000';\n    };\n    Element.prototype.getOutsideFill = function () {\n        return this.__zr && this.__zr.isDarkMode() ? LIGHT_LABEL_COLOR : DARK_LABEL_COLOR;\n    };\n    Element.prototype.getOutsideStroke = function (textFill) {\n        var backgroundColor = this.__zr && this.__zr.getBackgroundColor();\n        var colorArr = typeof backgroundColor === 'string' && parse(backgroundColor);\n        if (!colorArr) {\n            colorArr = [255, 255, 255, 1];\n        }\n        var alpha = colorArr[3];\n        var isDark = this.__zr.isDarkMode();\n        for (var i = 0; i < 3; i++) {\n            colorArr[i] = colorArr[i] * alpha + (isDark ? 0 : 255) * (1 - alpha);\n        }\n        colorArr[3] = 1;\n        return stringify(colorArr, 'rgba');\n    };\n    Element.prototype.traverse = function (cb, context) { };\n    Element.prototype.attrKV = function (key, value) {\n        if (key === 'textConfig') {\n            this.setTextConfig(value);\n        }\n        else if (key === 'textContent') {\n            this.setTextContent(value);\n        }\n        else if (key === 'clipPath') {\n            this.setClipPath(value);\n        }\n        else if (key === 'extra') {\n            this.extra = this.extra || {};\n            extend(this.extra, value);\n        }\n        else {\n            this[key] = value;\n        }\n    };\n    Element.prototype.hide = function () {\n        this.ignore = true;\n        this.markRedraw();\n    };\n    Element.prototype.show = function () {\n        this.ignore = false;\n        this.markRedraw();\n    };\n    Element.prototype.attr = function (keyOrObj, value) {\n        if (typeof keyOrObj === 'string') {\n            this.attrKV(keyOrObj, value);\n        }\n        else if (isObject(keyOrObj)) {\n            var obj = keyOrObj;\n            var keysArr = keys(obj);\n            for (var i = 0; i < keysArr.length; i++) {\n                var key = keysArr[i];\n                this.attrKV(key, keyOrObj[key]);\n            }\n        }\n        this.markRedraw();\n        return this;\n    };\n    Element.prototype.saveCurrentToNormalState = function (toState) {\n        this._innerSaveToNormal(toState);\n        var normalState = this._normalState;\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            var fromStateTransition = animator.__fromStateTransition;\n            if (animator.getLoop() || fromStateTransition && fromStateTransition !== PRESERVED_NORMAL_STATE) {\n                continue;\n            }\n            var targetName = animator.targetName;\n            var target = targetName\n                ? normalState[targetName] : normalState;\n            animator.saveTo(target);\n        }\n    };\n    Element.prototype._innerSaveToNormal = function (toState) {\n        var normalState = this._normalState;\n        if (!normalState) {\n            normalState = this._normalState = {};\n        }\n        if (toState.textConfig && !normalState.textConfig) {\n            normalState.textConfig = this.textConfig;\n        }\n        this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n    };\n    Element.prototype._savePrimaryToNormal = function (toState, normalState, primaryKeys) {\n        for (var i = 0; i < primaryKeys.length; i++) {\n            var key = primaryKeys[i];\n            if (toState[key] != null && !(key in normalState)) {\n                normalState[key] = this[key];\n            }\n        }\n    };\n    Element.prototype.hasState = function () {\n        return this.currentStates.length > 0;\n    };\n    Element.prototype.getState = function (name) {\n        return this.states[name];\n    };\n    Element.prototype.ensureState = function (name) {\n        var states = this.states;\n        if (!states[name]) {\n            states[name] = {};\n        }\n        return states[name];\n    };\n    Element.prototype.clearStates = function (noAnimation) {\n        this.useState(PRESERVED_NORMAL_STATE, false, noAnimation);\n    };\n    Element.prototype.useState = function (stateName, keepCurrentStates, noAnimation, forceUseHoverLayer) {\n        var toNormalState = stateName === PRESERVED_NORMAL_STATE;\n        var hasStates = this.hasState();\n        if (!hasStates && toNormalState) {\n            return;\n        }\n        var currentStates = this.currentStates;\n        var animationCfg = this.stateTransition;\n        if (indexOf(currentStates, stateName) >= 0 && (keepCurrentStates || currentStates.length === 1)) {\n            return;\n        }\n        var state;\n        if (this.stateProxy && !toNormalState) {\n            state = this.stateProxy(stateName);\n        }\n        if (!state) {\n            state = (this.states && this.states[stateName]);\n        }\n        if (!state && !toNormalState) {\n            logError(\"State \" + stateName + \" not exists.\");\n            return;\n        }\n        if (!toNormalState) {\n            this.saveCurrentToNormalState(state);\n        }\n        var useHoverLayer = !!((state && state.hoverLayer) || forceUseHoverLayer);\n        if (useHoverLayer) {\n            this._toggleHoverLayerFlag(true);\n        }\n        this._applyStateObj(stateName, state, this._normalState, keepCurrentStates, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (textGuide) {\n            textGuide.useState(stateName, keepCurrentStates, noAnimation, useHoverLayer);\n        }\n        if (toNormalState) {\n            this.currentStates = [];\n            this._normalState = {};\n        }\n        else {\n            if (!keepCurrentStates) {\n                this.currentStates = [stateName];\n            }\n            else {\n                this.currentStates.push(stateName);\n            }\n        }\n        this._updateAnimationTargets();\n        this.markRedraw();\n        if (!useHoverLayer && this.__inHover) {\n            this._toggleHoverLayerFlag(false);\n            this.__dirty &= ~REDRAW_BIT;\n        }\n        return state;\n    };\n    Element.prototype.useStates = function (states, noAnimation, forceUseHoverLayer) {\n        if (!states.length) {\n            this.clearStates();\n        }\n        else {\n            var stateObjects = [];\n            var currentStates = this.currentStates;\n            var len = states.length;\n            var notChange = len === currentStates.length;\n            if (notChange) {\n                for (var i = 0; i < len; i++) {\n                    if (states[i] !== currentStates[i]) {\n                        notChange = false;\n                        break;\n                    }\n                }\n            }\n            if (notChange) {\n                return;\n            }\n            for (var i = 0; i < len; i++) {\n                var stateName = states[i];\n                var stateObj = void 0;\n                if (this.stateProxy) {\n                    stateObj = this.stateProxy(stateName, states);\n                }\n                if (!stateObj) {\n                    stateObj = this.states[stateName];\n                }\n                if (stateObj) {\n                    stateObjects.push(stateObj);\n                }\n            }\n            var lastStateObj = stateObjects[len - 1];\n            var useHoverLayer = !!((lastStateObj && lastStateObj.hoverLayer) || forceUseHoverLayer);\n            if (useHoverLayer) {\n                this._toggleHoverLayerFlag(true);\n            }\n            var mergedState = this._mergeStates(stateObjects);\n            var animationCfg = this.stateTransition;\n            this.saveCurrentToNormalState(mergedState);\n            this._applyStateObj(states.join(','), mergedState, this._normalState, false, !noAnimation && !this.__inHover && animationCfg && animationCfg.duration > 0, animationCfg);\n            var textContent = this._textContent;\n            var textGuide = this._textGuide;\n            if (textContent) {\n                textContent.useStates(states, noAnimation, useHoverLayer);\n            }\n            if (textGuide) {\n                textGuide.useStates(states, noAnimation, useHoverLayer);\n            }\n            this._updateAnimationTargets();\n            this.currentStates = states.slice();\n            this.markRedraw();\n            if (!useHoverLayer && this.__inHover) {\n                this._toggleHoverLayerFlag(false);\n                this.__dirty &= ~REDRAW_BIT;\n            }\n        }\n    };\n    Element.prototype.isSilent = function () {\n        var el = this;\n        while (el) {\n            if (el.silent) {\n                return true;\n            }\n            var hostEl = el.__hostTarget;\n            el = hostEl ? (el.ignoreHostSilent ? null : hostEl) : el.parent;\n        }\n        return false;\n    };\n    Element.prototype._updateAnimationTargets = function () {\n        for (var i = 0; i < this.animators.length; i++) {\n            var animator = this.animators[i];\n            if (animator.targetName) {\n                animator.changeTarget(this[animator.targetName]);\n            }\n        }\n    };\n    Element.prototype.removeState = function (state) {\n        var idx = indexOf(this.currentStates, state);\n        if (idx >= 0) {\n            var currentStates = this.currentStates.slice();\n            currentStates.splice(idx, 1);\n            this.useStates(currentStates);\n        }\n    };\n    Element.prototype.replaceState = function (oldState, newState, forceAdd) {\n        var currentStates = this.currentStates.slice();\n        var idx = indexOf(currentStates, oldState);\n        var newStateExists = indexOf(currentStates, newState) >= 0;\n        if (idx >= 0) {\n            if (!newStateExists) {\n                currentStates[idx] = newState;\n            }\n            else {\n                currentStates.splice(idx, 1);\n            }\n        }\n        else if (forceAdd && !newStateExists) {\n            currentStates.push(newState);\n        }\n        this.useStates(currentStates);\n    };\n    Element.prototype.toggleState = function (state, enable) {\n        if (enable) {\n            this.useState(state, true);\n        }\n        else {\n            this.removeState(state);\n        }\n    };\n    Element.prototype._mergeStates = function (states) {\n        var mergedState = {};\n        var mergedTextConfig;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            extend(mergedState, state);\n            if (state.textConfig) {\n                mergedTextConfig = mergedTextConfig || {};\n                extend(mergedTextConfig, state.textConfig);\n            }\n        }\n        if (mergedTextConfig) {\n            mergedState.textConfig = mergedTextConfig;\n        }\n        return mergedState;\n    };\n    Element.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        if (state && state.textConfig) {\n            this.textConfig = extend({}, keepCurrentStates ? this.textConfig : normalState.textConfig);\n            extend(this.textConfig, state.textConfig);\n        }\n        else if (needsRestoreToNormal) {\n            if (normalState.textConfig) {\n                this.textConfig = normalState.textConfig;\n            }\n        }\n        var transitionTarget = {};\n        var hasTransition = false;\n        for (var i = 0; i < PRIMARY_STATES_KEYS.length; i++) {\n            var key = PRIMARY_STATES_KEYS[i];\n            var propNeedsTransition = transition && DEFAULT_ANIMATABLE_MAP[key];\n            if (state && state[key] != null) {\n                if (propNeedsTransition) {\n                    hasTransition = true;\n                    transitionTarget[key] = state[key];\n                }\n                else {\n                    this[key] = state[key];\n                }\n            }\n            else if (needsRestoreToNormal) {\n                if (normalState[key] != null) {\n                    if (propNeedsTransition) {\n                        hasTransition = true;\n                        transitionTarget[key] = normalState[key];\n                    }\n                    else {\n                        this[key] = normalState[key];\n                    }\n                }\n            }\n        }\n        if (!transition) {\n            for (var i = 0; i < this.animators.length; i++) {\n                var animator = this.animators[i];\n                var targetName = animator.targetName;\n                if (!animator.getLoop()) {\n                    animator.__changeFinalValue(targetName\n                        ? (state || normalState)[targetName]\n                        : (state || normalState));\n                }\n            }\n        }\n        if (hasTransition) {\n            this._transitionState(stateName, transitionTarget, animationCfg);\n        }\n    };\n    Element.prototype._attachComponent = function (componentEl) {\n        if (componentEl.__zr && !componentEl.__hostTarget) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Text element has been added to zrender.');\n            }\n            return;\n        }\n        if (componentEl === this) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new Error('Recursive component attachment.');\n            }\n            return;\n        }\n        var zr = this.__zr;\n        if (zr) {\n            componentEl.addSelfToZr(zr);\n        }\n        componentEl.__zr = zr;\n        componentEl.__hostTarget = this;\n    };\n    Element.prototype._detachComponent = function (componentEl) {\n        if (componentEl.__zr) {\n            componentEl.removeSelfFromZr(componentEl.__zr);\n        }\n        componentEl.__zr = null;\n        componentEl.__hostTarget = null;\n    };\n    Element.prototype.getClipPath = function () {\n        return this._clipPath;\n    };\n    Element.prototype.setClipPath = function (clipPath) {\n        if (this._clipPath && this._clipPath !== clipPath) {\n            this.removeClipPath();\n        }\n        this._attachComponent(clipPath);\n        this._clipPath = clipPath;\n        this.markRedraw();\n    };\n    Element.prototype.removeClipPath = function () {\n        var clipPath = this._clipPath;\n        if (clipPath) {\n            this._detachComponent(clipPath);\n            this._clipPath = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextContent = function () {\n        return this._textContent;\n    };\n    Element.prototype.setTextContent = function (textEl) {\n        var previousTextContent = this._textContent;\n        if (previousTextContent === textEl) {\n            return;\n        }\n        if (previousTextContent && previousTextContent !== textEl) {\n            this.removeTextContent();\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (textEl.__zr && !textEl.__hostTarget) {\n                throw new Error('Text element has been added to zrender.');\n            }\n        }\n        textEl.innerTransformable = new Transformable();\n        this._attachComponent(textEl);\n        this._textContent = textEl;\n        this.markRedraw();\n    };\n    Element.prototype.setTextConfig = function (cfg) {\n        if (!this.textConfig) {\n            this.textConfig = {};\n        }\n        extend(this.textConfig, cfg);\n        this.markRedraw();\n    };\n    Element.prototype.removeTextConfig = function () {\n        this.textConfig = null;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextContent = function () {\n        var textEl = this._textContent;\n        if (textEl) {\n            textEl.innerTransformable = null;\n            this._detachComponent(textEl);\n            this._textContent = null;\n            this._innerTextDefaultStyle = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.getTextGuideLine = function () {\n        return this._textGuide;\n    };\n    Element.prototype.setTextGuideLine = function (guideLine) {\n        if (this._textGuide && this._textGuide !== guideLine) {\n            this.removeTextGuideLine();\n        }\n        this._attachComponent(guideLine);\n        this._textGuide = guideLine;\n        this.markRedraw();\n    };\n    Element.prototype.removeTextGuideLine = function () {\n        var textGuide = this._textGuide;\n        if (textGuide) {\n            this._detachComponent(textGuide);\n            this._textGuide = null;\n            this.markRedraw();\n        }\n    };\n    Element.prototype.markRedraw = function () {\n        this.__dirty |= REDRAW_BIT;\n        var zr = this.__zr;\n        if (zr) {\n            if (this.__inHover) {\n                zr.refreshHover();\n            }\n            else {\n                zr.refresh();\n            }\n        }\n        if (this.__hostTarget) {\n            this.__hostTarget.markRedraw();\n        }\n    };\n    Element.prototype.dirty = function () {\n        this.markRedraw();\n    };\n    Element.prototype._toggleHoverLayerFlag = function (inHover) {\n        this.__inHover = inHover;\n        var textContent = this._textContent;\n        var textGuide = this._textGuide;\n        if (textContent) {\n            textContent.__inHover = inHover;\n        }\n        if (textGuide) {\n            textGuide.__inHover = inHover;\n        }\n    };\n    Element.prototype.addSelfToZr = function (zr) {\n        if (this.__zr === zr) {\n            return;\n        }\n        this.__zr = zr;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.addAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.addSelfToZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.addSelfToZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.addSelfToZr(zr);\n        }\n    };\n    Element.prototype.removeSelfFromZr = function (zr) {\n        if (!this.__zr) {\n            return;\n        }\n        this.__zr = null;\n        var animators = this.animators;\n        if (animators) {\n            for (var i = 0; i < animators.length; i++) {\n                zr.animation.removeAnimator(animators[i]);\n            }\n        }\n        if (this._clipPath) {\n            this._clipPath.removeSelfFromZr(zr);\n        }\n        if (this._textContent) {\n            this._textContent.removeSelfFromZr(zr);\n        }\n        if (this._textGuide) {\n            this._textGuide.removeSelfFromZr(zr);\n        }\n    };\n    Element.prototype.animate = function (key, loop, allowDiscreteAnimation) {\n        var target = key ? this[key] : this;\n        if (process.env.NODE_ENV !== 'production') {\n            if (!target) {\n                logError('Property \"'\n                    + key\n                    + '\" is not existed in element '\n                    + this.id);\n                return;\n            }\n        }\n        var animator = new Animator(target, loop, allowDiscreteAnimation);\n        key && (animator.targetName = key);\n        this.addAnimator(animator, key);\n        return animator;\n    };\n    Element.prototype.addAnimator = function (animator, key) {\n        var zr = this.__zr;\n        var el = this;\n        animator.during(function () {\n            el.updateDuringAnimation(key);\n        }).done(function () {\n            var animators = el.animators;\n            var idx = indexOf(animators, animator);\n            if (idx >= 0) {\n                animators.splice(idx, 1);\n            }\n        });\n        this.animators.push(animator);\n        if (zr) {\n            zr.animation.addAnimator(animator);\n        }\n        zr && zr.wakeUp();\n    };\n    Element.prototype.updateDuringAnimation = function (key) {\n        this.markRedraw();\n    };\n    Element.prototype.stopAnimation = function (scope, forwardToLast) {\n        var animators = this.animators;\n        var len = animators.length;\n        var leftAnimators = [];\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            if (!scope || scope === animator.scope) {\n                animator.stop(forwardToLast);\n            }\n            else {\n                leftAnimators.push(animator);\n            }\n        }\n        this.animators = leftAnimators;\n        return this;\n    };\n    Element.prototype.animateTo = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps);\n    };\n    Element.prototype.animateFrom = function (target, cfg, animationProps) {\n        animateTo(this, target, cfg, animationProps, true);\n    };\n    Element.prototype._transitionState = function (stateName, target, cfg, animationProps) {\n        var animators = animateTo(this, target, cfg, animationProps);\n        for (var i = 0; i < animators.length; i++) {\n            animators[i].__fromStateTransition = stateName;\n        }\n    };\n    Element.prototype.getBoundingRect = function () {\n        return null;\n    };\n    Element.prototype.getPaintRect = function () {\n        return null;\n    };\n    Element.initDefaultProps = (function () {\n        var elProto = Element.prototype;\n        elProto.type = 'element';\n        elProto.name = '';\n        elProto.ignore =\n            elProto.silent =\n                elProto.ignoreHostSilent =\n                    elProto.isGroup =\n                        elProto.draggable =\n                            elProto.dragging =\n                                elProto.ignoreClip =\n                                    elProto.__inHover = false;\n        elProto.__dirty = REDRAW_BIT;\n        var logs = {};\n        function logDeprecatedError(key, xKey, yKey) {\n            if (!logs[key + xKey + yKey]) {\n                console.warn(\"DEPRECATED: '\" + key + \"' has been deprecated. use '\" + xKey + \"', '\" + yKey + \"' instead\");\n                logs[key + xKey + yKey] = true;\n            }\n        }\n        function createLegacyProperty(key, privateKey, xKey, yKey) {\n            Object.defineProperty(elProto, key, {\n                get: function () {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    if (!this[privateKey]) {\n                        var pos = this[privateKey] = [];\n                        enhanceArray(this, pos);\n                    }\n                    return this[privateKey];\n                },\n                set: function (pos) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logDeprecatedError(key, xKey, yKey);\n                    }\n                    this[xKey] = pos[0];\n                    this[yKey] = pos[1];\n                    this[privateKey] = pos;\n                    enhanceArray(this, pos);\n                }\n            });\n            function enhanceArray(self, pos) {\n                Object.defineProperty(pos, 0, {\n                    get: function () {\n                        return self[xKey];\n                    },\n                    set: function (val) {\n                        self[xKey] = val;\n                    }\n                });\n                Object.defineProperty(pos, 1, {\n                    get: function () {\n                        return self[yKey];\n                    },\n                    set: function (val) {\n                        self[yKey] = val;\n                    }\n                });\n            }\n        }\n        if (Object.defineProperty) {\n            createLegacyProperty('position', '_legacyPos', 'x', 'y');\n            createLegacyProperty('scale', '_legacyScale', 'scaleX', 'scaleY');\n            createLegacyProperty('origin', '_legacyOrigin', 'originX', 'originY');\n        }\n    })();\n    return Element;\n}());\nmixin(Element, Eventful);\nmixin(Element, Transformable);\nfunction animateTo(animatable, target, cfg, animationProps, reverse) {\n    cfg = cfg || {};\n    var animators = [];\n    animateToShallow(animatable, '', animatable, target, cfg, animationProps, animators, reverse);\n    var finishCount = animators.length;\n    var doneHappened = false;\n    var cfgDone = cfg.done;\n    var cfgAborted = cfg.aborted;\n    var doneCb = function () {\n        doneHappened = true;\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    var abortedCb = function () {\n        finishCount--;\n        if (finishCount <= 0) {\n            doneHappened\n                ? (cfgDone && cfgDone())\n                : (cfgAborted && cfgAborted());\n        }\n    };\n    if (!finishCount) {\n        cfgDone && cfgDone();\n    }\n    if (animators.length > 0 && cfg.during) {\n        animators[0].during(function (target, percent) {\n            cfg.during(percent);\n        });\n    }\n    for (var i = 0; i < animators.length; i++) {\n        var animator = animators[i];\n        if (doneCb) {\n            animator.done(doneCb);\n        }\n        if (abortedCb) {\n            animator.aborted(abortedCb);\n        }\n        if (cfg.force) {\n            animator.duration(cfg.duration);\n        }\n        animator.start(cfg.easing);\n    }\n    return animators;\n}\nfunction copyArrShallow(source, target, len) {\n    for (var i = 0; i < len; i++) {\n        source[i] = target[i];\n    }\n}\nfunction is2DArray(value) {\n    return isArrayLike(value[0]);\n}\nfunction copyValue(target, source, key) {\n    if (isArrayLike(source[key])) {\n        if (!isArrayLike(target[key])) {\n            target[key] = [];\n        }\n        if (isTypedArray(source[key])) {\n            var len = source[key].length;\n            if (target[key].length !== len) {\n                target[key] = new (source[key].constructor)(len);\n                copyArrShallow(target[key], source[key], len);\n            }\n        }\n        else {\n            var sourceArr = source[key];\n            var targetArr = target[key];\n            var len0 = sourceArr.length;\n            if (is2DArray(sourceArr)) {\n                var len1 = sourceArr[0].length;\n                for (var i = 0; i < len0; i++) {\n                    if (!targetArr[i]) {\n                        targetArr[i] = Array.prototype.slice.call(sourceArr[i]);\n                    }\n                    else {\n                        copyArrShallow(targetArr[i], sourceArr[i], len1);\n                    }\n                }\n            }\n            else {\n                copyArrShallow(targetArr, sourceArr, len0);\n            }\n            targetArr.length = sourceArr.length;\n        }\n    }\n    else {\n        target[key] = source[key];\n    }\n}\nfunction isValueSame(val1, val2) {\n    return val1 === val2\n        || isArrayLike(val1) && isArrayLike(val2) && is1DArraySame(val1, val2);\n}\nfunction is1DArraySame(arr0, arr1) {\n    var len = arr0.length;\n    if (len !== arr1.length) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        if (arr0[i] !== arr1[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction animateToShallow(animatable, topKey, animateObj, target, cfg, animationProps, animators, reverse) {\n    var targetKeys = keys(target);\n    var duration = cfg.duration;\n    var delay = cfg.delay;\n    var additive = cfg.additive;\n    var setToFinal = cfg.setToFinal;\n    var animateAll = !isObject(animationProps);\n    var existsAnimators = animatable.animators;\n    var animationKeys = [];\n    for (var k = 0; k < targetKeys.length; k++) {\n        var innerKey = targetKeys[k];\n        var targetVal = target[innerKey];\n        if (targetVal != null && animateObj[innerKey] != null\n            && (animateAll || animationProps[innerKey])) {\n            if (isObject(targetVal)\n                && !isArrayLike(targetVal)\n                && !isGradientObject(targetVal)) {\n                if (topKey) {\n                    if (!reverse) {\n                        animateObj[innerKey] = targetVal;\n                        animatable.updateDuringAnimation(topKey);\n                    }\n                    continue;\n                }\n                animateToShallow(animatable, innerKey, animateObj[innerKey], targetVal, cfg, animationProps && animationProps[innerKey], animators, reverse);\n            }\n            else {\n                animationKeys.push(innerKey);\n            }\n        }\n        else if (!reverse) {\n            animateObj[innerKey] = targetVal;\n            animatable.updateDuringAnimation(topKey);\n            animationKeys.push(innerKey);\n        }\n    }\n    var keyLen = animationKeys.length;\n    if (!additive && keyLen) {\n        for (var i = 0; i < existsAnimators.length; i++) {\n            var animator = existsAnimators[i];\n            if (animator.targetName === topKey) {\n                var allAborted = animator.stopTracks(animationKeys);\n                if (allAborted) {\n                    var idx = indexOf(existsAnimators, animator);\n                    existsAnimators.splice(idx, 1);\n                }\n            }\n        }\n    }\n    if (!cfg.force) {\n        animationKeys = filter(animationKeys, function (key) { return !isValueSame(target[key], animateObj[key]); });\n        keyLen = animationKeys.length;\n    }\n    if (keyLen > 0\n        || (cfg.force && !animators.length)) {\n        var revertedSource = void 0;\n        var reversedTarget = void 0;\n        var sourceClone = void 0;\n        if (reverse) {\n            reversedTarget = {};\n            if (setToFinal) {\n                revertedSource = {};\n            }\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                reversedTarget[innerKey] = animateObj[innerKey];\n                if (setToFinal) {\n                    revertedSource[innerKey] = target[innerKey];\n                }\n                else {\n                    animateObj[innerKey] = target[innerKey];\n                }\n            }\n        }\n        else if (setToFinal) {\n            sourceClone = {};\n            for (var i = 0; i < keyLen; i++) {\n                var innerKey = animationKeys[i];\n                sourceClone[innerKey] = cloneValue(animateObj[innerKey]);\n                copyValue(animateObj, target, innerKey);\n            }\n        }\n        var animator = new Animator(animateObj, false, false, additive ? filter(existsAnimators, function (animator) { return animator.targetName === topKey; }) : null);\n        animator.targetName = topKey;\n        if (cfg.scope) {\n            animator.scope = cfg.scope;\n        }\n        if (setToFinal && revertedSource) {\n            animator.whenWithKeys(0, revertedSource, animationKeys);\n        }\n        if (sourceClone) {\n            animator.whenWithKeys(0, sourceClone, animationKeys);\n        }\n        animator.whenWithKeys(duration == null ? 500 : duration, reverse ? reversedTarget : target, animationKeys).delay(delay || 0);\n        animatable.addAnimator(animator, topKey);\n        animators.push(animator);\n    }\n}\nexport default Element;\n"], "mappings": ";AAAA,OAAOA,aAAa,IAAIC,mBAAmB,QAAQ,yBAAyB;AAC5E,OAAOC,QAAQ,IAAIC,UAAU,QAAQ,yBAAyB;AAC9D,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,mBAAmB;AACvE,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AACpJ,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,aAAa;AACjE,SAASC,KAAK,EAAEC,SAAS,QAAQ,iBAAiB;AAClD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,MAAM,QAAQ,kBAAkB;AACzC,OAAO,IAAIC,sBAAsB,GAAG,eAAe;AACnD,IAAIC,mBAAmB,GAAG1B,mBAAmB,CAAC2B,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;AAChE,IAAIC,sBAAsB,GAAGV,MAAM,CAAClB,mBAAmB,EAAE,UAAU6B,GAAG,EAAEC,GAAG,EAAE;EACzED,GAAG,CAACC,GAAG,CAAC,GAAG,IAAI;EACf,OAAOD,GAAG;AACd,CAAC,EAAE;EAAEE,MAAM,EAAE;AAAM,CAAC,CAAC;AACrB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;AAC1B,IAAIC,eAAe,GAAG,IAAI9B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI+B,iBAAiB,GAAG,EAAE;AAC1B,IAAIC,OAAO,GAAI,YAAY;EACvB,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,EAAE,GAAG9B,IAAI,CAAC,CAAC;IAChB,IAAI,CAAC+B,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;EACrB;EACAD,OAAO,CAACO,SAAS,CAACD,KAAK,GAAG,UAAUL,KAAK,EAAE;IACvC,IAAI,CAACO,IAAI,CAACP,KAAK,CAAC;EACpB,CAAC;EACDD,OAAO,CAACO,SAAS,CAACE,KAAK,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;IAC3C,QAAQ,IAAI,CAACC,SAAS;MAClB,KAAK,YAAY;QACbF,EAAE,GAAG,CAAC;QACN;MACJ,KAAK,UAAU;QACXD,EAAE,GAAG,CAAC;QACN;IACR;IACA,IAAII,CAAC,GAAG,IAAI,CAACC,SAAS;IACtB,IAAI,CAACD,CAAC,EAAE;MACJA,CAAC,GAAG,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3C;IACAD,CAAC,CAAC,CAAC,CAAC,IAAIJ,EAAE;IACVI,CAAC,CAAC,CAAC,CAAC,IAAIH,EAAE;IACV,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACW,YAAY,GAAG,YAAY,CAAE,CAAC;EAChDlB,OAAO,CAACO,SAAS,CAACY,WAAW,GAAG,YAAY,CAAE,CAAC;EAC/CnB,OAAO,CAACO,SAAS,CAACa,MAAM,GAAG,YAAY;IACnC,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;EACJ,CAAC;EACDvB,OAAO,CAACO,SAAS,CAACgB,eAAe,GAAG,UAAUC,WAAW,EAAE;IACvD,IAAIC,MAAM,GAAG,IAAI,CAACC,YAAY;IAC9B,IAAID,MAAM,KAAK,CAACA,MAAM,CAAC7B,MAAM,IAAI4B,WAAW,CAAC,EAAE;MAC3C,IAAI,CAAC,IAAI,CAACG,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;MACxB;MACA,IAAIA,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAIC,OAAO,GAAGD,UAAU,CAACE,KAAK;MAC9B,IAAIC,kBAAkB,GAAGL,MAAM,CAACK,kBAAkB;MAClD,IAAIC,SAAS,GAAG,KAAK,CAAC;MACtB,IAAIC,iBAAiB,GAAG,KAAK,CAAC;MAC9B,IAAIC,gBAAgB,GAAG,KAAK;MAC5BH,kBAAkB,CAACI,MAAM,GAAGN,OAAO,GAAG,IAAI,GAAG,IAAI;MACjD,IAAIO,WAAW,GAAG,KAAK;MACvBL,kBAAkB,CAACM,aAAa,CAACX,MAAM,CAAC;MACxC,IAAIY,WAAW,GAAGV,UAAU,CAACW,QAAQ,IAAI,IAAI;MAC7C,IAAIC,gBAAgB,GAAGZ,UAAU,CAACY,gBAAgB;MAClD,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAID,gBAAgB,IAAIF,WAAW,EAAE;QACjCG,UAAU,GAAG1C,eAAe;QAC5B,IAAI6B,UAAU,CAACa,UAAU,EAAE;UACvBA,UAAU,CAACC,IAAI,CAACd,UAAU,CAACa,UAAU,CAAC;QAC1C,CAAC,MACI;UACDA,UAAU,CAACC,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;QAC3C;QACA,IAAI,CAACd,OAAO,EAAE;UACVY,UAAU,CAACG,cAAc,CAAC,IAAI,CAAC5B,SAAS,CAAC;QAC7C;MACJ;MACA,IAAIsB,WAAW,EAAE;QACb,IAAI,IAAI,CAACnE,qBAAqB,EAAE;UAC5B,IAAI,CAACA,qBAAqB,CAAC2B,iBAAiB,EAAE8B,UAAU,EAAEa,UAAU,CAAC;QACzE,CAAC,MACI;UACDtE,qBAAqB,CAAC2B,iBAAiB,EAAE8B,UAAU,EAAEa,UAAU,CAAC;QACpE;QACAV,kBAAkB,CAACc,CAAC,GAAG/C,iBAAiB,CAAC+C,CAAC;QAC1Cd,kBAAkB,CAACe,CAAC,GAAGhD,iBAAiB,CAACgD,CAAC;QAC1Cd,SAAS,GAAGlC,iBAAiB,CAACiD,KAAK;QACnCd,iBAAiB,GAAGnC,iBAAiB,CAACkD,aAAa;QACnD,IAAIC,UAAU,GAAGrB,UAAU,CAACsB,MAAM;QAClC,IAAID,UAAU,IAAIrB,UAAU,CAACuB,QAAQ,IAAI,IAAI,EAAE;UAC3C,IAAIC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAIJ,UAAU,KAAK,QAAQ,EAAE;YACzBG,UAAU,GAAGX,UAAU,CAACa,KAAK,GAAG,GAAG;YACnCD,UAAU,GAAGZ,UAAU,CAACc,MAAM,GAAG,GAAG;UACxC,CAAC,MACI;YACDH,UAAU,GAAGhF,YAAY,CAAC6E,UAAU,CAAC,CAAC,CAAC,EAAER,UAAU,CAACa,KAAK,CAAC;YAC1DD,UAAU,GAAGjF,YAAY,CAAC6E,UAAU,CAAC,CAAC,CAAC,EAAER,UAAU,CAACc,MAAM,CAAC;UAC/D;UACAnB,WAAW,GAAG,IAAI;UAClBL,kBAAkB,CAACyB,OAAO,GAAG,CAACzB,kBAAkB,CAACc,CAAC,GAAGO,UAAU,IAAIvB,OAAO,GAAG,CAAC,GAAGY,UAAU,CAACI,CAAC,CAAC;UAC9Fd,kBAAkB,CAAC0B,OAAO,GAAG,CAAC1B,kBAAkB,CAACe,CAAC,GAAGO,UAAU,IAAIxB,OAAO,GAAG,CAAC,GAAGY,UAAU,CAACK,CAAC,CAAC;QAClG;MACJ;MACA,IAAIlB,UAAU,CAACuB,QAAQ,IAAI,IAAI,EAAE;QAC7BpB,kBAAkB,CAACoB,QAAQ,GAAGvB,UAAU,CAACuB,QAAQ;MACrD;MACA,IAAIO,UAAU,GAAG9B,UAAU,CAAC+B,MAAM;MAClC,IAAID,UAAU,EAAE;QACZ3B,kBAAkB,CAACc,CAAC,IAAIa,UAAU,CAAC,CAAC,CAAC;QACrC3B,kBAAkB,CAACe,CAAC,IAAIY,UAAU,CAAC,CAAC,CAAC;QACrC,IAAI,CAACtB,WAAW,EAAE;UACdL,kBAAkB,CAACyB,OAAO,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;UAC3C3B,kBAAkB,CAAC0B,OAAO,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;QAC/C;MACJ;MACA,IAAIE,qBAAqB,GAAG,IAAI,CAACC,sBAAsB,KAAK,IAAI,CAACA,sBAAsB,GAAG,CAAC,CAAC,CAAC;MAC7F,IAAIrB,gBAAgB,EAAE;QAClB,IAAIsB,YAAY,GAAGF,qBAAqB,CAACE,YAAY,GACjDF,qBAAqB,CAACE,YAAY,IAAI,IAAI7F,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtE8D,kBAAkB,CAACgC,iBAAiB,CAAC/D,iBAAiB,CAAC;QACvDV,MAAM,CAACU,iBAAiB,EAAEA,iBAAiB,CAAC;QAC5C/B,YAAY,CAACyE,IAAI,CAACoB,YAAY,EAAErB,UAAU,CAAC;QAC3CqB,YAAY,CAAClB,cAAc,CAAC5C,iBAAiB,CAAC;MAClD,CAAC,MACI;QACD4D,qBAAqB,CAACE,YAAY,GAAG,IAAI;MAC7C;MACA,IAAIE,QAAQ,GAAGpC,UAAU,CAACqC,MAAM,IAAI,IAAI,GACjC,OAAOrC,UAAU,CAACW,QAAQ,KAAK,QAAQ,IAAIX,UAAU,CAACW,QAAQ,CAAC9D,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GACtFmD,UAAU,CAACqC,MAAM;MACvB,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;MACvB,IAAIJ,QAAQ,IAAI,IAAI,CAACK,eAAe,CAAC,CAAC,EAAE;QACpCH,QAAQ,GAAGtC,UAAU,CAAC0C,UAAU;QAChCH,UAAU,GAAGvC,UAAU,CAAC2C,YAAY;QACpC,IAAIL,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,MAAM,EAAE;UACzCA,QAAQ,GAAG,IAAI,CAACM,iBAAiB,CAAC,CAAC;QACvC;QACA,IAAIL,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAAE;UAC7CA,UAAU,GAAG,IAAI,CAACM,mBAAmB,CAACP,QAAQ,CAAC;UAC/CE,UAAU,GAAG,IAAI;QACrB;MACJ,CAAC,MACI;QACDF,QAAQ,GAAGtC,UAAU,CAAC8C,WAAW;QACjCP,UAAU,GAAGvC,UAAU,CAAC+C,aAAa;QACrC,IAAIT,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,MAAM,EAAE;UACzCA,QAAQ,GAAG,IAAI,CAACU,cAAc,CAAC,CAAC;QACpC;QACA,IAAIT,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,MAAM,EAAE;UAC7CA,UAAU,GAAG,IAAI,CAACU,gBAAgB,CAACX,QAAQ,CAAC;UAC5CE,UAAU,GAAG,IAAI;QACrB;MACJ;MACAF,QAAQ,GAAGA,QAAQ,IAAI,MAAM;MAC7B,IAAIA,QAAQ,KAAKN,qBAAqB,CAACkB,IAAI,IACpCX,UAAU,KAAKP,qBAAqB,CAACmB,MAAM,IAC3CX,UAAU,KAAKR,qBAAqB,CAACQ,UAAU,IAC/CpC,SAAS,KAAK4B,qBAAqB,CAACb,KAAK,IACzCd,iBAAiB,KAAK2B,qBAAqB,CAACZ,aAAa,EAAE;QAC9Dd,gBAAgB,GAAG,IAAI;QACvB0B,qBAAqB,CAACkB,IAAI,GAAGZ,QAAQ;QACrCN,qBAAqB,CAACmB,MAAM,GAAGZ,UAAU;QACzCP,qBAAqB,CAACQ,UAAU,GAAGA,UAAU;QAC7CR,qBAAqB,CAACb,KAAK,GAAGf,SAAS;QACvC4B,qBAAqB,CAACZ,aAAa,GAAGf,iBAAiB;QACvDP,MAAM,CAACsD,mBAAmB,CAACpB,qBAAqB,CAAC;MACrD;MACAlC,MAAM,CAACH,OAAO,IAAIlC,UAAU;MAC5B,IAAI6C,gBAAgB,EAAE;QAClBR,MAAM,CAACuD,UAAU,CAAC,IAAI,CAAC;MAC3B;IACJ;EACJ,CAAC;EACDhF,OAAO,CAACO,SAAS,CAAC6D,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAI;EACf,CAAC;EACDpE,OAAO,CAACO,SAAS,CAACgE,iBAAiB,GAAG,YAAY;IAC9C,OAAO,MAAM;EACjB,CAAC;EACDvE,OAAO,CAACO,SAAS,CAACiE,mBAAmB,GAAG,UAAUP,QAAQ,EAAE;IACxD,OAAO,MAAM;EACjB,CAAC;EACDjE,OAAO,CAACO,SAAS,CAACoE,cAAc,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACM,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC,CAAC,GAAGlG,iBAAiB,GAAGC,gBAAgB;EACrF,CAAC;EACDe,OAAO,CAACO,SAAS,CAACqE,gBAAgB,GAAG,UAAUX,QAAQ,EAAE;IACrD,IAAIkB,eAAe,GAAG,IAAI,CAACF,IAAI,IAAI,IAAI,CAACA,IAAI,CAACG,kBAAkB,CAAC,CAAC;IACjE,IAAIC,QAAQ,GAAG,OAAOF,eAAe,KAAK,QAAQ,IAAIjG,KAAK,CAACiG,eAAe,CAAC;IAC5E,IAAI,CAACE,QAAQ,EAAE;MACXA,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC;IACA,IAAIC,KAAK,GAAGD,QAAQ,CAAC,CAAC,CAAC;IACvB,IAAIE,MAAM,GAAG,IAAI,CAACN,IAAI,CAACC,UAAU,CAAC,CAAC;IACnC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBH,QAAQ,CAACG,CAAC,CAAC,GAAGH,QAAQ,CAACG,CAAC,CAAC,GAAGF,KAAK,GAAG,CAACC,MAAM,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAGD,KAAK,CAAC;IACxE;IACAD,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,OAAOlG,SAAS,CAACkG,QAAQ,EAAE,MAAM,CAAC;EACtC,CAAC;EACDrF,OAAO,CAACO,SAAS,CAACkF,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE,CAAE,CAAC;EACvD3F,OAAO,CAACO,SAAS,CAACqF,MAAM,GAAG,UAAUjG,GAAG,EAAEkG,KAAK,EAAE;IAC7C,IAAIlG,GAAG,KAAK,YAAY,EAAE;MACtB,IAAI,CAACmG,aAAa,CAACD,KAAK,CAAC;IAC7B,CAAC,MACI,IAAIlG,GAAG,KAAK,aAAa,EAAE;MAC5B,IAAI,CAACoG,cAAc,CAACF,KAAK,CAAC;IAC9B,CAAC,MACI,IAAIlG,GAAG,KAAK,UAAU,EAAE;MACzB,IAAI,CAACqG,WAAW,CAACH,KAAK,CAAC;IAC3B,CAAC,MACI,IAAIlG,GAAG,KAAK,OAAO,EAAE;MACtB,IAAI,CAACsG,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;MAC7B1H,MAAM,CAAC,IAAI,CAAC0H,KAAK,EAAEJ,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAAClG,GAAG,CAAC,GAAGkG,KAAK;IACrB;EACJ,CAAC;EACD7F,OAAO,CAACO,SAAS,CAAC2F,IAAI,GAAG,YAAY;IACjC,IAAI,CAACtG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACqB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4F,IAAI,GAAG,YAAY;IACjC,IAAI,CAACvG,MAAM,GAAG,KAAK;IACnB,IAAI,CAACqB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACC,IAAI,GAAG,UAAU4F,QAAQ,EAAEP,KAAK,EAAE;IAChD,IAAI,OAAOO,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACR,MAAM,CAACQ,QAAQ,EAAEP,KAAK,CAAC;IAChC,CAAC,MACI,IAAIxH,QAAQ,CAAC+H,QAAQ,CAAC,EAAE;MACzB,IAAI1G,GAAG,GAAG0G,QAAQ;MAClB,IAAIC,OAAO,GAAG/H,IAAI,CAACoB,GAAG,CAAC;MACvB,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,OAAO,CAACC,MAAM,EAAEd,CAAC,EAAE,EAAE;QACrC,IAAI7F,GAAG,GAAG0G,OAAO,CAACb,CAAC,CAAC;QACpB,IAAI,CAACI,MAAM,CAACjG,GAAG,EAAEyG,QAAQ,CAACzG,GAAG,CAAC,CAAC;MACnC;IACJ;IACA,IAAI,CAACsB,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACgG,wBAAwB,GAAG,UAAUC,OAAO,EAAE;IAC5D,IAAI,CAACC,kBAAkB,CAACD,OAAO,CAAC;IAChC,IAAIE,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACzG,SAAS,CAACqF,CAAC,CAAC;MAChC,IAAIqB,mBAAmB,GAAGD,QAAQ,CAACE,qBAAqB;MACxD,IAAIF,QAAQ,CAACG,OAAO,CAAC,CAAC,IAAIF,mBAAmB,IAAIA,mBAAmB,KAAKvH,sBAAsB,EAAE;QAC7F;MACJ;MACA,IAAI0H,UAAU,GAAGJ,QAAQ,CAACI,UAAU;MACpC,IAAIC,MAAM,GAAGD,UAAU,GACjBN,WAAW,CAACM,UAAU,CAAC,GAAGN,WAAW;MAC3CE,QAAQ,CAACM,MAAM,CAACD,MAAM,CAAC;IAC3B;EACJ,CAAC;EACDjH,OAAO,CAACO,SAAS,CAACkG,kBAAkB,GAAG,UAAUD,OAAO,EAAE;IACtD,IAAIE,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,IAAI,CAACD,WAAW,EAAE;MACdA,WAAW,GAAG,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACxC;IACA,IAAIH,OAAO,CAAC7E,UAAU,IAAI,CAAC+E,WAAW,CAAC/E,UAAU,EAAE;MAC/C+E,WAAW,CAAC/E,UAAU,GAAG,IAAI,CAACA,UAAU;IAC5C;IACA,IAAI,CAACwF,oBAAoB,CAACX,OAAO,EAAEE,WAAW,EAAEnH,mBAAmB,CAAC;EACxE,CAAC;EACDS,OAAO,CAACO,SAAS,CAAC4G,oBAAoB,GAAG,UAAUX,OAAO,EAAEE,WAAW,EAAEU,WAAW,EAAE;IAClF,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,WAAW,CAACd,MAAM,EAAEd,CAAC,EAAE,EAAE;MACzC,IAAI7F,GAAG,GAAGyH,WAAW,CAAC5B,CAAC,CAAC;MACxB,IAAIgB,OAAO,CAAC7G,GAAG,CAAC,IAAI,IAAI,IAAI,EAAEA,GAAG,IAAI+G,WAAW,CAAC,EAAE;QAC/CA,WAAW,CAAC/G,GAAG,CAAC,GAAG,IAAI,CAACA,GAAG,CAAC;MAChC;IACJ;EACJ,CAAC;EACDK,OAAO,CAACO,SAAS,CAAC8G,QAAQ,GAAG,YAAY;IACrC,OAAO,IAAI,CAACjH,aAAa,CAACkG,MAAM,GAAG,CAAC;EACxC,CAAC;EACDtG,OAAO,CAACO,SAAS,CAAC+G,QAAQ,GAAG,UAAUC,IAAI,EAAE;IACzC,OAAO,IAAI,CAAClH,MAAM,CAACkH,IAAI,CAAC;EAC5B,CAAC;EACDvH,OAAO,CAACO,SAAS,CAACiH,WAAW,GAAG,UAAUD,IAAI,EAAE;IAC5C,IAAIlH,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI,CAACA,MAAM,CAACkH,IAAI,CAAC,EAAE;MACflH,MAAM,CAACkH,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB;IACA,OAAOlH,MAAM,CAACkH,IAAI,CAAC;EACvB,CAAC;EACDvH,OAAO,CAACO,SAAS,CAACkH,WAAW,GAAG,UAAUC,WAAW,EAAE;IACnD,IAAI,CAACC,QAAQ,CAACrI,sBAAsB,EAAE,KAAK,EAAEoI,WAAW,CAAC;EAC7D,CAAC;EACD1H,OAAO,CAACO,SAAS,CAACoH,QAAQ,GAAG,UAAUC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEI,kBAAkB,EAAE;IAClG,IAAIC,aAAa,GAAGH,SAAS,KAAKtI,sBAAsB;IACxD,IAAI0I,SAAS,GAAG,IAAI,CAACX,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACW,SAAS,IAAID,aAAa,EAAE;MAC7B;IACJ;IACA,IAAI3H,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAI6H,YAAY,GAAG,IAAI,CAACC,eAAe;IACvC,IAAI1J,OAAO,CAAC4B,aAAa,EAAEwH,SAAS,CAAC,IAAI,CAAC,KAAKC,iBAAiB,IAAIzH,aAAa,CAACkG,MAAM,KAAK,CAAC,CAAC,EAAE;MAC7F;IACJ;IACA,IAAI6B,KAAK;IACT,IAAI,IAAI,CAACC,UAAU,IAAI,CAACL,aAAa,EAAE;MACnCI,KAAK,GAAG,IAAI,CAACC,UAAU,CAACR,SAAS,CAAC;IACtC;IACA,IAAI,CAACO,KAAK,EAAE;MACRA,KAAK,GAAI,IAAI,CAAC9H,MAAM,IAAI,IAAI,CAACA,MAAM,CAACuH,SAAS,CAAE;IACnD;IACA,IAAI,CAACO,KAAK,IAAI,CAACJ,aAAa,EAAE;MAC1BtJ,QAAQ,CAAC,QAAQ,GAAGmJ,SAAS,GAAG,cAAc,CAAC;MAC/C;IACJ;IACA,IAAI,CAACG,aAAa,EAAE;MAChB,IAAI,CAACxB,wBAAwB,CAAC4B,KAAK,CAAC;IACxC;IACA,IAAIE,aAAa,GAAG,CAAC,EAAGF,KAAK,IAAIA,KAAK,CAACG,UAAU,IAAKR,kBAAkB,CAAC;IACzE,IAAIO,aAAa,EAAE;MACf,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC;IACpC;IACA,IAAI,CAACC,cAAc,CAACZ,SAAS,EAAEO,KAAK,EAAE,IAAI,CAACxB,YAAY,EAAEkB,iBAAiB,EAAE,CAACH,WAAW,IAAI,CAAC,IAAI,CAACe,SAAS,IAAIR,YAAY,IAAIA,YAAY,CAACS,QAAQ,GAAG,CAAC,EAAET,YAAY,CAAC;IACvK,IAAIU,WAAW,GAAG,IAAI,CAACjH,YAAY;IACnC,IAAIkH,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAIF,WAAW,EAAE;MACbA,WAAW,CAAChB,QAAQ,CAACC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEW,aAAa,CAAC;IAClF;IACA,IAAIO,SAAS,EAAE;MACXA,SAAS,CAACjB,QAAQ,CAACC,SAAS,EAAEC,iBAAiB,EAAEH,WAAW,EAAEW,aAAa,CAAC;IAChF;IACA,IAAIN,aAAa,EAAE;MACf,IAAI,CAAC3H,aAAa,GAAG,EAAE;MACvB,IAAI,CAACuG,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACkB,iBAAiB,EAAE;QACpB,IAAI,CAACzH,aAAa,GAAG,CAACwH,SAAS,CAAC;MACpC,CAAC,MACI;QACD,IAAI,CAACxH,aAAa,CAAC0I,IAAI,CAAClB,SAAS,CAAC;MACtC;IACJ;IACA,IAAI,CAACmB,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC9H,UAAU,CAAC,CAAC;IACjB,IAAI,CAACoH,aAAa,IAAI,IAAI,CAACI,SAAS,EAAE;MAClC,IAAI,CAACF,qBAAqB,CAAC,KAAK,CAAC;MACjC,IAAI,CAACjH,OAAO,IAAI,CAAClC,UAAU;IAC/B;IACA,OAAO+I,KAAK;EAChB,CAAC;EACDnI,OAAO,CAACO,SAAS,CAACyI,SAAS,GAAG,UAAU3I,MAAM,EAAEqH,WAAW,EAAEI,kBAAkB,EAAE;IAC7E,IAAI,CAACzH,MAAM,CAACiG,MAAM,EAAE;MAChB,IAAI,CAACmB,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,IAAIwB,YAAY,GAAG,EAAE;MACrB,IAAI7I,aAAa,GAAG,IAAI,CAACA,aAAa;MACtC,IAAI8I,GAAG,GAAG7I,MAAM,CAACiG,MAAM;MACvB,IAAI6C,SAAS,GAAGD,GAAG,KAAK9I,aAAa,CAACkG,MAAM;MAC5C,IAAI6C,SAAS,EAAE;QACX,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;UAC1B,IAAInF,MAAM,CAACmF,CAAC,CAAC,KAAKpF,aAAa,CAACoF,CAAC,CAAC,EAAE;YAChC2D,SAAS,GAAG,KAAK;YACjB;UACJ;QACJ;MACJ;MACA,IAAIA,SAAS,EAAE;QACX;MACJ;MACA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;QAC1B,IAAIoC,SAAS,GAAGvH,MAAM,CAACmF,CAAC,CAAC;QACzB,IAAI4D,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,IAAI,CAAChB,UAAU,EAAE;UACjBgB,QAAQ,GAAG,IAAI,CAAChB,UAAU,CAACR,SAAS,EAAEvH,MAAM,CAAC;QACjD;QACA,IAAI,CAAC+I,QAAQ,EAAE;UACXA,QAAQ,GAAG,IAAI,CAAC/I,MAAM,CAACuH,SAAS,CAAC;QACrC;QACA,IAAIwB,QAAQ,EAAE;UACVH,YAAY,CAACH,IAAI,CAACM,QAAQ,CAAC;QAC/B;MACJ;MACA,IAAIC,YAAY,GAAGJ,YAAY,CAACC,GAAG,GAAG,CAAC,CAAC;MACxC,IAAIb,aAAa,GAAG,CAAC,EAAGgB,YAAY,IAAIA,YAAY,CAACf,UAAU,IAAKR,kBAAkB,CAAC;MACvF,IAAIO,aAAa,EAAE;QACf,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC;MACpC;MACA,IAAIe,WAAW,GAAG,IAAI,CAACC,YAAY,CAACN,YAAY,CAAC;MACjD,IAAIhB,YAAY,GAAG,IAAI,CAACC,eAAe;MACvC,IAAI,CAAC3B,wBAAwB,CAAC+C,WAAW,CAAC;MAC1C,IAAI,CAACd,cAAc,CAACnI,MAAM,CAACmJ,IAAI,CAAC,GAAG,CAAC,EAAEF,WAAW,EAAE,IAAI,CAAC3C,YAAY,EAAE,KAAK,EAAE,CAACe,WAAW,IAAI,CAAC,IAAI,CAACe,SAAS,IAAIR,YAAY,IAAIA,YAAY,CAACS,QAAQ,GAAG,CAAC,EAAET,YAAY,CAAC;MACxK,IAAIU,WAAW,GAAG,IAAI,CAACjH,YAAY;MACnC,IAAIkH,SAAS,GAAG,IAAI,CAACC,UAAU;MAC/B,IAAIF,WAAW,EAAE;QACbA,WAAW,CAACK,SAAS,CAAC3I,MAAM,EAAEqH,WAAW,EAAEW,aAAa,CAAC;MAC7D;MACA,IAAIO,SAAS,EAAE;QACXA,SAAS,CAACI,SAAS,CAAC3I,MAAM,EAAEqH,WAAW,EAAEW,aAAa,CAAC;MAC3D;MACA,IAAI,CAACU,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAAC3I,aAAa,GAAGC,MAAM,CAACoJ,KAAK,CAAC,CAAC;MACnC,IAAI,CAACxI,UAAU,CAAC,CAAC;MACjB,IAAI,CAACoH,aAAa,IAAI,IAAI,CAACI,SAAS,EAAE;QAClC,IAAI,CAACF,qBAAqB,CAAC,KAAK,CAAC;QACjC,IAAI,CAACjH,OAAO,IAAI,CAAClC,UAAU;MAC/B;IACJ;EACJ,CAAC;EACDY,OAAO,CAACO,SAAS,CAACmJ,QAAQ,GAAG,YAAY;IACrC,IAAIC,EAAE,GAAG,IAAI;IACb,OAAOA,EAAE,EAAE;MACP,IAAIA,EAAE,CAACC,MAAM,EAAE;QACX,OAAO,IAAI;MACf;MACA,IAAIC,MAAM,GAAGF,EAAE,CAACG,YAAY;MAC5BH,EAAE,GAAGE,MAAM,GAAIF,EAAE,CAACI,gBAAgB,GAAG,IAAI,GAAGF,MAAM,GAAIF,EAAE,CAACzH,MAAM;IACnE;IACA,OAAO,KAAK;EAChB,CAAC;EACDlC,OAAO,CAACO,SAAS,CAACwI,uBAAuB,GAAG,YAAY;IACpD,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACzG,SAAS,CAACqF,CAAC,CAAC;MAChC,IAAIoB,QAAQ,CAACI,UAAU,EAAE;QACrBJ,QAAQ,CAACoD,YAAY,CAAC,IAAI,CAACpD,QAAQ,CAACI,UAAU,CAAC,CAAC;MACpD;IACJ;EACJ,CAAC;EACDhH,OAAO,CAACO,SAAS,CAAC0J,WAAW,GAAG,UAAU9B,KAAK,EAAE;IAC7C,IAAI+B,GAAG,GAAG1L,OAAO,CAAC,IAAI,CAAC4B,aAAa,EAAE+H,KAAK,CAAC;IAC5C,IAAI+B,GAAG,IAAI,CAAC,EAAE;MACV,IAAI9J,aAAa,GAAG,IAAI,CAACA,aAAa,CAACqJ,KAAK,CAAC,CAAC;MAC9CrJ,aAAa,CAAC+J,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAC5B,IAAI,CAAClB,SAAS,CAAC5I,aAAa,CAAC;IACjC;EACJ,CAAC;EACDJ,OAAO,CAACO,SAAS,CAAC6J,YAAY,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACrE,IAAInK,aAAa,GAAG,IAAI,CAACA,aAAa,CAACqJ,KAAK,CAAC,CAAC;IAC9C,IAAIS,GAAG,GAAG1L,OAAO,CAAC4B,aAAa,EAAEiK,QAAQ,CAAC;IAC1C,IAAIG,cAAc,GAAGhM,OAAO,CAAC4B,aAAa,EAAEkK,QAAQ,CAAC,IAAI,CAAC;IAC1D,IAAIJ,GAAG,IAAI,CAAC,EAAE;MACV,IAAI,CAACM,cAAc,EAAE;QACjBpK,aAAa,CAAC8J,GAAG,CAAC,GAAGI,QAAQ;MACjC,CAAC,MACI;QACDlK,aAAa,CAAC+J,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAChC;IACJ,CAAC,MACI,IAAIK,QAAQ,IAAI,CAACC,cAAc,EAAE;MAClCpK,aAAa,CAAC0I,IAAI,CAACwB,QAAQ,CAAC;IAChC;IACA,IAAI,CAACtB,SAAS,CAAC5I,aAAa,CAAC;EACjC,CAAC;EACDJ,OAAO,CAACO,SAAS,CAACkK,WAAW,GAAG,UAAUtC,KAAK,EAAEuC,MAAM,EAAE;IACrD,IAAIA,MAAM,EAAE;MACR,IAAI,CAAC/C,QAAQ,CAACQ,KAAK,EAAE,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,IAAI,CAAC8B,WAAW,CAAC9B,KAAK,CAAC;IAC3B;EACJ,CAAC;EACDnI,OAAO,CAACO,SAAS,CAACgJ,YAAY,GAAG,UAAUlJ,MAAM,EAAE;IAC/C,IAAIiJ,WAAW,GAAG,CAAC,CAAC;IACpB,IAAIqB,gBAAgB;IACpB,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnF,MAAM,CAACiG,MAAM,EAAEd,CAAC,EAAE,EAAE;MACpC,IAAI2C,KAAK,GAAG9H,MAAM,CAACmF,CAAC,CAAC;MACrBjH,MAAM,CAAC+K,WAAW,EAAEnB,KAAK,CAAC;MAC1B,IAAIA,KAAK,CAACxG,UAAU,EAAE;QAClBgJ,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC,CAAC;QACzCpM,MAAM,CAACoM,gBAAgB,EAAExC,KAAK,CAACxG,UAAU,CAAC;MAC9C;IACJ;IACA,IAAIgJ,gBAAgB,EAAE;MAClBrB,WAAW,CAAC3H,UAAU,GAAGgJ,gBAAgB;IAC7C;IACA,OAAOrB,WAAW;EACtB,CAAC;EACDtJ,OAAO,CAACO,SAAS,CAACiI,cAAc,GAAG,UAAUZ,SAAS,EAAEO,KAAK,EAAEzB,WAAW,EAAEmB,iBAAiB,EAAE+C,UAAU,EAAE3C,YAAY,EAAE;IACrH,IAAI4C,oBAAoB,GAAG,EAAE1C,KAAK,IAAIN,iBAAiB,CAAC;IACxD,IAAIM,KAAK,IAAIA,KAAK,CAACxG,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAGpD,MAAM,CAAC,CAAC,CAAC,EAAEsJ,iBAAiB,GAAG,IAAI,CAAClG,UAAU,GAAG+E,WAAW,CAAC/E,UAAU,CAAC;MAC1FpD,MAAM,CAAC,IAAI,CAACoD,UAAU,EAAEwG,KAAK,CAACxG,UAAU,CAAC;IAC7C,CAAC,MACI,IAAIkJ,oBAAoB,EAAE;MAC3B,IAAInE,WAAW,CAAC/E,UAAU,EAAE;QACxB,IAAI,CAACA,UAAU,GAAG+E,WAAW,CAAC/E,UAAU;MAC5C;IACJ;IACA,IAAImJ,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,KAAK,IAAIvF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjG,mBAAmB,CAAC+G,MAAM,EAAEd,CAAC,EAAE,EAAE;MACjD,IAAI7F,GAAG,GAAGJ,mBAAmB,CAACiG,CAAC,CAAC;MAChC,IAAIwF,mBAAmB,GAAGJ,UAAU,IAAInL,sBAAsB,CAACE,GAAG,CAAC;MACnE,IAAIwI,KAAK,IAAIA,KAAK,CAACxI,GAAG,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAIqL,mBAAmB,EAAE;UACrBD,aAAa,GAAG,IAAI;UACpBD,gBAAgB,CAACnL,GAAG,CAAC,GAAGwI,KAAK,CAACxI,GAAG,CAAC;QACtC,CAAC,MACI;UACD,IAAI,CAACA,GAAG,CAAC,GAAGwI,KAAK,CAACxI,GAAG,CAAC;QAC1B;MACJ,CAAC,MACI,IAAIkL,oBAAoB,EAAE;QAC3B,IAAInE,WAAW,CAAC/G,GAAG,CAAC,IAAI,IAAI,EAAE;UAC1B,IAAIqL,mBAAmB,EAAE;YACrBD,aAAa,GAAG,IAAI;YACpBD,gBAAgB,CAACnL,GAAG,CAAC,GAAG+G,WAAW,CAAC/G,GAAG,CAAC;UAC5C,CAAC,MACI;YACD,IAAI,CAACA,GAAG,CAAC,GAAG+G,WAAW,CAAC/G,GAAG,CAAC;UAChC;QACJ;MACJ;IACJ;IACA,IAAI,CAACiL,UAAU,EAAE;MACb,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;QAC5C,IAAIoB,QAAQ,GAAG,IAAI,CAACzG,SAAS,CAACqF,CAAC,CAAC;QAChC,IAAIwB,UAAU,GAAGJ,QAAQ,CAACI,UAAU;QACpC,IAAI,CAACJ,QAAQ,CAACG,OAAO,CAAC,CAAC,EAAE;UACrBH,QAAQ,CAACqE,kBAAkB,CAACjE,UAAU,GAChC,CAACmB,KAAK,IAAIzB,WAAW,EAAEM,UAAU,CAAC,GACjCmB,KAAK,IAAIzB,WAAY,CAAC;QACjC;MACJ;IACJ;IACA,IAAIqE,aAAa,EAAE;MACf,IAAI,CAACG,gBAAgB,CAACtD,SAAS,EAAEkD,gBAAgB,EAAE7C,YAAY,CAAC;IACpE;EACJ,CAAC;EACDjI,OAAO,CAACO,SAAS,CAAC4K,gBAAgB,GAAG,UAAUC,WAAW,EAAE;IACxD,IAAIA,WAAW,CAACnG,IAAI,IAAI,CAACmG,WAAW,CAACtB,YAAY,EAAE;MAC/C,IAAIuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;MAC9D;MACA;IACJ;IACA,IAAIJ,WAAW,KAAK,IAAI,EAAE;MACtB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACA;IACJ;IACA,IAAIC,EAAE,GAAG,IAAI,CAACxG,IAAI;IAClB,IAAIwG,EAAE,EAAE;MACJL,WAAW,CAACM,WAAW,CAACD,EAAE,CAAC;IAC/B;IACAL,WAAW,CAACnG,IAAI,GAAGwG,EAAE;IACrBL,WAAW,CAACtB,YAAY,GAAG,IAAI;EACnC,CAAC;EACD9J,OAAO,CAACO,SAAS,CAACoL,gBAAgB,GAAG,UAAUP,WAAW,EAAE;IACxD,IAAIA,WAAW,CAACnG,IAAI,EAAE;MAClBmG,WAAW,CAACQ,gBAAgB,CAACR,WAAW,CAACnG,IAAI,CAAC;IAClD;IACAmG,WAAW,CAACnG,IAAI,GAAG,IAAI;IACvBmG,WAAW,CAACtB,YAAY,GAAG,IAAI;EACnC,CAAC;EACD9J,OAAO,CAACO,SAAS,CAACsL,WAAW,GAAG,YAAY;IACxC,OAAO,IAAI,CAACC,SAAS;EACzB,CAAC;EACD9L,OAAO,CAACO,SAAS,CAACyF,WAAW,GAAG,UAAU+F,QAAQ,EAAE;IAChD,IAAI,IAAI,CAACD,SAAS,IAAI,IAAI,CAACA,SAAS,KAAKC,QAAQ,EAAE;MAC/C,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACb,gBAAgB,CAACY,QAAQ,CAAC;IAC/B,IAAI,CAACD,SAAS,GAAGC,QAAQ;IACzB,IAAI,CAAC9K,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACyL,cAAc,GAAG,YAAY;IAC3C,IAAID,QAAQ,GAAG,IAAI,CAACD,SAAS;IAC7B,IAAIC,QAAQ,EAAE;MACV,IAAI,CAACJ,gBAAgB,CAACI,QAAQ,CAAC;MAC/B,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC7K,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC0L,cAAc,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACvK,YAAY;EAC5B,CAAC;EACD1B,OAAO,CAACO,SAAS,CAACwF,cAAc,GAAG,UAAUtE,MAAM,EAAE;IACjD,IAAIyK,mBAAmB,GAAG,IAAI,CAACxK,YAAY;IAC3C,IAAIwK,mBAAmB,KAAKzK,MAAM,EAAE;MAChC;IACJ;IACA,IAAIyK,mBAAmB,IAAIA,mBAAmB,KAAKzK,MAAM,EAAE;MACvD,IAAI,CAAC0K,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAId,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAI9J,MAAM,CAACwD,IAAI,IAAI,CAACxD,MAAM,CAACqI,YAAY,EAAE;QACrC,MAAM,IAAI0B,KAAK,CAAC,yCAAyC,CAAC;MAC9D;IACJ;IACA/J,MAAM,CAACK,kBAAkB,GAAG,IAAIlE,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACuN,gBAAgB,CAAC1J,MAAM,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAGD,MAAM;IAC1B,IAAI,CAACR,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACuF,aAAa,GAAG,UAAUsG,GAAG,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACzK,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;IACxB;IACApD,MAAM,CAAC,IAAI,CAACoD,UAAU,EAAEyK,GAAG,CAAC;IAC5B,IAAI,CAACnL,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC8L,gBAAgB,GAAG,YAAY;IAC7C,IAAI,CAAC1K,UAAU,GAAG,IAAI;IACtB,IAAI,CAACV,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC4L,iBAAiB,GAAG,YAAY;IAC9C,IAAI1K,MAAM,GAAG,IAAI,CAACC,YAAY;IAC9B,IAAID,MAAM,EAAE;MACRA,MAAM,CAACK,kBAAkB,GAAG,IAAI;MAChC,IAAI,CAAC6J,gBAAgB,CAAClK,MAAM,CAAC;MAC7B,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACkC,sBAAsB,GAAG,IAAI;MAClC,IAAI,CAAC3C,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAAC+L,gBAAgB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACzD,UAAU;EAC1B,CAAC;EACD7I,OAAO,CAACO,SAAS,CAACgM,gBAAgB,GAAG,UAAUC,SAAS,EAAE;IACtD,IAAI,IAAI,CAAC3D,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK2D,SAAS,EAAE;MAClD,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI,CAACtB,gBAAgB,CAACqB,SAAS,CAAC;IAChC,IAAI,CAAC3D,UAAU,GAAG2D,SAAS;IAC3B,IAAI,CAACvL,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACkM,mBAAmB,GAAG,YAAY;IAChD,IAAI7D,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAID,SAAS,EAAE;MACX,IAAI,CAAC+C,gBAAgB,CAAC/C,SAAS,CAAC;MAChC,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC5H,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACU,UAAU,GAAG,YAAY;IACvC,IAAI,CAACK,OAAO,IAAIlC,UAAU;IAC1B,IAAIqM,EAAE,GAAG,IAAI,CAACxG,IAAI;IAClB,IAAIwG,EAAE,EAAE;MACJ,IAAI,IAAI,CAAChD,SAAS,EAAE;QAChBgD,EAAE,CAACiB,YAAY,CAAC,CAAC;MACrB,CAAC,MACI;QACDjB,EAAE,CAACkB,OAAO,CAAC,CAAC;MAChB;IACJ;IACA,IAAI,IAAI,CAAC7C,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC7I,UAAU,CAAC,CAAC;IAClC;EACJ,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACqM,KAAK,GAAG,YAAY;IAClC,IAAI,CAAC3L,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACgI,qBAAqB,GAAG,UAAUsE,OAAO,EAAE;IACzD,IAAI,CAACpE,SAAS,GAAGoE,OAAO;IACxB,IAAIlE,WAAW,GAAG,IAAI,CAACjH,YAAY;IACnC,IAAIkH,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAIF,WAAW,EAAE;MACbA,WAAW,CAACF,SAAS,GAAGoE,OAAO;IACnC;IACA,IAAIjE,SAAS,EAAE;MACXA,SAAS,CAACH,SAAS,GAAGoE,OAAO;IACjC;EACJ,CAAC;EACD7M,OAAO,CAACO,SAAS,CAACmL,WAAW,GAAG,UAAUD,EAAE,EAAE;IAC1C,IAAI,IAAI,CAACxG,IAAI,KAAKwG,EAAE,EAAE;MAClB;IACJ;IACA,IAAI,CAACxG,IAAI,GAAGwG,EAAE;IACd,IAAItL,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIA,SAAS,EAAE;MACX,KAAK,IAAIqF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;QACvCiG,EAAE,CAACqB,SAAS,CAACC,WAAW,CAAC5M,SAAS,CAACqF,CAAC,CAAC,CAAC;MAC1C;IACJ;IACA,IAAI,IAAI,CAACsG,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACJ,WAAW,CAACD,EAAE,CAAC;IAClC;IACA,IAAI,IAAI,CAAC/J,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACgK,WAAW,CAACD,EAAE,CAAC;IACrC;IACA,IAAI,IAAI,CAAC5C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC6C,WAAW,CAACD,EAAE,CAAC;IACnC;EACJ,CAAC;EACDzL,OAAO,CAACO,SAAS,CAACqL,gBAAgB,GAAG,UAAUH,EAAE,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACxG,IAAI,EAAE;MACZ;IACJ;IACA,IAAI,CAACA,IAAI,GAAG,IAAI;IAChB,IAAI9E,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIA,SAAS,EAAE;MACX,KAAK,IAAIqF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;QACvCiG,EAAE,CAACqB,SAAS,CAACE,cAAc,CAAC7M,SAAS,CAACqF,CAAC,CAAC,CAAC;MAC7C;IACJ;IACA,IAAI,IAAI,CAACsG,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACF,gBAAgB,CAACH,EAAE,CAAC;IACvC;IACA,IAAI,IAAI,CAAC/J,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACkK,gBAAgB,CAACH,EAAE,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC5C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC+C,gBAAgB,CAACH,EAAE,CAAC;IACxC;EACJ,CAAC;EACDzL,OAAO,CAACO,SAAS,CAAC0M,OAAO,GAAG,UAAUtN,GAAG,EAAEuN,IAAI,EAAEC,sBAAsB,EAAE;IACrE,IAAIlG,MAAM,GAAGtH,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,GAAG,IAAI;IACnC,IAAI0L,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAI,CAACtE,MAAM,EAAE;QACTxI,QAAQ,CAAC,YAAY,GACfkB,GAAG,GACH,8BAA8B,GAC9B,IAAI,CAACO,EAAE,CAAC;QACd;MACJ;IACJ;IACA,IAAI0G,QAAQ,GAAG,IAAI9I,QAAQ,CAACmJ,MAAM,EAAEiG,IAAI,EAAEC,sBAAsB,CAAC;IACjExN,GAAG,KAAKiH,QAAQ,CAACI,UAAU,GAAGrH,GAAG,CAAC;IAClC,IAAI,CAACoN,WAAW,CAACnG,QAAQ,EAAEjH,GAAG,CAAC;IAC/B,OAAOiH,QAAQ;EACnB,CAAC;EACD5G,OAAO,CAACO,SAAS,CAACwM,WAAW,GAAG,UAAUnG,QAAQ,EAAEjH,GAAG,EAAE;IACrD,IAAI8L,EAAE,GAAG,IAAI,CAACxG,IAAI;IAClB,IAAI0E,EAAE,GAAG,IAAI;IACb/C,QAAQ,CAACwG,MAAM,CAAC,YAAY;MACxBzD,EAAE,CAAC0D,qBAAqB,CAAC1N,GAAG,CAAC;IACjC,CAAC,CAAC,CAAC2N,IAAI,CAAC,YAAY;MAChB,IAAInN,SAAS,GAAGwJ,EAAE,CAACxJ,SAAS;MAC5B,IAAI+J,GAAG,GAAG1L,OAAO,CAAC2B,SAAS,EAAEyG,QAAQ,CAAC;MACtC,IAAIsD,GAAG,IAAI,CAAC,EAAE;QACV/J,SAAS,CAACgK,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC/J,SAAS,CAAC2I,IAAI,CAAClC,QAAQ,CAAC;IAC7B,IAAI6E,EAAE,EAAE;MACJA,EAAE,CAACqB,SAAS,CAACC,WAAW,CAACnG,QAAQ,CAAC;IACtC;IACA6E,EAAE,IAAIA,EAAE,CAAC8B,MAAM,CAAC,CAAC;EACrB,CAAC;EACDvN,OAAO,CAACO,SAAS,CAAC8M,qBAAqB,GAAG,UAAU1N,GAAG,EAAE;IACrD,IAAI,CAACsB,UAAU,CAAC,CAAC;EACrB,CAAC;EACDjB,OAAO,CAACO,SAAS,CAACiN,aAAa,GAAG,UAAUC,KAAK,EAAEC,aAAa,EAAE;IAC9D,IAAIvN,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI+I,GAAG,GAAG/I,SAAS,CAACmG,MAAM;IAC1B,IAAIqH,aAAa,GAAG,EAAE;IACtB,KAAK,IAAInI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;MAC1B,IAAIoB,QAAQ,GAAGzG,SAAS,CAACqF,CAAC,CAAC;MAC3B,IAAI,CAACiI,KAAK,IAAIA,KAAK,KAAK7G,QAAQ,CAAC6G,KAAK,EAAE;QACpC7G,QAAQ,CAACgH,IAAI,CAACF,aAAa,CAAC;MAChC,CAAC,MACI;QACDC,aAAa,CAAC7E,IAAI,CAAClC,QAAQ,CAAC;MAChC;IACJ;IACA,IAAI,CAACzG,SAAS,GAAGwN,aAAa;IAC9B,OAAO,IAAI;EACf,CAAC;EACD3N,OAAO,CAACO,SAAS,CAACsN,SAAS,GAAG,UAAU5G,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE;IACjED,SAAS,CAAC,IAAI,EAAE5G,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,CAAC;EAChD,CAAC;EACD9N,OAAO,CAACO,SAAS,CAACwN,WAAW,GAAG,UAAU9G,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE;IACnED,SAAS,CAAC,IAAI,EAAE5G,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE,IAAI,CAAC;EACtD,CAAC;EACD9N,OAAO,CAACO,SAAS,CAAC2K,gBAAgB,GAAG,UAAUtD,SAAS,EAAEX,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE;IACnF,IAAI3N,SAAS,GAAG0N,SAAS,CAAC,IAAI,EAAE5G,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,CAAC;IAC5D,KAAK,IAAItI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;MACvCrF,SAAS,CAACqF,CAAC,CAAC,CAACsB,qBAAqB,GAAGc,SAAS;IAClD;EACJ,CAAC;EACD5H,OAAO,CAACO,SAAS,CAACmC,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAI;EACf,CAAC;EACD1C,OAAO,CAACO,SAAS,CAACyN,YAAY,GAAG,YAAY;IACzC,OAAO,IAAI;EACf,CAAC;EACDhO,OAAO,CAACiO,gBAAgB,GAAI,YAAY;IACpC,IAAIC,OAAO,GAAGlO,OAAO,CAACO,SAAS;IAC/B2N,OAAO,CAACC,IAAI,GAAG,SAAS;IACxBD,OAAO,CAAC3G,IAAI,GAAG,EAAE;IACjB2G,OAAO,CAACtO,MAAM,GACVsO,OAAO,CAACtE,MAAM,GACVsE,OAAO,CAACnE,gBAAgB,GACpBmE,OAAO,CAACE,OAAO,GACXF,OAAO,CAACrN,SAAS,GACbqN,OAAO,CAACG,QAAQ,GACZH,OAAO,CAACI,UAAU,GACdJ,OAAO,CAACzF,SAAS,GAAG,KAAK;IACrDyF,OAAO,CAAC5M,OAAO,GAAGlC,UAAU;IAC5B,IAAImP,IAAI,GAAG,CAAC,CAAC;IACb,SAASC,kBAAkBA,CAAC7O,GAAG,EAAE8O,IAAI,EAAEC,IAAI,EAAE;MACzC,IAAI,CAACH,IAAI,CAAC5O,GAAG,GAAG8O,IAAI,GAAGC,IAAI,CAAC,EAAE;QAC1BC,OAAO,CAACC,IAAI,CAAC,eAAe,GAAGjP,GAAG,GAAG,8BAA8B,GAAG8O,IAAI,GAAG,MAAM,GAAGC,IAAI,GAAG,WAAW,CAAC;QACzGH,IAAI,CAAC5O,GAAG,GAAG8O,IAAI,GAAGC,IAAI,CAAC,GAAG,IAAI;MAClC;IACJ;IACA,SAASG,oBAAoBA,CAAClP,GAAG,EAAEmP,UAAU,EAAEL,IAAI,EAAEC,IAAI,EAAE;MACvDK,MAAM,CAACC,cAAc,CAACd,OAAO,EAAEvO,GAAG,EAAE;QAChCsP,GAAG,EAAE,SAAAA,CAAA,EAAY;UACb,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACvCiD,kBAAkB,CAAC7O,GAAG,EAAE8O,IAAI,EAAEC,IAAI,CAAC;UACvC;UACA,IAAI,CAAC,IAAI,CAACI,UAAU,CAAC,EAAE;YACnB,IAAII,GAAG,GAAG,IAAI,CAACJ,UAAU,CAAC,GAAG,EAAE;YAC/BK,YAAY,CAAC,IAAI,EAAED,GAAG,CAAC;UAC3B;UACA,OAAO,IAAI,CAACJ,UAAU,CAAC;QAC3B,CAAC;QACDM,GAAG,EAAE,SAAAA,CAAUF,GAAG,EAAE;UAChB,IAAI7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACvCiD,kBAAkB,CAAC7O,GAAG,EAAE8O,IAAI,EAAEC,IAAI,CAAC;UACvC;UACA,IAAI,CAACD,IAAI,CAAC,GAAGS,GAAG,CAAC,CAAC,CAAC;UACnB,IAAI,CAACR,IAAI,CAAC,GAAGQ,GAAG,CAAC,CAAC,CAAC;UACnB,IAAI,CAACJ,UAAU,CAAC,GAAGI,GAAG;UACtBC,YAAY,CAAC,IAAI,EAAED,GAAG,CAAC;QAC3B;MACJ,CAAC,CAAC;MACF,SAASC,YAAYA,CAACE,IAAI,EAAEH,GAAG,EAAE;QAC7BH,MAAM,CAACC,cAAc,CAACE,GAAG,EAAE,CAAC,EAAE;UAC1BD,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAOI,IAAI,CAACZ,IAAI,CAAC;UACrB,CAAC;UACDW,GAAG,EAAE,SAAAA,CAAUE,GAAG,EAAE;YAChBD,IAAI,CAACZ,IAAI,CAAC,GAAGa,GAAG;UACpB;QACJ,CAAC,CAAC;QACFP,MAAM,CAACC,cAAc,CAACE,GAAG,EAAE,CAAC,EAAE;UAC1BD,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAOI,IAAI,CAACX,IAAI,CAAC;UACrB,CAAC;UACDU,GAAG,EAAE,SAAAA,CAAUE,GAAG,EAAE;YAChBD,IAAI,CAACX,IAAI,CAAC,GAAGY,GAAG;UACpB;QACJ,CAAC,CAAC;MACN;IACJ;IACA,IAAIP,MAAM,CAACC,cAAc,EAAE;MACvBH,oBAAoB,CAAC,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC;MACxDA,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjEA,oBAAoB,CAAC,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,CAAC;IACzE;EACJ,CAAC,CAAE,CAAC;EACJ,OAAO7O,OAAO;AAClB,CAAC,CAAC,CAAE;AACJtB,KAAK,CAACsB,OAAO,EAAE/B,QAAQ,CAAC;AACxBS,KAAK,CAACsB,OAAO,EAAEpC,aAAa,CAAC;AAC7B,SAASiQ,SAASA,CAAC0B,UAAU,EAAEtI,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE0B,OAAO,EAAE;EACjEpD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIjM,SAAS,GAAG,EAAE;EAClBsP,gBAAgB,CAACF,UAAU,EAAE,EAAE,EAAEA,UAAU,EAAEtI,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE3N,SAAS,EAAEqP,OAAO,CAAC;EAC7F,IAAIE,WAAW,GAAGvP,SAAS,CAACmG,MAAM;EAClC,IAAIqJ,YAAY,GAAG,KAAK;EACxB,IAAIC,OAAO,GAAGxD,GAAG,CAACkB,IAAI;EACtB,IAAIuC,UAAU,GAAGzD,GAAG,CAAC0D,OAAO;EAC5B,IAAIC,MAAM,GAAG,SAAAA,CAAA,EAAY;IACrBJ,YAAY,GAAG,IAAI;IACnBD,WAAW,EAAE;IACb,IAAIA,WAAW,IAAI,CAAC,EAAE;MAClBC,YAAY,GACLC,OAAO,IAAIA,OAAO,CAAC,CAAC,GACpBC,UAAU,IAAIA,UAAU,CAAC,CAAE;IACtC;EACJ,CAAC;EACD,IAAIG,SAAS,GAAG,SAAAA,CAAA,EAAY;IACxBN,WAAW,EAAE;IACb,IAAIA,WAAW,IAAI,CAAC,EAAE;MAClBC,YAAY,GACLC,OAAO,IAAIA,OAAO,CAAC,CAAC,GACpBC,UAAU,IAAIA,UAAU,CAAC,CAAE;IACtC;EACJ,CAAC;EACD,IAAI,CAACH,WAAW,EAAE;IACdE,OAAO,IAAIA,OAAO,CAAC,CAAC;EACxB;EACA,IAAIzP,SAAS,CAACmG,MAAM,GAAG,CAAC,IAAI8F,GAAG,CAACgB,MAAM,EAAE;IACpCjN,SAAS,CAAC,CAAC,CAAC,CAACiN,MAAM,CAAC,UAAUnG,MAAM,EAAEgJ,OAAO,EAAE;MAC3C7D,GAAG,CAACgB,MAAM,CAAC6C,OAAO,CAAC;IACvB,CAAC,CAAC;EACN;EACA,KAAK,IAAIzK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrF,SAAS,CAACmG,MAAM,EAAEd,CAAC,EAAE,EAAE;IACvC,IAAIoB,QAAQ,GAAGzG,SAAS,CAACqF,CAAC,CAAC;IAC3B,IAAIuK,MAAM,EAAE;MACRnJ,QAAQ,CAAC0G,IAAI,CAACyC,MAAM,CAAC;IACzB;IACA,IAAIC,SAAS,EAAE;MACXpJ,QAAQ,CAACkJ,OAAO,CAACE,SAAS,CAAC;IAC/B;IACA,IAAI5D,GAAG,CAAC8D,KAAK,EAAE;MACXtJ,QAAQ,CAAC8B,QAAQ,CAAC0D,GAAG,CAAC1D,QAAQ,CAAC;IACnC;IACA9B,QAAQ,CAACuJ,KAAK,CAAC/D,GAAG,CAACgE,MAAM,CAAC;EAC9B;EACA,OAAOjQ,SAAS;AACpB;AACA,SAASkQ,cAAcA,CAACC,MAAM,EAAErJ,MAAM,EAAEiC,GAAG,EAAE;EACzC,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;IAC1B8K,MAAM,CAAC9K,CAAC,CAAC,GAAGyB,MAAM,CAACzB,CAAC,CAAC;EACzB;AACJ;AACA,SAAS+K,SAASA,CAAC1K,KAAK,EAAE;EACtB,OAAOlH,WAAW,CAACkH,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAAS2K,SAASA,CAACvJ,MAAM,EAAEqJ,MAAM,EAAE3Q,GAAG,EAAE;EACpC,IAAIhB,WAAW,CAAC2R,MAAM,CAAC3Q,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI,CAAChB,WAAW,CAACsI,MAAM,CAACtH,GAAG,CAAC,CAAC,EAAE;MAC3BsH,MAAM,CAACtH,GAAG,CAAC,GAAG,EAAE;IACpB;IACA,IAAIf,YAAY,CAAC0R,MAAM,CAAC3Q,GAAG,CAAC,CAAC,EAAE;MAC3B,IAAIuJ,GAAG,GAAGoH,MAAM,CAAC3Q,GAAG,CAAC,CAAC2G,MAAM;MAC5B,IAAIW,MAAM,CAACtH,GAAG,CAAC,CAAC2G,MAAM,KAAK4C,GAAG,EAAE;QAC5BjC,MAAM,CAACtH,GAAG,CAAC,GAAG,IAAK2Q,MAAM,CAAC3Q,GAAG,CAAC,CAAC8Q,WAAW,CAAEvH,GAAG,CAAC;QAChDmH,cAAc,CAACpJ,MAAM,CAACtH,GAAG,CAAC,EAAE2Q,MAAM,CAAC3Q,GAAG,CAAC,EAAEuJ,GAAG,CAAC;MACjD;IACJ,CAAC,MACI;MACD,IAAIwH,SAAS,GAAGJ,MAAM,CAAC3Q,GAAG,CAAC;MAC3B,IAAIgR,SAAS,GAAG1J,MAAM,CAACtH,GAAG,CAAC;MAC3B,IAAIiR,IAAI,GAAGF,SAAS,CAACpK,MAAM;MAC3B,IAAIiK,SAAS,CAACG,SAAS,CAAC,EAAE;QACtB,IAAIG,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,CAACpK,MAAM;QAC9B,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,IAAI,EAAEpL,CAAC,EAAE,EAAE;UAC3B,IAAI,CAACmL,SAAS,CAACnL,CAAC,CAAC,EAAE;YACfmL,SAAS,CAACnL,CAAC,CAAC,GAAGsL,KAAK,CAACvQ,SAAS,CAACkJ,KAAK,CAACsH,IAAI,CAACL,SAAS,CAAClL,CAAC,CAAC,CAAC;UAC3D,CAAC,MACI;YACD6K,cAAc,CAACM,SAAS,CAACnL,CAAC,CAAC,EAAEkL,SAAS,CAAClL,CAAC,CAAC,EAAEqL,IAAI,CAAC;UACpD;QACJ;MACJ,CAAC,MACI;QACDR,cAAc,CAACM,SAAS,EAAED,SAAS,EAAEE,IAAI,CAAC;MAC9C;MACAD,SAAS,CAACrK,MAAM,GAAGoK,SAAS,CAACpK,MAAM;IACvC;EACJ,CAAC,MACI;IACDW,MAAM,CAACtH,GAAG,CAAC,GAAG2Q,MAAM,CAAC3Q,GAAG,CAAC;EAC7B;AACJ;AACA,SAASqR,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7B,OAAOD,IAAI,KAAKC,IAAI,IACbvS,WAAW,CAACsS,IAAI,CAAC,IAAItS,WAAW,CAACuS,IAAI,CAAC,IAAIC,aAAa,CAACF,IAAI,EAAEC,IAAI,CAAC;AAC9E;AACA,SAASC,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC/B,IAAInI,GAAG,GAAGkI,IAAI,CAAC9K,MAAM;EACrB,IAAI4C,GAAG,KAAKmI,IAAI,CAAC/K,MAAM,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,GAAG,EAAE1D,CAAC,EAAE,EAAE;IAC1B,IAAI4L,IAAI,CAAC5L,CAAC,CAAC,KAAK6L,IAAI,CAAC7L,CAAC,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASiK,gBAAgBA,CAACF,UAAU,EAAE+B,MAAM,EAAEC,UAAU,EAAEtK,MAAM,EAAEmF,GAAG,EAAE0B,cAAc,EAAE3N,SAAS,EAAEqP,OAAO,EAAE;EACvG,IAAIgC,UAAU,GAAGlT,IAAI,CAAC2I,MAAM,CAAC;EAC7B,IAAIyB,QAAQ,GAAG0D,GAAG,CAAC1D,QAAQ;EAC3B,IAAI+I,KAAK,GAAGrF,GAAG,CAACqF,KAAK;EACrB,IAAIC,QAAQ,GAAGtF,GAAG,CAACsF,QAAQ;EAC3B,IAAIC,UAAU,GAAGvF,GAAG,CAACuF,UAAU;EAC/B,IAAIC,UAAU,GAAG,CAACvT,QAAQ,CAACyP,cAAc,CAAC;EAC1C,IAAI+D,eAAe,GAAGtC,UAAU,CAACpP,SAAS;EAC1C,IAAI2R,aAAa,GAAG,EAAE;EACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,UAAU,CAAClL,MAAM,EAAEyL,CAAC,EAAE,EAAE;IACxC,IAAIC,QAAQ,GAAGR,UAAU,CAACO,CAAC,CAAC;IAC5B,IAAIE,SAAS,GAAGhL,MAAM,CAAC+K,QAAQ,CAAC;IAChC,IAAIC,SAAS,IAAI,IAAI,IAAIV,UAAU,CAACS,QAAQ,CAAC,IAAI,IAAI,KAC7CJ,UAAU,IAAI9D,cAAc,CAACkE,QAAQ,CAAC,CAAC,EAAE;MAC7C,IAAI3T,QAAQ,CAAC4T,SAAS,CAAC,IAChB,CAACtT,WAAW,CAACsT,SAAS,CAAC,IACvB,CAACpT,gBAAgB,CAACoT,SAAS,CAAC,EAAE;QACjC,IAAIX,MAAM,EAAE;UACR,IAAI,CAAC9B,OAAO,EAAE;YACV+B,UAAU,CAACS,QAAQ,CAAC,GAAGC,SAAS;YAChC1C,UAAU,CAAClC,qBAAqB,CAACiE,MAAM,CAAC;UAC5C;UACA;QACJ;QACA7B,gBAAgB,CAACF,UAAU,EAAEyC,QAAQ,EAAET,UAAU,CAACS,QAAQ,CAAC,EAAEC,SAAS,EAAE7F,GAAG,EAAE0B,cAAc,IAAIA,cAAc,CAACkE,QAAQ,CAAC,EAAE7R,SAAS,EAAEqP,OAAO,CAAC;MAChJ,CAAC,MACI;QACDsC,aAAa,CAAChJ,IAAI,CAACkJ,QAAQ,CAAC;MAChC;IACJ,CAAC,MACI,IAAI,CAACxC,OAAO,EAAE;MACf+B,UAAU,CAACS,QAAQ,CAAC,GAAGC,SAAS;MAChC1C,UAAU,CAAClC,qBAAqB,CAACiE,MAAM,CAAC;MACxCQ,aAAa,CAAChJ,IAAI,CAACkJ,QAAQ,CAAC;IAChC;EACJ;EACA,IAAIE,MAAM,GAAGJ,aAAa,CAACxL,MAAM;EACjC,IAAI,CAACoL,QAAQ,IAAIQ,MAAM,EAAE;IACrB,KAAK,IAAI1M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqM,eAAe,CAACvL,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC7C,IAAIoB,QAAQ,GAAGiL,eAAe,CAACrM,CAAC,CAAC;MACjC,IAAIoB,QAAQ,CAACI,UAAU,KAAKsK,MAAM,EAAE;QAChC,IAAIa,UAAU,GAAGvL,QAAQ,CAACwL,UAAU,CAACN,aAAa,CAAC;QACnD,IAAIK,UAAU,EAAE;UACZ,IAAIjI,GAAG,GAAG1L,OAAO,CAACqT,eAAe,EAAEjL,QAAQ,CAAC;UAC5CiL,eAAe,CAAC1H,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;QAClC;MACJ;IACJ;EACJ;EACA,IAAI,CAACkC,GAAG,CAAC8D,KAAK,EAAE;IACZ4B,aAAa,GAAGhT,MAAM,CAACgT,aAAa,EAAE,UAAUnS,GAAG,EAAE;MAAE,OAAO,CAACqR,WAAW,CAAC/J,MAAM,CAACtH,GAAG,CAAC,EAAE4R,UAAU,CAAC5R,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;IAC5GuS,MAAM,GAAGJ,aAAa,CAACxL,MAAM;EACjC;EACA,IAAI4L,MAAM,GAAG,CAAC,IACN9F,GAAG,CAAC8D,KAAK,IAAI,CAAC/P,SAAS,CAACmG,MAAO,EAAE;IACrC,IAAI+L,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIC,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIC,WAAW,GAAG,KAAK,CAAC;IACxB,IAAI/C,OAAO,EAAE;MACT8C,cAAc,GAAG,CAAC,CAAC;MACnB,IAAIX,UAAU,EAAE;QACZU,cAAc,GAAG,CAAC,CAAC;MACvB;MACA,KAAK,IAAI7M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0M,MAAM,EAAE1M,CAAC,EAAE,EAAE;QAC7B,IAAIwM,QAAQ,GAAGF,aAAa,CAACtM,CAAC,CAAC;QAC/B8M,cAAc,CAACN,QAAQ,CAAC,GAAGT,UAAU,CAACS,QAAQ,CAAC;QAC/C,IAAIL,UAAU,EAAE;UACZU,cAAc,CAACL,QAAQ,CAAC,GAAG/K,MAAM,CAAC+K,QAAQ,CAAC;QAC/C,CAAC,MACI;UACDT,UAAU,CAACS,QAAQ,CAAC,GAAG/K,MAAM,CAAC+K,QAAQ,CAAC;QAC3C;MACJ;IACJ,CAAC,MACI,IAAIL,UAAU,EAAE;MACjBY,WAAW,GAAG,CAAC,CAAC;MAChB,KAAK,IAAI/M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0M,MAAM,EAAE1M,CAAC,EAAE,EAAE;QAC7B,IAAIwM,QAAQ,GAAGF,aAAa,CAACtM,CAAC,CAAC;QAC/B+M,WAAW,CAACP,QAAQ,CAAC,GAAGjU,UAAU,CAACwT,UAAU,CAACS,QAAQ,CAAC,CAAC;QACxDxB,SAAS,CAACe,UAAU,EAAEtK,MAAM,EAAE+K,QAAQ,CAAC;MAC3C;IACJ;IACA,IAAIpL,QAAQ,GAAG,IAAI9I,QAAQ,CAACyT,UAAU,EAAE,KAAK,EAAE,KAAK,EAAEG,QAAQ,GAAG5S,MAAM,CAAC+S,eAAe,EAAE,UAAUjL,QAAQ,EAAE;MAAE,OAAOA,QAAQ,CAACI,UAAU,KAAKsK,MAAM;IAAE,CAAC,CAAC,GAAG,IAAI,CAAC;IAChK1K,QAAQ,CAACI,UAAU,GAAGsK,MAAM;IAC5B,IAAIlF,GAAG,CAACqB,KAAK,EAAE;MACX7G,QAAQ,CAAC6G,KAAK,GAAGrB,GAAG,CAACqB,KAAK;IAC9B;IACA,IAAIkE,UAAU,IAAIU,cAAc,EAAE;MAC9BzL,QAAQ,CAAC4L,YAAY,CAAC,CAAC,EAAEH,cAAc,EAAEP,aAAa,CAAC;IAC3D;IACA,IAAIS,WAAW,EAAE;MACb3L,QAAQ,CAAC4L,YAAY,CAAC,CAAC,EAAED,WAAW,EAAET,aAAa,CAAC;IACxD;IACAlL,QAAQ,CAAC4L,YAAY,CAAC9J,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAGA,QAAQ,EAAE8G,OAAO,GAAG8C,cAAc,GAAGrL,MAAM,EAAE6K,aAAa,CAAC,CAACL,KAAK,CAACA,KAAK,IAAI,CAAC,CAAC;IAC5HlC,UAAU,CAACxC,WAAW,CAACnG,QAAQ,EAAE0K,MAAM,CAAC;IACxCnR,SAAS,CAAC2I,IAAI,CAAClC,QAAQ,CAAC;EAC5B;AACJ;AACA,eAAe5G,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}