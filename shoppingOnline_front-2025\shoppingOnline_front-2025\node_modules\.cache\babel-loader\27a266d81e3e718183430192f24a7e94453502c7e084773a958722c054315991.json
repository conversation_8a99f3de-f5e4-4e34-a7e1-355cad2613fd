{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { fromPoints } from '../core/bbox.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Point from '../core/Point.js';\nimport { map } from '../core/util.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Sector from '../graphic/shape/Sector.js';\nimport { pathToPolygons } from './convertPath.js';\nimport { clonePath } from './path.js';\nfunction getDividingGrids(dimSize, rowDim, count) {\n  var rowSize = dimSize[rowDim];\n  var columnSize = dimSize[1 - rowDim];\n  var ratio = Math.abs(rowSize / columnSize);\n  var rowCount = Math.ceil(Math.sqrt(ratio * count));\n  var columnCount = Math.floor(count / rowCount);\n  if (columnCount === 0) {\n    columnCount = 1;\n    rowCount = count;\n  }\n  var grids = [];\n  for (var i = 0; i < rowCount; i++) {\n    grids.push(columnCount);\n  }\n  var currentCount = rowCount * columnCount;\n  var remained = count - currentCount;\n  if (remained > 0) {\n    for (var i = 0; i < remained; i++) {\n      grids[i % rowCount] += 1;\n    }\n  }\n  return grids;\n}\nfunction divideSector(sectorShape, count, outShapes) {\n  var r0 = sectorShape.r0;\n  var r = sectorShape.r;\n  var startAngle = sectorShape.startAngle;\n  var endAngle = sectorShape.endAngle;\n  var angle = Math.abs(endAngle - startAngle);\n  var arcLen = angle * r;\n  var deltaR = r - r0;\n  var isAngleRow = arcLen > Math.abs(deltaR);\n  var grids = getDividingGrids([arcLen, deltaR], isAngleRow ? 0 : 1, count);\n  var rowSize = (isAngleRow ? angle : deltaR) / grids.length;\n  for (var row = 0; row < grids.length; row++) {\n    var columnSize = (isAngleRow ? deltaR : angle) / grids[row];\n    for (var column = 0; column < grids[row]; column++) {\n      var newShape = {};\n      if (isAngleRow) {\n        newShape.startAngle = startAngle + rowSize * row;\n        newShape.endAngle = startAngle + rowSize * (row + 1);\n        newShape.r0 = r0 + columnSize * column;\n        newShape.r = r0 + columnSize * (column + 1);\n      } else {\n        newShape.startAngle = startAngle + columnSize * column;\n        newShape.endAngle = startAngle + columnSize * (column + 1);\n        newShape.r0 = r0 + rowSize * row;\n        newShape.r = r0 + rowSize * (row + 1);\n      }\n      newShape.clockwise = sectorShape.clockwise;\n      newShape.cx = sectorShape.cx;\n      newShape.cy = sectorShape.cy;\n      outShapes.push(newShape);\n    }\n  }\n}\nfunction divideRect(rectShape, count, outShapes) {\n  var width = rectShape.width;\n  var height = rectShape.height;\n  var isHorizontalRow = width > height;\n  var grids = getDividingGrids([width, height], isHorizontalRow ? 0 : 1, count);\n  var rowSizeDim = isHorizontalRow ? 'width' : 'height';\n  var columnSizeDim = isHorizontalRow ? 'height' : 'width';\n  var rowDim = isHorizontalRow ? 'x' : 'y';\n  var columnDim = isHorizontalRow ? 'y' : 'x';\n  var rowSize = rectShape[rowSizeDim] / grids.length;\n  for (var row = 0; row < grids.length; row++) {\n    var columnSize = rectShape[columnSizeDim] / grids[row];\n    for (var column = 0; column < grids[row]; column++) {\n      var newShape = {};\n      newShape[rowDim] = row * rowSize;\n      newShape[columnDim] = column * columnSize;\n      newShape[rowSizeDim] = rowSize;\n      newShape[columnSizeDim] = columnSize;\n      newShape.x += rectShape.x;\n      newShape.y += rectShape.y;\n      outShapes.push(newShape);\n    }\n  }\n}\nfunction crossProduct2d(x1, y1, x2, y2) {\n  return x1 * y2 - x2 * y1;\n}\nfunction lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n  var mx = a2x - a1x;\n  var my = a2y - a1y;\n  var nx = b2x - b1x;\n  var ny = b2y - b1y;\n  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n  if (Math.abs(nmCrossProduct) < 1e-6) {\n    return null;\n  }\n  var b1a1x = a1x - b1x;\n  var b1a1y = a1y - b1y;\n  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n  if (p < 0 || p > 1) {\n    return null;\n  }\n  return new Point(p * mx + a1x, p * my + a1y);\n}\nfunction projPtOnLine(pt, lineA, lineB) {\n  var dir = new Point();\n  Point.sub(dir, lineB, lineA);\n  dir.normalize();\n  var dir2 = new Point();\n  Point.sub(dir2, pt, lineA);\n  var len = dir2.dot(dir);\n  return len;\n}\nfunction addToPoly(poly, pt) {\n  var last = poly[poly.length - 1];\n  if (last && last[0] === pt[0] && last[1] === pt[1]) {\n    return;\n  }\n  poly.push(pt);\n}\nfunction splitPolygonByLine(points, lineA, lineB) {\n  var len = points.length;\n  var intersections = [];\n  for (var i = 0; i < len; i++) {\n    var p0 = points[i];\n    var p1 = points[(i + 1) % len];\n    var intersectionPt = lineLineIntersect(p0[0], p0[1], p1[0], p1[1], lineA.x, lineA.y, lineB.x, lineB.y);\n    if (intersectionPt) {\n      intersections.push({\n        projPt: projPtOnLine(intersectionPt, lineA, lineB),\n        pt: intersectionPt,\n        idx: i\n      });\n    }\n  }\n  if (intersections.length < 2) {\n    return [{\n      points: points\n    }, {\n      points: points\n    }];\n  }\n  intersections.sort(function (a, b) {\n    return a.projPt - b.projPt;\n  });\n  var splitPt0 = intersections[0];\n  var splitPt1 = intersections[intersections.length - 1];\n  if (splitPt1.idx < splitPt0.idx) {\n    var tmp = splitPt0;\n    splitPt0 = splitPt1;\n    splitPt1 = tmp;\n  }\n  var splitPt0Arr = [splitPt0.pt.x, splitPt0.pt.y];\n  var splitPt1Arr = [splitPt1.pt.x, splitPt1.pt.y];\n  var newPolyA = [splitPt0Arr];\n  var newPolyB = [splitPt1Arr];\n  for (var i = splitPt0.idx + 1; i <= splitPt1.idx; i++) {\n    addToPoly(newPolyA, points[i].slice());\n  }\n  addToPoly(newPolyA, splitPt1Arr);\n  addToPoly(newPolyA, splitPt0Arr);\n  for (var i = splitPt1.idx + 1; i <= splitPt0.idx + len; i++) {\n    addToPoly(newPolyB, points[i % len].slice());\n  }\n  addToPoly(newPolyB, splitPt0Arr);\n  addToPoly(newPolyB, splitPt1Arr);\n  return [{\n    points: newPolyA\n  }, {\n    points: newPolyB\n  }];\n}\nfunction binaryDividePolygon(polygonShape) {\n  var points = polygonShape.points;\n  var min = [];\n  var max = [];\n  fromPoints(points, min, max);\n  var boundingRect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n  var width = boundingRect.width;\n  var height = boundingRect.height;\n  var x = boundingRect.x;\n  var y = boundingRect.y;\n  var pt0 = new Point();\n  var pt1 = new Point();\n  if (width > height) {\n    pt0.x = pt1.x = x + width / 2;\n    pt0.y = y;\n    pt1.y = y + height;\n  } else {\n    pt0.y = pt1.y = y + height / 2;\n    pt0.x = x;\n    pt1.x = x + width;\n  }\n  return splitPolygonByLine(points, pt0, pt1);\n}\nfunction binaryDivideRecursive(divider, shape, count, out) {\n  if (count === 1) {\n    out.push(shape);\n  } else {\n    var mid = Math.floor(count / 2);\n    var sub = divider(shape);\n    binaryDivideRecursive(divider, sub[0], mid, out);\n    binaryDivideRecursive(divider, sub[1], count - mid, out);\n  }\n  return out;\n}\nexport function clone(path, count) {\n  var paths = [];\n  for (var i = 0; i < count; i++) {\n    paths.push(clonePath(path));\n  }\n  return paths;\n}\nfunction copyPathProps(source, target) {\n  target.setStyle(source.style);\n  target.z = source.z;\n  target.z2 = source.z2;\n  target.zlevel = source.zlevel;\n}\nfunction polygonConvert(points) {\n  var out = [];\n  for (var i = 0; i < points.length;) {\n    out.push([points[i++], points[i++]]);\n  }\n  return out;\n}\nexport function split(path, count) {\n  var outShapes = [];\n  var shape = path.shape;\n  var OutShapeCtor;\n  switch (path.type) {\n    case 'rect':\n      divideRect(shape, count, outShapes);\n      OutShapeCtor = Rect;\n      break;\n    case 'sector':\n      divideSector(shape, count, outShapes);\n      OutShapeCtor = Sector;\n      break;\n    case 'circle':\n      divideSector({\n        r0: 0,\n        r: shape.r,\n        startAngle: 0,\n        endAngle: Math.PI * 2,\n        cx: shape.cx,\n        cy: shape.cy\n      }, count, outShapes);\n      OutShapeCtor = Sector;\n      break;\n    default:\n      var m = path.getComputedTransform();\n      var scale = m ? Math.sqrt(Math.max(m[0] * m[0] + m[1] * m[1], m[2] * m[2] + m[3] * m[3])) : 1;\n      var polygons = map(pathToPolygons(path.getUpdatedPathProxy(), scale), function (poly) {\n        return polygonConvert(poly);\n      });\n      var polygonCount = polygons.length;\n      if (polygonCount === 0) {\n        binaryDivideRecursive(binaryDividePolygon, {\n          points: polygons[0]\n        }, count, outShapes);\n      } else if (polygonCount === count) {\n        for (var i = 0; i < polygonCount; i++) {\n          outShapes.push({\n            points: polygons[i]\n          });\n        }\n      } else {\n        var totalArea_1 = 0;\n        var items = map(polygons, function (poly) {\n          var min = [];\n          var max = [];\n          fromPoints(poly, min, max);\n          var area = (max[1] - min[1]) * (max[0] - min[0]);\n          totalArea_1 += area;\n          return {\n            poly: poly,\n            area: area\n          };\n        });\n        items.sort(function (a, b) {\n          return b.area - a.area;\n        });\n        var left = count;\n        for (var i = 0; i < polygonCount; i++) {\n          var item = items[i];\n          if (left <= 0) {\n            break;\n          }\n          var selfCount = i === polygonCount - 1 ? left : Math.ceil(item.area / totalArea_1 * count);\n          if (selfCount < 0) {\n            continue;\n          }\n          binaryDivideRecursive(binaryDividePolygon, {\n            points: item.poly\n          }, selfCount, outShapes);\n          left -= selfCount;\n        }\n        ;\n      }\n      OutShapeCtor = Polygon;\n      break;\n  }\n  if (!OutShapeCtor) {\n    return clone(path, count);\n  }\n  var out = [];\n  for (var i = 0; i < outShapes.length; i++) {\n    var subPath = new OutShapeCtor();\n    subPath.setShape(outShapes[i]);\n    copyPathProps(path, subPath);\n    out.push(subPath);\n  }\n  return out;\n}", "map": {"version": 3, "names": ["fromPoints", "BoundingRect", "Point", "map", "Polygon", "Rect", "Sector", "pathToPolygons", "<PERSON><PERSON><PERSON>", "getDividingGrids", "dimSize", "row<PERSON><PERSON>", "count", "rowSize", "columnSize", "ratio", "Math", "abs", "rowCount", "ceil", "sqrt", "columnCount", "floor", "grids", "i", "push", "currentCount", "remained", "divideSector", "sectorShape", "outShapes", "r0", "r", "startAngle", "endAngle", "angle", "arcLen", "deltaR", "isAngleRow", "length", "row", "column", "newShape", "clockwise", "cx", "cy", "divideRect", "rectShape", "width", "height", "isHorizontalRow", "rowSizeDim", "columnSizeDim", "columnDim", "x", "y", "crossProduct2d", "x1", "y1", "x2", "y2", "lineLineIntersect", "a1x", "a1y", "a2x", "a2y", "b1x", "b1y", "b2x", "b2y", "mx", "my", "nx", "ny", "nmCrossProduct", "b1a1x", "b1a1y", "p", "projPtOnLine", "pt", "lineA", "lineB", "dir", "sub", "normalize", "dir2", "len", "dot", "addToPoly", "poly", "last", "splitPolygonByLine", "points", "intersections", "p0", "p1", "intersectionPt", "projPt", "idx", "sort", "a", "b", "splitPt0", "splitPt1", "tmp", "splitPt0Arr", "splitPt1Arr", "newPolyA", "newPolyB", "slice", "binaryDividePolygon", "polygonShape", "min", "max", "boundingRect", "pt0", "pt1", "binaryDivideRecursive", "divider", "shape", "out", "mid", "clone", "path", "paths", "copyPathProps", "source", "target", "setStyle", "style", "z", "z2", "zlevel", "polygonConvert", "split", "OutShapeCtor", "type", "PI", "m", "getComputedTransform", "scale", "polygons", "getUpdatedPathProxy", "polygonCount", "totalArea_1", "items", "area", "left", "item", "selfCount", "subPath", "setShape"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/tool/dividePath.js"], "sourcesContent": ["import { fromPoints } from '../core/bbox.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Point from '../core/Point.js';\nimport { map } from '../core/util.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Sector from '../graphic/shape/Sector.js';\nimport { pathToPolygons } from './convertPath.js';\nimport { clonePath } from './path.js';\nfunction getDividingGrids(dimSize, rowDim, count) {\n    var rowSize = dimSize[rowDim];\n    var columnSize = dimSize[1 - rowDim];\n    var ratio = Math.abs(rowSize / columnSize);\n    var rowCount = Math.ceil(Math.sqrt(ratio * count));\n    var columnCount = Math.floor(count / rowCount);\n    if (columnCount === 0) {\n        columnCount = 1;\n        rowCount = count;\n    }\n    var grids = [];\n    for (var i = 0; i < rowCount; i++) {\n        grids.push(columnCount);\n    }\n    var currentCount = rowCount * columnCount;\n    var remained = count - currentCount;\n    if (remained > 0) {\n        for (var i = 0; i < remained; i++) {\n            grids[i % rowCount] += 1;\n        }\n    }\n    return grids;\n}\nfunction divideSector(sectorShape, count, outShapes) {\n    var r0 = sectorShape.r0;\n    var r = sectorShape.r;\n    var startAngle = sectorShape.startAngle;\n    var endAngle = sectorShape.endAngle;\n    var angle = Math.abs(endAngle - startAngle);\n    var arcLen = angle * r;\n    var deltaR = r - r0;\n    var isAngleRow = arcLen > Math.abs(deltaR);\n    var grids = getDividingGrids([arcLen, deltaR], isAngleRow ? 0 : 1, count);\n    var rowSize = (isAngleRow ? angle : deltaR) / grids.length;\n    for (var row = 0; row < grids.length; row++) {\n        var columnSize = (isAngleRow ? deltaR : angle) / grids[row];\n        for (var column = 0; column < grids[row]; column++) {\n            var newShape = {};\n            if (isAngleRow) {\n                newShape.startAngle = startAngle + rowSize * row;\n                newShape.endAngle = startAngle + rowSize * (row + 1);\n                newShape.r0 = r0 + columnSize * column;\n                newShape.r = r0 + columnSize * (column + 1);\n            }\n            else {\n                newShape.startAngle = startAngle + columnSize * column;\n                newShape.endAngle = startAngle + columnSize * (column + 1);\n                newShape.r0 = r0 + rowSize * row;\n                newShape.r = r0 + rowSize * (row + 1);\n            }\n            newShape.clockwise = sectorShape.clockwise;\n            newShape.cx = sectorShape.cx;\n            newShape.cy = sectorShape.cy;\n            outShapes.push(newShape);\n        }\n    }\n}\nfunction divideRect(rectShape, count, outShapes) {\n    var width = rectShape.width;\n    var height = rectShape.height;\n    var isHorizontalRow = width > height;\n    var grids = getDividingGrids([width, height], isHorizontalRow ? 0 : 1, count);\n    var rowSizeDim = isHorizontalRow ? 'width' : 'height';\n    var columnSizeDim = isHorizontalRow ? 'height' : 'width';\n    var rowDim = isHorizontalRow ? 'x' : 'y';\n    var columnDim = isHorizontalRow ? 'y' : 'x';\n    var rowSize = rectShape[rowSizeDim] / grids.length;\n    for (var row = 0; row < grids.length; row++) {\n        var columnSize = rectShape[columnSizeDim] / grids[row];\n        for (var column = 0; column < grids[row]; column++) {\n            var newShape = {};\n            newShape[rowDim] = row * rowSize;\n            newShape[columnDim] = column * columnSize;\n            newShape[rowSizeDim] = rowSize;\n            newShape[columnSizeDim] = columnSize;\n            newShape.x += rectShape.x;\n            newShape.y += rectShape.y;\n            outShapes.push(newShape);\n        }\n    }\n}\nfunction crossProduct2d(x1, y1, x2, y2) {\n    return x1 * y2 - x2 * y1;\n}\nfunction lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n    var mx = a2x - a1x;\n    var my = a2y - a1y;\n    var nx = b2x - b1x;\n    var ny = b2y - b1y;\n    var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n    if (Math.abs(nmCrossProduct) < 1e-6) {\n        return null;\n    }\n    var b1a1x = a1x - b1x;\n    var b1a1y = a1y - b1y;\n    var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n    if (p < 0 || p > 1) {\n        return null;\n    }\n    return new Point(p * mx + a1x, p * my + a1y);\n}\nfunction projPtOnLine(pt, lineA, lineB) {\n    var dir = new Point();\n    Point.sub(dir, lineB, lineA);\n    dir.normalize();\n    var dir2 = new Point();\n    Point.sub(dir2, pt, lineA);\n    var len = dir2.dot(dir);\n    return len;\n}\nfunction addToPoly(poly, pt) {\n    var last = poly[poly.length - 1];\n    if (last && last[0] === pt[0] && last[1] === pt[1]) {\n        return;\n    }\n    poly.push(pt);\n}\nfunction splitPolygonByLine(points, lineA, lineB) {\n    var len = points.length;\n    var intersections = [];\n    for (var i = 0; i < len; i++) {\n        var p0 = points[i];\n        var p1 = points[(i + 1) % len];\n        var intersectionPt = lineLineIntersect(p0[0], p0[1], p1[0], p1[1], lineA.x, lineA.y, lineB.x, lineB.y);\n        if (intersectionPt) {\n            intersections.push({\n                projPt: projPtOnLine(intersectionPt, lineA, lineB),\n                pt: intersectionPt,\n                idx: i\n            });\n        }\n    }\n    if (intersections.length < 2) {\n        return [{ points: points }, { points: points }];\n    }\n    intersections.sort(function (a, b) {\n        return a.projPt - b.projPt;\n    });\n    var splitPt0 = intersections[0];\n    var splitPt1 = intersections[intersections.length - 1];\n    if (splitPt1.idx < splitPt0.idx) {\n        var tmp = splitPt0;\n        splitPt0 = splitPt1;\n        splitPt1 = tmp;\n    }\n    var splitPt0Arr = [splitPt0.pt.x, splitPt0.pt.y];\n    var splitPt1Arr = [splitPt1.pt.x, splitPt1.pt.y];\n    var newPolyA = [splitPt0Arr];\n    var newPolyB = [splitPt1Arr];\n    for (var i = splitPt0.idx + 1; i <= splitPt1.idx; i++) {\n        addToPoly(newPolyA, points[i].slice());\n    }\n    addToPoly(newPolyA, splitPt1Arr);\n    addToPoly(newPolyA, splitPt0Arr);\n    for (var i = splitPt1.idx + 1; i <= splitPt0.idx + len; i++) {\n        addToPoly(newPolyB, points[i % len].slice());\n    }\n    addToPoly(newPolyB, splitPt0Arr);\n    addToPoly(newPolyB, splitPt1Arr);\n    return [{\n            points: newPolyA\n        }, {\n            points: newPolyB\n        }];\n}\nfunction binaryDividePolygon(polygonShape) {\n    var points = polygonShape.points;\n    var min = [];\n    var max = [];\n    fromPoints(points, min, max);\n    var boundingRect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    var width = boundingRect.width;\n    var height = boundingRect.height;\n    var x = boundingRect.x;\n    var y = boundingRect.y;\n    var pt0 = new Point();\n    var pt1 = new Point();\n    if (width > height) {\n        pt0.x = pt1.x = x + width / 2;\n        pt0.y = y;\n        pt1.y = y + height;\n    }\n    else {\n        pt0.y = pt1.y = y + height / 2;\n        pt0.x = x;\n        pt1.x = x + width;\n    }\n    return splitPolygonByLine(points, pt0, pt1);\n}\nfunction binaryDivideRecursive(divider, shape, count, out) {\n    if (count === 1) {\n        out.push(shape);\n    }\n    else {\n        var mid = Math.floor(count / 2);\n        var sub = divider(shape);\n        binaryDivideRecursive(divider, sub[0], mid, out);\n        binaryDivideRecursive(divider, sub[1], count - mid, out);\n    }\n    return out;\n}\nexport function clone(path, count) {\n    var paths = [];\n    for (var i = 0; i < count; i++) {\n        paths.push(clonePath(path));\n    }\n    return paths;\n}\nfunction copyPathProps(source, target) {\n    target.setStyle(source.style);\n    target.z = source.z;\n    target.z2 = source.z2;\n    target.zlevel = source.zlevel;\n}\nfunction polygonConvert(points) {\n    var out = [];\n    for (var i = 0; i < points.length;) {\n        out.push([points[i++], points[i++]]);\n    }\n    return out;\n}\nexport function split(path, count) {\n    var outShapes = [];\n    var shape = path.shape;\n    var OutShapeCtor;\n    switch (path.type) {\n        case 'rect':\n            divideRect(shape, count, outShapes);\n            OutShapeCtor = Rect;\n            break;\n        case 'sector':\n            divideSector(shape, count, outShapes);\n            OutShapeCtor = Sector;\n            break;\n        case 'circle':\n            divideSector({\n                r0: 0, r: shape.r, startAngle: 0, endAngle: Math.PI * 2,\n                cx: shape.cx, cy: shape.cy\n            }, count, outShapes);\n            OutShapeCtor = Sector;\n            break;\n        default:\n            var m = path.getComputedTransform();\n            var scale = m ? Math.sqrt(Math.max(m[0] * m[0] + m[1] * m[1], m[2] * m[2] + m[3] * m[3])) : 1;\n            var polygons = map(pathToPolygons(path.getUpdatedPathProxy(), scale), function (poly) { return polygonConvert(poly); });\n            var polygonCount = polygons.length;\n            if (polygonCount === 0) {\n                binaryDivideRecursive(binaryDividePolygon, {\n                    points: polygons[0]\n                }, count, outShapes);\n            }\n            else if (polygonCount === count) {\n                for (var i = 0; i < polygonCount; i++) {\n                    outShapes.push({\n                        points: polygons[i]\n                    });\n                }\n            }\n            else {\n                var totalArea_1 = 0;\n                var items = map(polygons, function (poly) {\n                    var min = [];\n                    var max = [];\n                    fromPoints(poly, min, max);\n                    var area = (max[1] - min[1]) * (max[0] - min[0]);\n                    totalArea_1 += area;\n                    return { poly: poly, area: area };\n                });\n                items.sort(function (a, b) { return b.area - a.area; });\n                var left = count;\n                for (var i = 0; i < polygonCount; i++) {\n                    var item = items[i];\n                    if (left <= 0) {\n                        break;\n                    }\n                    var selfCount = i === polygonCount - 1\n                        ? left\n                        : Math.ceil(item.area / totalArea_1 * count);\n                    if (selfCount < 0) {\n                        continue;\n                    }\n                    binaryDivideRecursive(binaryDividePolygon, {\n                        points: item.poly\n                    }, selfCount, outShapes);\n                    left -= selfCount;\n                }\n                ;\n            }\n            OutShapeCtor = Polygon;\n            break;\n    }\n    if (!OutShapeCtor) {\n        return clone(path, count);\n    }\n    var out = [];\n    for (var i = 0; i < outShapes.length; i++) {\n        var subPath = new OutShapeCtor();\n        subPath.setShape(outShapes[i]);\n        copyPathProps(path, subPath);\n        out.push(subPath);\n    }\n    return out;\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,GAAG,QAAQ,iBAAiB;AACrC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC9C,IAAIC,OAAO,GAAGH,OAAO,CAACC,MAAM,CAAC;EAC7B,IAAIG,UAAU,GAAGJ,OAAO,CAAC,CAAC,GAAGC,MAAM,CAAC;EACpC,IAAII,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,OAAO,GAAGC,UAAU,CAAC;EAC1C,IAAII,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACH,IAAI,CAACI,IAAI,CAACL,KAAK,GAAGH,KAAK,CAAC,CAAC;EAClD,IAAIS,WAAW,GAAGL,IAAI,CAACM,KAAK,CAACV,KAAK,GAAGM,QAAQ,CAAC;EAC9C,IAAIG,WAAW,KAAK,CAAC,EAAE;IACnBA,WAAW,GAAG,CAAC;IACfH,QAAQ,GAAGN,KAAK;EACpB;EACA,IAAIW,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,QAAQ,EAAEM,CAAC,EAAE,EAAE;IAC/BD,KAAK,CAACE,IAAI,CAACJ,WAAW,CAAC;EAC3B;EACA,IAAIK,YAAY,GAAGR,QAAQ,GAAGG,WAAW;EACzC,IAAIM,QAAQ,GAAGf,KAAK,GAAGc,YAAY;EACnC,IAAIC,QAAQ,GAAG,CAAC,EAAE;IACd,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,QAAQ,EAAEH,CAAC,EAAE,EAAE;MAC/BD,KAAK,CAACC,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;IAC5B;EACJ;EACA,OAAOK,KAAK;AAChB;AACA,SAASK,YAAYA,CAACC,WAAW,EAAEjB,KAAK,EAAEkB,SAAS,EAAE;EACjD,IAAIC,EAAE,GAAGF,WAAW,CAACE,EAAE;EACvB,IAAIC,CAAC,GAAGH,WAAW,CAACG,CAAC;EACrB,IAAIC,UAAU,GAAGJ,WAAW,CAACI,UAAU;EACvC,IAAIC,QAAQ,GAAGL,WAAW,CAACK,QAAQ;EACnC,IAAIC,KAAK,GAAGnB,IAAI,CAACC,GAAG,CAACiB,QAAQ,GAAGD,UAAU,CAAC;EAC3C,IAAIG,MAAM,GAAGD,KAAK,GAAGH,CAAC;EACtB,IAAIK,MAAM,GAAGL,CAAC,GAAGD,EAAE;EACnB,IAAIO,UAAU,GAAGF,MAAM,GAAGpB,IAAI,CAACC,GAAG,CAACoB,MAAM,CAAC;EAC1C,IAAId,KAAK,GAAGd,gBAAgB,CAAC,CAAC2B,MAAM,EAAEC,MAAM,CAAC,EAAEC,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE1B,KAAK,CAAC;EACzE,IAAIC,OAAO,GAAG,CAACyB,UAAU,GAAGH,KAAK,GAAGE,MAAM,IAAId,KAAK,CAACgB,MAAM;EAC1D,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGjB,KAAK,CAACgB,MAAM,EAAEC,GAAG,EAAE,EAAE;IACzC,IAAI1B,UAAU,GAAG,CAACwB,UAAU,GAAGD,MAAM,GAAGF,KAAK,IAAIZ,KAAK,CAACiB,GAAG,CAAC;IAC3D,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGlB,KAAK,CAACiB,GAAG,CAAC,EAAEC,MAAM,EAAE,EAAE;MAChD,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjB,IAAIJ,UAAU,EAAE;QACZI,QAAQ,CAACT,UAAU,GAAGA,UAAU,GAAGpB,OAAO,GAAG2B,GAAG;QAChDE,QAAQ,CAACR,QAAQ,GAAGD,UAAU,GAAGpB,OAAO,IAAI2B,GAAG,GAAG,CAAC,CAAC;QACpDE,QAAQ,CAACX,EAAE,GAAGA,EAAE,GAAGjB,UAAU,GAAG2B,MAAM;QACtCC,QAAQ,CAACV,CAAC,GAAGD,EAAE,GAAGjB,UAAU,IAAI2B,MAAM,GAAG,CAAC,CAAC;MAC/C,CAAC,MACI;QACDC,QAAQ,CAACT,UAAU,GAAGA,UAAU,GAAGnB,UAAU,GAAG2B,MAAM;QACtDC,QAAQ,CAACR,QAAQ,GAAGD,UAAU,GAAGnB,UAAU,IAAI2B,MAAM,GAAG,CAAC,CAAC;QAC1DC,QAAQ,CAACX,EAAE,GAAGA,EAAE,GAAGlB,OAAO,GAAG2B,GAAG;QAChCE,QAAQ,CAACV,CAAC,GAAGD,EAAE,GAAGlB,OAAO,IAAI2B,GAAG,GAAG,CAAC,CAAC;MACzC;MACAE,QAAQ,CAACC,SAAS,GAAGd,WAAW,CAACc,SAAS;MAC1CD,QAAQ,CAACE,EAAE,GAAGf,WAAW,CAACe,EAAE;MAC5BF,QAAQ,CAACG,EAAE,GAAGhB,WAAW,CAACgB,EAAE;MAC5Bf,SAAS,CAACL,IAAI,CAACiB,QAAQ,CAAC;IAC5B;EACJ;AACJ;AACA,SAASI,UAAUA,CAACC,SAAS,EAAEnC,KAAK,EAAEkB,SAAS,EAAE;EAC7C,IAAIkB,KAAK,GAAGD,SAAS,CAACC,KAAK;EAC3B,IAAIC,MAAM,GAAGF,SAAS,CAACE,MAAM;EAC7B,IAAIC,eAAe,GAAGF,KAAK,GAAGC,MAAM;EACpC,IAAI1B,KAAK,GAAGd,gBAAgB,CAAC,CAACuC,KAAK,EAAEC,MAAM,CAAC,EAAEC,eAAe,GAAG,CAAC,GAAG,CAAC,EAAEtC,KAAK,CAAC;EAC7E,IAAIuC,UAAU,GAAGD,eAAe,GAAG,OAAO,GAAG,QAAQ;EACrD,IAAIE,aAAa,GAAGF,eAAe,GAAG,QAAQ,GAAG,OAAO;EACxD,IAAIvC,MAAM,GAAGuC,eAAe,GAAG,GAAG,GAAG,GAAG;EACxC,IAAIG,SAAS,GAAGH,eAAe,GAAG,GAAG,GAAG,GAAG;EAC3C,IAAIrC,OAAO,GAAGkC,SAAS,CAACI,UAAU,CAAC,GAAG5B,KAAK,CAACgB,MAAM;EAClD,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGjB,KAAK,CAACgB,MAAM,EAAEC,GAAG,EAAE,EAAE;IACzC,IAAI1B,UAAU,GAAGiC,SAAS,CAACK,aAAa,CAAC,GAAG7B,KAAK,CAACiB,GAAG,CAAC;IACtD,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGlB,KAAK,CAACiB,GAAG,CAAC,EAAEC,MAAM,EAAE,EAAE;MAChD,IAAIC,QAAQ,GAAG,CAAC,CAAC;MACjBA,QAAQ,CAAC/B,MAAM,CAAC,GAAG6B,GAAG,GAAG3B,OAAO;MAChC6B,QAAQ,CAACW,SAAS,CAAC,GAAGZ,MAAM,GAAG3B,UAAU;MACzC4B,QAAQ,CAACS,UAAU,CAAC,GAAGtC,OAAO;MAC9B6B,QAAQ,CAACU,aAAa,CAAC,GAAGtC,UAAU;MACpC4B,QAAQ,CAACY,CAAC,IAAIP,SAAS,CAACO,CAAC;MACzBZ,QAAQ,CAACa,CAAC,IAAIR,SAAS,CAACQ,CAAC;MACzBzB,SAAS,CAACL,IAAI,CAACiB,QAAQ,CAAC;IAC5B;EACJ;AACJ;AACA,SAASc,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACpC,OAAOH,EAAE,GAAGG,EAAE,GAAGD,EAAE,GAAGD,EAAE;AAC5B;AACA,SAASG,iBAAiBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC/D,IAAIC,EAAE,GAAGN,GAAG,GAAGF,GAAG;EAClB,IAAIS,EAAE,GAAGN,GAAG,GAAGF,GAAG;EAClB,IAAIS,EAAE,GAAGJ,GAAG,GAAGF,GAAG;EAClB,IAAIO,EAAE,GAAGJ,GAAG,GAAGF,GAAG;EAClB,IAAIO,cAAc,GAAGlB,cAAc,CAACgB,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,CAAC;EACnD,IAAIvD,IAAI,CAACC,GAAG,CAACyD,cAAc,CAAC,GAAG,IAAI,EAAE;IACjC,OAAO,IAAI;EACf;EACA,IAAIC,KAAK,GAAGb,GAAG,GAAGI,GAAG;EACrB,IAAIU,KAAK,GAAGb,GAAG,GAAGI,GAAG;EACrB,IAAIU,CAAC,GAAGrB,cAAc,CAACmB,KAAK,EAAEC,KAAK,EAAEJ,EAAE,EAAEC,EAAE,CAAC,GAAGC,cAAc;EAC7D,IAAIG,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAChB,OAAO,IAAI;EACf;EACA,OAAO,IAAI3E,KAAK,CAAC2E,CAAC,GAAGP,EAAE,GAAGR,GAAG,EAAEe,CAAC,GAAGN,EAAE,GAAGR,GAAG,CAAC;AAChD;AACA,SAASe,YAAYA,CAACC,EAAE,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACpC,IAAIC,GAAG,GAAG,IAAIhF,KAAK,CAAC,CAAC;EACrBA,KAAK,CAACiF,GAAG,CAACD,GAAG,EAAED,KAAK,EAAED,KAAK,CAAC;EAC5BE,GAAG,CAACE,SAAS,CAAC,CAAC;EACf,IAAIC,IAAI,GAAG,IAAInF,KAAK,CAAC,CAAC;EACtBA,KAAK,CAACiF,GAAG,CAACE,IAAI,EAAEN,EAAE,EAAEC,KAAK,CAAC;EAC1B,IAAIM,GAAG,GAAGD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC;EACvB,OAAOI,GAAG;AACd;AACA,SAASE,SAASA,CAACC,IAAI,EAAEV,EAAE,EAAE;EACzB,IAAIW,IAAI,GAAGD,IAAI,CAACA,IAAI,CAAClD,MAAM,GAAG,CAAC,CAAC;EAChC,IAAImD,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAKX,EAAE,CAAC,CAAC,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC,KAAKX,EAAE,CAAC,CAAC,CAAC,EAAE;IAChD;EACJ;EACAU,IAAI,CAAChE,IAAI,CAACsD,EAAE,CAAC;AACjB;AACA,SAASY,kBAAkBA,CAACC,MAAM,EAAEZ,KAAK,EAAEC,KAAK,EAAE;EAC9C,IAAIK,GAAG,GAAGM,MAAM,CAACrD,MAAM;EACvB,IAAIsD,aAAa,GAAG,EAAE;EACtB,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,GAAG,EAAE9D,CAAC,EAAE,EAAE;IAC1B,IAAIsE,EAAE,GAAGF,MAAM,CAACpE,CAAC,CAAC;IAClB,IAAIuE,EAAE,GAAGH,MAAM,CAAC,CAACpE,CAAC,GAAG,CAAC,IAAI8D,GAAG,CAAC;IAC9B,IAAIU,cAAc,GAAGnC,iBAAiB,CAACiC,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEf,KAAK,CAAC1B,CAAC,EAAE0B,KAAK,CAACzB,CAAC,EAAE0B,KAAK,CAAC3B,CAAC,EAAE2B,KAAK,CAAC1B,CAAC,CAAC;IACtG,IAAIyC,cAAc,EAAE;MAChBH,aAAa,CAACpE,IAAI,CAAC;QACfwE,MAAM,EAAEnB,YAAY,CAACkB,cAAc,EAAEhB,KAAK,EAAEC,KAAK,CAAC;QAClDF,EAAE,EAAEiB,cAAc;QAClBE,GAAG,EAAE1E;MACT,CAAC,CAAC;IACN;EACJ;EACA,IAAIqE,aAAa,CAACtD,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAO,CAAC;MAAEqD,MAAM,EAAEA;IAAO,CAAC,EAAE;MAAEA,MAAM,EAAEA;IAAO,CAAC,CAAC;EACnD;EACAC,aAAa,CAACM,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC/B,OAAOD,CAAC,CAACH,MAAM,GAAGI,CAAC,CAACJ,MAAM;EAC9B,CAAC,CAAC;EACF,IAAIK,QAAQ,GAAGT,aAAa,CAAC,CAAC,CAAC;EAC/B,IAAIU,QAAQ,GAAGV,aAAa,CAACA,aAAa,CAACtD,MAAM,GAAG,CAAC,CAAC;EACtD,IAAIgE,QAAQ,CAACL,GAAG,GAAGI,QAAQ,CAACJ,GAAG,EAAE;IAC7B,IAAIM,GAAG,GAAGF,QAAQ;IAClBA,QAAQ,GAAGC,QAAQ;IACnBA,QAAQ,GAAGC,GAAG;EAClB;EACA,IAAIC,WAAW,GAAG,CAACH,QAAQ,CAACvB,EAAE,CAACzB,CAAC,EAAEgD,QAAQ,CAACvB,EAAE,CAACxB,CAAC,CAAC;EAChD,IAAImD,WAAW,GAAG,CAACH,QAAQ,CAACxB,EAAE,CAACzB,CAAC,EAAEiD,QAAQ,CAACxB,EAAE,CAACxB,CAAC,CAAC;EAChD,IAAIoD,QAAQ,GAAG,CAACF,WAAW,CAAC;EAC5B,IAAIG,QAAQ,GAAG,CAACF,WAAW,CAAC;EAC5B,KAAK,IAAIlF,CAAC,GAAG8E,QAAQ,CAACJ,GAAG,GAAG,CAAC,EAAE1E,CAAC,IAAI+E,QAAQ,CAACL,GAAG,EAAE1E,CAAC,EAAE,EAAE;IACnDgE,SAAS,CAACmB,QAAQ,EAAEf,MAAM,CAACpE,CAAC,CAAC,CAACqF,KAAK,CAAC,CAAC,CAAC;EAC1C;EACArB,SAAS,CAACmB,QAAQ,EAAED,WAAW,CAAC;EAChClB,SAAS,CAACmB,QAAQ,EAAEF,WAAW,CAAC;EAChC,KAAK,IAAIjF,CAAC,GAAG+E,QAAQ,CAACL,GAAG,GAAG,CAAC,EAAE1E,CAAC,IAAI8E,QAAQ,CAACJ,GAAG,GAAGZ,GAAG,EAAE9D,CAAC,EAAE,EAAE;IACzDgE,SAAS,CAACoB,QAAQ,EAAEhB,MAAM,CAACpE,CAAC,GAAG8D,GAAG,CAAC,CAACuB,KAAK,CAAC,CAAC,CAAC;EAChD;EACArB,SAAS,CAACoB,QAAQ,EAAEH,WAAW,CAAC;EAChCjB,SAAS,CAACoB,QAAQ,EAAEF,WAAW,CAAC;EAChC,OAAO,CAAC;IACAd,MAAM,EAAEe;EACZ,CAAC,EAAE;IACCf,MAAM,EAAEgB;EACZ,CAAC,CAAC;AACV;AACA,SAASE,mBAAmBA,CAACC,YAAY,EAAE;EACvC,IAAInB,MAAM,GAAGmB,YAAY,CAACnB,MAAM;EAChC,IAAIoB,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAG,EAAE;EACZjH,UAAU,CAAC4F,MAAM,EAAEoB,GAAG,EAAEC,GAAG,CAAC;EAC5B,IAAIC,YAAY,GAAG,IAAIjH,YAAY,CAAC+G,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;EACrF,IAAIhE,KAAK,GAAGkE,YAAY,CAAClE,KAAK;EAC9B,IAAIC,MAAM,GAAGiE,YAAY,CAACjE,MAAM;EAChC,IAAIK,CAAC,GAAG4D,YAAY,CAAC5D,CAAC;EACtB,IAAIC,CAAC,GAAG2D,YAAY,CAAC3D,CAAC;EACtB,IAAI4D,GAAG,GAAG,IAAIjH,KAAK,CAAC,CAAC;EACrB,IAAIkH,GAAG,GAAG,IAAIlH,KAAK,CAAC,CAAC;EACrB,IAAI8C,KAAK,GAAGC,MAAM,EAAE;IAChBkE,GAAG,CAAC7D,CAAC,GAAG8D,GAAG,CAAC9D,CAAC,GAAGA,CAAC,GAAGN,KAAK,GAAG,CAAC;IAC7BmE,GAAG,CAAC5D,CAAC,GAAGA,CAAC;IACT6D,GAAG,CAAC7D,CAAC,GAAGA,CAAC,GAAGN,MAAM;EACtB,CAAC,MACI;IACDkE,GAAG,CAAC5D,CAAC,GAAG6D,GAAG,CAAC7D,CAAC,GAAGA,CAAC,GAAGN,MAAM,GAAG,CAAC;IAC9BkE,GAAG,CAAC7D,CAAC,GAAGA,CAAC;IACT8D,GAAG,CAAC9D,CAAC,GAAGA,CAAC,GAAGN,KAAK;EACrB;EACA,OAAO2C,kBAAkB,CAACC,MAAM,EAAEuB,GAAG,EAAEC,GAAG,CAAC;AAC/C;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,EAAE3G,KAAK,EAAE4G,GAAG,EAAE;EACvD,IAAI5G,KAAK,KAAK,CAAC,EAAE;IACb4G,GAAG,CAAC/F,IAAI,CAAC8F,KAAK,CAAC;EACnB,CAAC,MACI;IACD,IAAIE,GAAG,GAAGzG,IAAI,CAACM,KAAK,CAACV,KAAK,GAAG,CAAC,CAAC;IAC/B,IAAIuE,GAAG,GAAGmC,OAAO,CAACC,KAAK,CAAC;IACxBF,qBAAqB,CAACC,OAAO,EAAEnC,GAAG,CAAC,CAAC,CAAC,EAAEsC,GAAG,EAAED,GAAG,CAAC;IAChDH,qBAAqB,CAACC,OAAO,EAAEnC,GAAG,CAAC,CAAC,CAAC,EAAEvE,KAAK,GAAG6G,GAAG,EAAED,GAAG,CAAC;EAC5D;EACA,OAAOA,GAAG;AACd;AACA,OAAO,SAASE,KAAKA,CAACC,IAAI,EAAE/G,KAAK,EAAE;EAC/B,IAAIgH,KAAK,GAAG,EAAE;EACd,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,KAAK,EAAEY,CAAC,EAAE,EAAE;IAC5BoG,KAAK,CAACnG,IAAI,CAACjB,SAAS,CAACmH,IAAI,CAAC,CAAC;EAC/B;EACA,OAAOC,KAAK;AAChB;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnCA,MAAM,CAACC,QAAQ,CAACF,MAAM,CAACG,KAAK,CAAC;EAC7BF,MAAM,CAACG,CAAC,GAAGJ,MAAM,CAACI,CAAC;EACnBH,MAAM,CAACI,EAAE,GAAGL,MAAM,CAACK,EAAE;EACrBJ,MAAM,CAACK,MAAM,GAAGN,MAAM,CAACM,MAAM;AACjC;AACA,SAASC,cAAcA,CAACzC,MAAM,EAAE;EAC5B,IAAI4B,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,MAAM,CAACrD,MAAM,GAAG;IAChCiF,GAAG,CAAC/F,IAAI,CAAC,CAACmE,MAAM,CAACpE,CAAC,EAAE,CAAC,EAAEoE,MAAM,CAACpE,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC;EACA,OAAOgG,GAAG;AACd;AACA,OAAO,SAASc,KAAKA,CAACX,IAAI,EAAE/G,KAAK,EAAE;EAC/B,IAAIkB,SAAS,GAAG,EAAE;EAClB,IAAIyF,KAAK,GAAGI,IAAI,CAACJ,KAAK;EACtB,IAAIgB,YAAY;EAChB,QAAQZ,IAAI,CAACa,IAAI;IACb,KAAK,MAAM;MACP1F,UAAU,CAACyE,KAAK,EAAE3G,KAAK,EAAEkB,SAAS,CAAC;MACnCyG,YAAY,GAAGlI,IAAI;MACnB;IACJ,KAAK,QAAQ;MACTuB,YAAY,CAAC2F,KAAK,EAAE3G,KAAK,EAAEkB,SAAS,CAAC;MACrCyG,YAAY,GAAGjI,MAAM;MACrB;IACJ,KAAK,QAAQ;MACTsB,YAAY,CAAC;QACTG,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAEuF,KAAK,CAACvF,CAAC;QAAEC,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAElB,IAAI,CAACyH,EAAE,GAAG,CAAC;QACvD7F,EAAE,EAAE2E,KAAK,CAAC3E,EAAE;QAAEC,EAAE,EAAE0E,KAAK,CAAC1E;MAC5B,CAAC,EAAEjC,KAAK,EAAEkB,SAAS,CAAC;MACpByG,YAAY,GAAGjI,MAAM;MACrB;IACJ;MACI,IAAIoI,CAAC,GAAGf,IAAI,CAACgB,oBAAoB,CAAC,CAAC;MACnC,IAAIC,KAAK,GAAGF,CAAC,GAAG1H,IAAI,CAACI,IAAI,CAACJ,IAAI,CAACiG,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC7F,IAAIG,QAAQ,GAAG1I,GAAG,CAACI,cAAc,CAACoH,IAAI,CAACmB,mBAAmB,CAAC,CAAC,EAAEF,KAAK,CAAC,EAAE,UAAUnD,IAAI,EAAE;QAAE,OAAO4C,cAAc,CAAC5C,IAAI,CAAC;MAAE,CAAC,CAAC;MACvH,IAAIsD,YAAY,GAAGF,QAAQ,CAACtG,MAAM;MAClC,IAAIwG,YAAY,KAAK,CAAC,EAAE;QACpB1B,qBAAqB,CAACP,mBAAmB,EAAE;UACvClB,MAAM,EAAEiD,QAAQ,CAAC,CAAC;QACtB,CAAC,EAAEjI,KAAK,EAAEkB,SAAS,CAAC;MACxB,CAAC,MACI,IAAIiH,YAAY,KAAKnI,KAAK,EAAE;QAC7B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,YAAY,EAAEvH,CAAC,EAAE,EAAE;UACnCM,SAAS,CAACL,IAAI,CAAC;YACXmE,MAAM,EAAEiD,QAAQ,CAACrH,CAAC;UACtB,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACD,IAAIwH,WAAW,GAAG,CAAC;QACnB,IAAIC,KAAK,GAAG9I,GAAG,CAAC0I,QAAQ,EAAE,UAAUpD,IAAI,EAAE;UACtC,IAAIuB,GAAG,GAAG,EAAE;UACZ,IAAIC,GAAG,GAAG,EAAE;UACZjH,UAAU,CAACyF,IAAI,EAAEuB,GAAG,EAAEC,GAAG,CAAC;UAC1B,IAAIiC,IAAI,GAAG,CAACjC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAKC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;UAChDgC,WAAW,IAAIE,IAAI;UACnB,OAAO;YAAEzD,IAAI,EAAEA,IAAI;YAAEyD,IAAI,EAAEA;UAAK,CAAC;QACrC,CAAC,CAAC;QACFD,KAAK,CAAC9C,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;UAAE,OAAOA,CAAC,CAAC6C,IAAI,GAAG9C,CAAC,CAAC8C,IAAI;QAAE,CAAC,CAAC;QACvD,IAAIC,IAAI,GAAGvI,KAAK;QAChB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,YAAY,EAAEvH,CAAC,EAAE,EAAE;UACnC,IAAI4H,IAAI,GAAGH,KAAK,CAACzH,CAAC,CAAC;UACnB,IAAI2H,IAAI,IAAI,CAAC,EAAE;YACX;UACJ;UACA,IAAIE,SAAS,GAAG7H,CAAC,KAAKuH,YAAY,GAAG,CAAC,GAChCI,IAAI,GACJnI,IAAI,CAACG,IAAI,CAACiI,IAAI,CAACF,IAAI,GAAGF,WAAW,GAAGpI,KAAK,CAAC;UAChD,IAAIyI,SAAS,GAAG,CAAC,EAAE;YACf;UACJ;UACAhC,qBAAqB,CAACP,mBAAmB,EAAE;YACvClB,MAAM,EAAEwD,IAAI,CAAC3D;UACjB,CAAC,EAAE4D,SAAS,EAAEvH,SAAS,CAAC;UACxBqH,IAAI,IAAIE,SAAS;QACrB;QACA;MACJ;MACAd,YAAY,GAAGnI,OAAO;MACtB;EACR;EACA,IAAI,CAACmI,YAAY,EAAE;IACf,OAAOb,KAAK,CAACC,IAAI,EAAE/G,KAAK,CAAC;EAC7B;EACA,IAAI4G,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIhG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,SAAS,CAACS,MAAM,EAAEf,CAAC,EAAE,EAAE;IACvC,IAAI8H,OAAO,GAAG,IAAIf,YAAY,CAAC,CAAC;IAChCe,OAAO,CAACC,QAAQ,CAACzH,SAAS,CAACN,CAAC,CAAC,CAAC;IAC9BqG,aAAa,CAACF,IAAI,EAAE2B,OAAO,CAAC;IAC5B9B,GAAG,CAAC/F,IAAI,CAAC6H,OAAO,CAAC;EACrB;EACA,OAAO9B,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}