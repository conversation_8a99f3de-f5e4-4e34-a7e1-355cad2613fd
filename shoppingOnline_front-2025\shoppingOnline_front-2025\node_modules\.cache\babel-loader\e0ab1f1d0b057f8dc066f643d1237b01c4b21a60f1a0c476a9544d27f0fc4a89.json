{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nimport { getClassId } from './cssClassId.js';\nexport var EASING_MAP = {\n  cubicIn: '0.32,0,0.67,0',\n  cubicOut: '0.33,1,0.68,1',\n  cubicInOut: '0.65,0,0.35,1',\n  quadraticIn: '0.11,0,0.5,0',\n  quadraticOut: '0.5,1,0.89,1',\n  quadraticInOut: '0.45,0,0.55,1',\n  quarticIn: '0.5,0,0.75,0',\n  quarticOut: '0.25,1,0.5,1',\n  quarticInOut: '0.76,0,0.24,1',\n  quinticIn: '0.64,0,0.78,0',\n  quinticOut: '0.22,1,0.36,1',\n  quinticInOut: '0.83,0,0.17,1',\n  sinusoidalIn: '0.12,0,0.39,0',\n  sinusoidalOut: '0.61,1,0.88,1',\n  sinusoidalInOut: '0.37,0,0.63,1',\n  exponentialIn: '0.7,0,0.84,0',\n  exponentialOut: '0.16,1,0.3,1',\n  exponentialInOut: '0.87,0,0.13,1',\n  circularIn: '0.55,0,1,0.45',\n  circularOut: '0,0.55,0.45,1',\n  circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n  var shape = extend({}, el.shape);\n  extend(shape, kfShape);\n  el.buildPath(path, shape);\n  var svgPathBuilder = new SVGPathRebuilder();\n  svgPathBuilder.reset(getPathPrecision(el));\n  path.rebuildPath(svgPathBuilder, 1);\n  svgPathBuilder.generateStr();\n  return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n  var originX = transform.originX,\n    originY = transform.originY;\n  if (originX || originY) {\n    target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n  }\n}\nexport var ANIMATE_STYLE_MAP = {\n  fill: 'fill',\n  opacity: 'opacity',\n  lineWidth: 'stroke-width',\n  lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n  var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n  scope.cssAnims[animationName] = cssAnim;\n  return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n  var paths = el.shape.paths;\n  var composedAnim = {};\n  var cssAnimationCfg;\n  var cssAnimationName;\n  each(paths, function (path) {\n    var subScope = createBrushScope(scope.zrId);\n    subScope.animation = true;\n    createCSSAnimation(path, {}, subScope, true);\n    var cssAnims = subScope.cssAnims;\n    var cssNodes = subScope.cssNodes;\n    var animNames = keys(cssAnims);\n    var len = animNames.length;\n    if (!len) {\n      return;\n    }\n    cssAnimationName = animNames[len - 1];\n    var lastAnim = cssAnims[cssAnimationName];\n    for (var percent in lastAnim) {\n      var kf = lastAnim[percent];\n      composedAnim[percent] = composedAnim[percent] || {\n        d: ''\n      };\n      composedAnim[percent].d += kf.d || '';\n    }\n    for (var className in cssNodes) {\n      var val = cssNodes[className].animation;\n      if (val.indexOf(cssAnimationName) >= 0) {\n        cssAnimationCfg = val;\n      }\n    }\n  });\n  if (!cssAnimationCfg) {\n    return;\n  }\n  attrs.d = false;\n  var animationName = addAnimation(composedAnim, scope);\n  return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n  return isString(easing) ? EASING_MAP[easing] ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\" : createCubicEasingFunc(easing) ? easing : '' : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n  var animators = el.animators;\n  var len = animators.length;\n  var cssAnimations = [];\n  if (el instanceof CompoundPath) {\n    var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    } else if (!len) {\n      return;\n    }\n  } else if (!len) {\n    return;\n  }\n  var groupAnimators = {};\n  for (var i = 0; i < len; i++) {\n    var animator = animators[i];\n    var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n    var easing = getEasingFunc(animator.getClip().easing);\n    var delay = animator.getDelay();\n    if (easing) {\n      cfgArr.push(easing);\n    } else {\n      cfgArr.push('linear');\n    }\n    if (delay) {\n      cfgArr.push(delay / 1000 + 's');\n    }\n    if (animator.getLoop()) {\n      cfgArr.push('infinite');\n    }\n    var cfg = cfgArr.join(' ');\n    groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n    groupAnimators[cfg][1].push(animator);\n  }\n  function createSingleCSSAnimation(groupAnimator) {\n    var animators = groupAnimator[1];\n    var len = animators.length;\n    var transformKfs = {};\n    var shapeKfs = {};\n    var finalKfs = {};\n    var animationTimingFunctionAttrName = 'animation-timing-function';\n    function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n      var tracks = animator.getTracks();\n      var maxTime = animator.getMaxTime();\n      for (var k = 0; k < tracks.length; k++) {\n        var track = tracks[k];\n        if (track.needsAnimate()) {\n          var kfs = track.keyframes;\n          var attrName = track.propName;\n          toCssAttrName && (attrName = toCssAttrName(attrName));\n          if (attrName) {\n            for (var i = 0; i < kfs.length; i++) {\n              var kf = kfs[i];\n              var percent = Math.round(kf.time / maxTime * 100) + '%';\n              var kfEasing = getEasingFunc(kf.easing);\n              var rawValue = kf.rawValue;\n              if (isString(rawValue) || isNumber(rawValue)) {\n                cssKfs[percent] = cssKfs[percent] || {};\n                cssKfs[percent][attrName] = kf.rawValue;\n                if (kfEasing) {\n                  cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    for (var i = 0; i < len; i++) {\n      var animator = animators[i];\n      var targetProp = animator.targetName;\n      if (!targetProp) {\n        !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n      } else if (targetProp === 'shape') {\n        saveAnimatorTrackToCssKfs(animator, shapeKfs);\n      }\n    }\n    for (var percent in transformKfs) {\n      var transform = {};\n      copyTransform(transform, el);\n      extend(transform, transformKfs[percent]);\n      var str = getSRTTransformString(transform);\n      var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n      finalKfs[percent] = str ? {\n        transform: str\n      } : {};\n      setTransformOrigin(finalKfs[percent], transform);\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    var path;\n    var canAnimateShape = true;\n    for (var percent in shapeKfs) {\n      finalKfs[percent] = finalKfs[percent] || {};\n      var isFirst = !path;\n      var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n      if (isFirst) {\n        path = new PathProxy();\n      }\n      var len_1 = path.len();\n      path.reset();\n      finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n      var newLen = path.len();\n      if (!isFirst && len_1 !== newLen) {\n        canAnimateShape = false;\n        break;\n      }\n      if (timingFunction) {\n        finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n      }\n    }\n    ;\n    if (!canAnimateShape) {\n      for (var percent in finalKfs) {\n        delete finalKfs[percent].d;\n      }\n    }\n    if (!onlyShape) {\n      for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var targetProp = animator.targetName;\n        if (targetProp === 'style') {\n          saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) {\n            return ANIMATE_STYLE_MAP[propName];\n          });\n        }\n      }\n    }\n    var percents = keys(finalKfs);\n    var allTransformOriginSame = true;\n    var transformOrigin;\n    for (var i = 1; i < percents.length; i++) {\n      var p0 = percents[i - 1];\n      var p1 = percents[i];\n      if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n        allTransformOriginSame = false;\n        break;\n      }\n      transformOrigin = finalKfs[p0][transformOriginKey];\n    }\n    if (allTransformOriginSame && transformOrigin) {\n      for (var percent in finalKfs) {\n        if (finalKfs[percent][transformOriginKey]) {\n          delete finalKfs[percent][transformOriginKey];\n        }\n      }\n      attrs[transformOriginKey] = transformOrigin;\n    }\n    if (filter(percents, function (percent) {\n      return keys(finalKfs[percent]).length > 0;\n    }).length) {\n      var animationName = addAnimation(finalKfs, scope);\n      return animationName + \" \" + groupAnimator[0] + \" both\";\n    }\n  }\n  for (var key in groupAnimators) {\n    var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n    if (animationCfg) {\n      cssAnimations.push(animationCfg);\n    }\n  }\n  if (cssAnimations.length) {\n    var className = scope.zrId + '-cls-' + getClassId();\n    scope.cssNodes['.' + className] = {\n      animation: cssAnimations.join(',')\n    };\n    attrs[\"class\"] = className;\n  }\n}", "map": {"version": 3, "names": ["copyTransform", "createBrushScope", "SVGPathRebuilder", "PathProxy", "getPathPrecision", "getSRTTransformString", "each", "extend", "filter", "isNumber", "isString", "keys", "CompoundPath", "createCubicEasingFunc", "getClassId", "EASING_MAP", "cubicIn", "cubicOut", "cubicInOut", "quadraticIn", "quadraticOut", "quadraticInOut", "quarticIn", "quarticOut", "quarticInOut", "quinticIn", "quinticOut", "quinticInOut", "sinusoidalIn", "sinusoidalOut", "sinusoidalInOut", "exponentialIn", "exponentialOut", "exponentialInOut", "circularIn", "circularOut", "circularInOut", "transform<PERSON><PERSON><PERSON><PERSON><PERSON>", "buildPathString", "el", "kfShape", "path", "shape", "buildPath", "svgPathBuilder", "reset", "rebuildPath", "generateStr", "getStr", "setTransformOrigin", "target", "transform", "originX", "originY", "ANIMATE_STYLE_MAP", "fill", "opacity", "lineWidth", "lineDashOffset", "addAnimation", "cssAnim", "scope", "animationName", "zrId", "cssAnimIdx", "cssAnims", "createCompoundPathCSSAnimation", "attrs", "paths", "composedAnim", "cssAnimationCfg", "cssAnimationName", "subScope", "animation", "createCSSAnimation", "cssNodes", "anim<PERSON><PERSON><PERSON>", "len", "length", "lastAnim", "percent", "kf", "d", "className", "val", "indexOf", "replace", "getEasingFunc", "easing", "only<PERSON><PERSON><PERSON>", "animators", "cssAnimations", "animationCfg", "push", "groupAnimators", "i", "animator", "cfgArr", "getMaxTime", "getClip", "delay", "get<PERSON>elay", "getLoop", "cfg", "join", "createSingleCSSAnimation", "groupAnimator", "transformKfs", "shapeKfs", "finalKfs", "animationTimingFunctionAttrName", "saveAnimatorTrackToCssKfs", "cssKfs", "toCssAttrName", "tracks", "getTracks", "maxTime", "k", "track", "needsAnimate", "kfs", "keyframes", "attrName", "propName", "Math", "round", "time", "kfEasing", "rawValue", "targetProp", "targetName", "str", "timingFunction", "canAnimateShape", "<PERSON><PERSON><PERSON><PERSON>", "len_1", "newLen", "percents", "allTransformOriginSame", "transform<PERSON><PERSON>in", "p0", "p1", "key"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/svg/cssAnimation.js"], "sourcesContent": ["import { copyTransform } from '../core/Transformable.js';\nimport { createBrushScope } from './core.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport PathProxy from '../core/PathProxy.js';\nimport { getPathPrecision, getSRTTransformString } from './helper.js';\nimport { each, extend, filter, isNumber, isString, keys } from '../core/util.js';\nimport CompoundPath from '../graphic/CompoundPath.js';\nimport { createCubicEasingFunc } from '../animation/cubicEasing.js';\nimport { getClassId } from './cssClassId.js';\nexport var EASING_MAP = {\n    cubicIn: '0.32,0,0.67,0',\n    cubicOut: '0.33,1,0.68,1',\n    cubicInOut: '0.65,0,0.35,1',\n    quadraticIn: '0.11,0,0.5,0',\n    quadraticOut: '0.5,1,0.89,1',\n    quadraticInOut: '0.45,0,0.55,1',\n    quarticIn: '0.5,0,0.75,0',\n    quarticOut: '0.25,1,0.5,1',\n    quarticInOut: '0.76,0,0.24,1',\n    quinticIn: '0.64,0,0.78,0',\n    quinticOut: '0.22,1,0.36,1',\n    quinticInOut: '0.83,0,0.17,1',\n    sinusoidalIn: '0.12,0,0.39,0',\n    sinusoidalOut: '0.61,1,0.88,1',\n    sinusoidalInOut: '0.37,0,0.63,1',\n    exponentialIn: '0.7,0,0.84,0',\n    exponentialOut: '0.16,1,0.3,1',\n    exponentialInOut: '0.87,0,0.13,1',\n    circularIn: '0.55,0,1,0.45',\n    circularOut: '0,0.55,0.45,1',\n    circularInOut: '0.85,0,0.15,1'\n};\nvar transformOriginKey = 'transform-origin';\nfunction buildPathString(el, kfShape, path) {\n    var shape = extend({}, el.shape);\n    extend(shape, kfShape);\n    el.buildPath(path, shape);\n    var svgPathBuilder = new SVGPathRebuilder();\n    svgPathBuilder.reset(getPathPrecision(el));\n    path.rebuildPath(svgPathBuilder, 1);\n    svgPathBuilder.generateStr();\n    return svgPathBuilder.getStr();\n}\nfunction setTransformOrigin(target, transform) {\n    var originX = transform.originX, originY = transform.originY;\n    if (originX || originY) {\n        target[transformOriginKey] = originX + \"px \" + originY + \"px\";\n    }\n}\nexport var ANIMATE_STYLE_MAP = {\n    fill: 'fill',\n    opacity: 'opacity',\n    lineWidth: 'stroke-width',\n    lineDashOffset: 'stroke-dashoffset'\n};\nfunction addAnimation(cssAnim, scope) {\n    var animationName = scope.zrId + '-ani-' + scope.cssAnimIdx++;\n    scope.cssAnims[animationName] = cssAnim;\n    return animationName;\n}\nfunction createCompoundPathCSSAnimation(el, attrs, scope) {\n    var paths = el.shape.paths;\n    var composedAnim = {};\n    var cssAnimationCfg;\n    var cssAnimationName;\n    each(paths, function (path) {\n        var subScope = createBrushScope(scope.zrId);\n        subScope.animation = true;\n        createCSSAnimation(path, {}, subScope, true);\n        var cssAnims = subScope.cssAnims;\n        var cssNodes = subScope.cssNodes;\n        var animNames = keys(cssAnims);\n        var len = animNames.length;\n        if (!len) {\n            return;\n        }\n        cssAnimationName = animNames[len - 1];\n        var lastAnim = cssAnims[cssAnimationName];\n        for (var percent in lastAnim) {\n            var kf = lastAnim[percent];\n            composedAnim[percent] = composedAnim[percent] || { d: '' };\n            composedAnim[percent].d += kf.d || '';\n        }\n        for (var className in cssNodes) {\n            var val = cssNodes[className].animation;\n            if (val.indexOf(cssAnimationName) >= 0) {\n                cssAnimationCfg = val;\n            }\n        }\n    });\n    if (!cssAnimationCfg) {\n        return;\n    }\n    attrs.d = false;\n    var animationName = addAnimation(composedAnim, scope);\n    return cssAnimationCfg.replace(cssAnimationName, animationName);\n}\nfunction getEasingFunc(easing) {\n    return isString(easing)\n        ? EASING_MAP[easing]\n            ? \"cubic-bezier(\" + EASING_MAP[easing] + \")\"\n            : createCubicEasingFunc(easing) ? easing : ''\n        : '';\n}\nexport function createCSSAnimation(el, attrs, scope, onlyShape) {\n    var animators = el.animators;\n    var len = animators.length;\n    var cssAnimations = [];\n    if (el instanceof CompoundPath) {\n        var animationCfg = createCompoundPathCSSAnimation(el, attrs, scope);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n        else if (!len) {\n            return;\n        }\n    }\n    else if (!len) {\n        return;\n    }\n    var groupAnimators = {};\n    for (var i = 0; i < len; i++) {\n        var animator = animators[i];\n        var cfgArr = [animator.getMaxTime() / 1000 + 's'];\n        var easing = getEasingFunc(animator.getClip().easing);\n        var delay = animator.getDelay();\n        if (easing) {\n            cfgArr.push(easing);\n        }\n        else {\n            cfgArr.push('linear');\n        }\n        if (delay) {\n            cfgArr.push(delay / 1000 + 's');\n        }\n        if (animator.getLoop()) {\n            cfgArr.push('infinite');\n        }\n        var cfg = cfgArr.join(' ');\n        groupAnimators[cfg] = groupAnimators[cfg] || [cfg, []];\n        groupAnimators[cfg][1].push(animator);\n    }\n    function createSingleCSSAnimation(groupAnimator) {\n        var animators = groupAnimator[1];\n        var len = animators.length;\n        var transformKfs = {};\n        var shapeKfs = {};\n        var finalKfs = {};\n        var animationTimingFunctionAttrName = 'animation-timing-function';\n        function saveAnimatorTrackToCssKfs(animator, cssKfs, toCssAttrName) {\n            var tracks = animator.getTracks();\n            var maxTime = animator.getMaxTime();\n            for (var k = 0; k < tracks.length; k++) {\n                var track = tracks[k];\n                if (track.needsAnimate()) {\n                    var kfs = track.keyframes;\n                    var attrName = track.propName;\n                    toCssAttrName && (attrName = toCssAttrName(attrName));\n                    if (attrName) {\n                        for (var i = 0; i < kfs.length; i++) {\n                            var kf = kfs[i];\n                            var percent = Math.round(kf.time / maxTime * 100) + '%';\n                            var kfEasing = getEasingFunc(kf.easing);\n                            var rawValue = kf.rawValue;\n                            if (isString(rawValue) || isNumber(rawValue)) {\n                                cssKfs[percent] = cssKfs[percent] || {};\n                                cssKfs[percent][attrName] = kf.rawValue;\n                                if (kfEasing) {\n                                    cssKfs[percent][animationTimingFunctionAttrName] = kfEasing;\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        for (var i = 0; i < len; i++) {\n            var animator = animators[i];\n            var targetProp = animator.targetName;\n            if (!targetProp) {\n                !onlyShape && saveAnimatorTrackToCssKfs(animator, transformKfs);\n            }\n            else if (targetProp === 'shape') {\n                saveAnimatorTrackToCssKfs(animator, shapeKfs);\n            }\n        }\n        for (var percent in transformKfs) {\n            var transform = {};\n            copyTransform(transform, el);\n            extend(transform, transformKfs[percent]);\n            var str = getSRTTransformString(transform);\n            var timingFunction = transformKfs[percent][animationTimingFunctionAttrName];\n            finalKfs[percent] = str ? {\n                transform: str\n            } : {};\n            setTransformOrigin(finalKfs[percent], transform);\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        var path;\n        var canAnimateShape = true;\n        for (var percent in shapeKfs) {\n            finalKfs[percent] = finalKfs[percent] || {};\n            var isFirst = !path;\n            var timingFunction = shapeKfs[percent][animationTimingFunctionAttrName];\n            if (isFirst) {\n                path = new PathProxy();\n            }\n            var len_1 = path.len();\n            path.reset();\n            finalKfs[percent].d = buildPathString(el, shapeKfs[percent], path);\n            var newLen = path.len();\n            if (!isFirst && len_1 !== newLen) {\n                canAnimateShape = false;\n                break;\n            }\n            if (timingFunction) {\n                finalKfs[percent][animationTimingFunctionAttrName] = timingFunction;\n            }\n        }\n        ;\n        if (!canAnimateShape) {\n            for (var percent in finalKfs) {\n                delete finalKfs[percent].d;\n            }\n        }\n        if (!onlyShape) {\n            for (var i = 0; i < len; i++) {\n                var animator = animators[i];\n                var targetProp = animator.targetName;\n                if (targetProp === 'style') {\n                    saveAnimatorTrackToCssKfs(animator, finalKfs, function (propName) { return ANIMATE_STYLE_MAP[propName]; });\n                }\n            }\n        }\n        var percents = keys(finalKfs);\n        var allTransformOriginSame = true;\n        var transformOrigin;\n        for (var i = 1; i < percents.length; i++) {\n            var p0 = percents[i - 1];\n            var p1 = percents[i];\n            if (finalKfs[p0][transformOriginKey] !== finalKfs[p1][transformOriginKey]) {\n                allTransformOriginSame = false;\n                break;\n            }\n            transformOrigin = finalKfs[p0][transformOriginKey];\n        }\n        if (allTransformOriginSame && transformOrigin) {\n            for (var percent in finalKfs) {\n                if (finalKfs[percent][transformOriginKey]) {\n                    delete finalKfs[percent][transformOriginKey];\n                }\n            }\n            attrs[transformOriginKey] = transformOrigin;\n        }\n        if (filter(percents, function (percent) { return keys(finalKfs[percent]).length > 0; }).length) {\n            var animationName = addAnimation(finalKfs, scope);\n            return animationName + \" \" + groupAnimator[0] + \" both\";\n        }\n    }\n    for (var key in groupAnimators) {\n        var animationCfg = createSingleCSSAnimation(groupAnimators[key]);\n        if (animationCfg) {\n            cssAnimations.push(animationCfg);\n        }\n    }\n    if (cssAnimations.length) {\n        var className = scope.zrId + '-cls-' + getClassId();\n        scope.cssNodes['.' + className] = {\n            animation: cssAnimations.join(',')\n        };\n        attrs[\"class\"] = className;\n    }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,0BAA0B;AACxD,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,aAAa;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,iBAAiB;AAChF,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,OAAO,IAAIC,UAAU,GAAG;EACpBC,OAAO,EAAE,eAAe;EACxBC,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,eAAe;EAC3BC,WAAW,EAAE,cAAc;EAC3BC,YAAY,EAAE,cAAc;EAC5BC,cAAc,EAAE,eAAe;EAC/BC,SAAS,EAAE,cAAc;EACzBC,UAAU,EAAE,cAAc;EAC1BC,YAAY,EAAE,eAAe;EAC7BC,SAAS,EAAE,eAAe;EAC1BC,UAAU,EAAE,eAAe;EAC3BC,YAAY,EAAE,eAAe;EAC7BC,YAAY,EAAE,eAAe;EAC7BC,aAAa,EAAE,eAAe;EAC9BC,eAAe,EAAE,eAAe;EAChCC,aAAa,EAAE,cAAc;EAC7BC,cAAc,EAAE,cAAc;EAC9BC,gBAAgB,EAAE,eAAe;EACjCC,UAAU,EAAE,eAAe;EAC3BC,WAAW,EAAE,eAAe;EAC5BC,aAAa,EAAE;AACnB,CAAC;AACD,IAAIC,kBAAkB,GAAG,kBAAkB;AAC3C,SAASC,eAAeA,CAACC,EAAE,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGnC,MAAM,CAAC,CAAC,CAAC,EAAEgC,EAAE,CAACG,KAAK,CAAC;EAChCnC,MAAM,CAACmC,KAAK,EAAEF,OAAO,CAAC;EACtBD,EAAE,CAACI,SAAS,CAACF,IAAI,EAAEC,KAAK,CAAC;EACzB,IAAIE,cAAc,GAAG,IAAI1C,gBAAgB,CAAC,CAAC;EAC3C0C,cAAc,CAACC,KAAK,CAACzC,gBAAgB,CAACmC,EAAE,CAAC,CAAC;EAC1CE,IAAI,CAACK,WAAW,CAACF,cAAc,EAAE,CAAC,CAAC;EACnCA,cAAc,CAACG,WAAW,CAAC,CAAC;EAC5B,OAAOH,cAAc,CAACI,MAAM,CAAC,CAAC;AAClC;AACA,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,SAAS,EAAE;EAC3C,IAAIC,OAAO,GAAGD,SAAS,CAACC,OAAO;IAAEC,OAAO,GAAGF,SAAS,CAACE,OAAO;EAC5D,IAAID,OAAO,IAAIC,OAAO,EAAE;IACpBH,MAAM,CAACb,kBAAkB,CAAC,GAAGe,OAAO,GAAG,KAAK,GAAGC,OAAO,GAAG,IAAI;EACjE;AACJ;AACA,OAAO,IAAIC,iBAAiB,GAAG;EAC3BC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,cAAc;EACzBC,cAAc,EAAE;AACpB,CAAC;AACD,SAASC,YAAYA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAClC,IAAIC,aAAa,GAAGD,KAAK,CAACE,IAAI,GAAG,OAAO,GAAGF,KAAK,CAACG,UAAU,EAAE;EAC7DH,KAAK,CAACI,QAAQ,CAACH,aAAa,CAAC,GAAGF,OAAO;EACvC,OAAOE,aAAa;AACxB;AACA,SAASI,8BAA8BA,CAAC3B,EAAE,EAAE4B,KAAK,EAAEN,KAAK,EAAE;EACtD,IAAIO,KAAK,GAAG7B,EAAE,CAACG,KAAK,CAAC0B,KAAK;EAC1B,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,eAAe;EACnB,IAAIC,gBAAgB;EACpBjE,IAAI,CAAC8D,KAAK,EAAE,UAAU3B,IAAI,EAAE;IACxB,IAAI+B,QAAQ,GAAGvE,gBAAgB,CAAC4D,KAAK,CAACE,IAAI,CAAC;IAC3CS,QAAQ,CAACC,SAAS,GAAG,IAAI;IACzBC,kBAAkB,CAACjC,IAAI,EAAE,CAAC,CAAC,EAAE+B,QAAQ,EAAE,IAAI,CAAC;IAC5C,IAAIP,QAAQ,GAAGO,QAAQ,CAACP,QAAQ;IAChC,IAAIU,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;IAChC,IAAIC,SAAS,GAAGjE,IAAI,CAACsD,QAAQ,CAAC;IAC9B,IAAIY,GAAG,GAAGD,SAAS,CAACE,MAAM;IAC1B,IAAI,CAACD,GAAG,EAAE;MACN;IACJ;IACAN,gBAAgB,GAAGK,SAAS,CAACC,GAAG,GAAG,CAAC,CAAC;IACrC,IAAIE,QAAQ,GAAGd,QAAQ,CAACM,gBAAgB,CAAC;IACzC,KAAK,IAAIS,OAAO,IAAID,QAAQ,EAAE;MAC1B,IAAIE,EAAE,GAAGF,QAAQ,CAACC,OAAO,CAAC;MAC1BX,YAAY,CAACW,OAAO,CAAC,GAAGX,YAAY,CAACW,OAAO,CAAC,IAAI;QAAEE,CAAC,EAAE;MAAG,CAAC;MAC1Db,YAAY,CAACW,OAAO,CAAC,CAACE,CAAC,IAAID,EAAE,CAACC,CAAC,IAAI,EAAE;IACzC;IACA,KAAK,IAAIC,SAAS,IAAIR,QAAQ,EAAE;MAC5B,IAAIS,GAAG,GAAGT,QAAQ,CAACQ,SAAS,CAAC,CAACV,SAAS;MACvC,IAAIW,GAAG,CAACC,OAAO,CAACd,gBAAgB,CAAC,IAAI,CAAC,EAAE;QACpCD,eAAe,GAAGc,GAAG;MACzB;IACJ;EACJ,CAAC,CAAC;EACF,IAAI,CAACd,eAAe,EAAE;IAClB;EACJ;EACAH,KAAK,CAACe,CAAC,GAAG,KAAK;EACf,IAAIpB,aAAa,GAAGH,YAAY,CAACU,YAAY,EAAER,KAAK,CAAC;EACrD,OAAOS,eAAe,CAACgB,OAAO,CAACf,gBAAgB,EAAET,aAAa,CAAC;AACnE;AACA,SAASyB,aAAaA,CAACC,MAAM,EAAE;EAC3B,OAAO9E,QAAQ,CAAC8E,MAAM,CAAC,GACjBzE,UAAU,CAACyE,MAAM,CAAC,GACd,eAAe,GAAGzE,UAAU,CAACyE,MAAM,CAAC,GAAG,GAAG,GAC1C3E,qBAAqB,CAAC2E,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE,GAC/C,EAAE;AACZ;AACA,OAAO,SAASd,kBAAkBA,CAACnC,EAAE,EAAE4B,KAAK,EAAEN,KAAK,EAAE4B,SAAS,EAAE;EAC5D,IAAIC,SAAS,GAAGnD,EAAE,CAACmD,SAAS;EAC5B,IAAIb,GAAG,GAAGa,SAAS,CAACZ,MAAM;EAC1B,IAAIa,aAAa,GAAG,EAAE;EACtB,IAAIpD,EAAE,YAAY3B,YAAY,EAAE;IAC5B,IAAIgF,YAAY,GAAG1B,8BAA8B,CAAC3B,EAAE,EAAE4B,KAAK,EAAEN,KAAK,CAAC;IACnE,IAAI+B,YAAY,EAAE;MACdD,aAAa,CAACE,IAAI,CAACD,YAAY,CAAC;IACpC,CAAC,MACI,IAAI,CAACf,GAAG,EAAE;MACX;IACJ;EACJ,CAAC,MACI,IAAI,CAACA,GAAG,EAAE;IACX;EACJ;EACA,IAAIiB,cAAc,GAAG,CAAC,CAAC;EACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,GAAG,EAAEkB,CAAC,EAAE,EAAE;IAC1B,IAAIC,QAAQ,GAAGN,SAAS,CAACK,CAAC,CAAC;IAC3B,IAAIE,MAAM,GAAG,CAACD,QAAQ,CAACE,UAAU,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC;IACjD,IAAIV,MAAM,GAAGD,aAAa,CAACS,QAAQ,CAACG,OAAO,CAAC,CAAC,CAACX,MAAM,CAAC;IACrD,IAAIY,KAAK,GAAGJ,QAAQ,CAACK,QAAQ,CAAC,CAAC;IAC/B,IAAIb,MAAM,EAAE;MACRS,MAAM,CAACJ,IAAI,CAACL,MAAM,CAAC;IACvB,CAAC,MACI;MACDS,MAAM,CAACJ,IAAI,CAAC,QAAQ,CAAC;IACzB;IACA,IAAIO,KAAK,EAAE;MACPH,MAAM,CAACJ,IAAI,CAACO,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;IACnC;IACA,IAAIJ,QAAQ,CAACM,OAAO,CAAC,CAAC,EAAE;MACpBL,MAAM,CAACJ,IAAI,CAAC,UAAU,CAAC;IAC3B;IACA,IAAIU,GAAG,GAAGN,MAAM,CAACO,IAAI,CAAC,GAAG,CAAC;IAC1BV,cAAc,CAACS,GAAG,CAAC,GAAGT,cAAc,CAACS,GAAG,CAAC,IAAI,CAACA,GAAG,EAAE,EAAE,CAAC;IACtDT,cAAc,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACV,IAAI,CAACG,QAAQ,CAAC;EACzC;EACA,SAASS,wBAAwBA,CAACC,aAAa,EAAE;IAC7C,IAAIhB,SAAS,GAAGgB,aAAa,CAAC,CAAC,CAAC;IAChC,IAAI7B,GAAG,GAAGa,SAAS,CAACZ,MAAM;IAC1B,IAAI6B,YAAY,GAAG,CAAC,CAAC;IACrB,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,+BAA+B,GAAG,2BAA2B;IACjE,SAASC,yBAAyBA,CAACf,QAAQ,EAAEgB,MAAM,EAAEC,aAAa,EAAE;MAChE,IAAIC,MAAM,GAAGlB,QAAQ,CAACmB,SAAS,CAAC,CAAC;MACjC,IAAIC,OAAO,GAAGpB,QAAQ,CAACE,UAAU,CAAC,CAAC;MACnC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACpC,MAAM,EAAEuC,CAAC,EAAE,EAAE;QACpC,IAAIC,KAAK,GAAGJ,MAAM,CAACG,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACC,YAAY,CAAC,CAAC,EAAE;UACtB,IAAIC,GAAG,GAAGF,KAAK,CAACG,SAAS;UACzB,IAAIC,QAAQ,GAAGJ,KAAK,CAACK,QAAQ;UAC7BV,aAAa,KAAKS,QAAQ,GAAGT,aAAa,CAACS,QAAQ,CAAC,CAAC;UACrD,IAAIA,QAAQ,EAAE;YACV,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,GAAG,CAAC1C,MAAM,EAAEiB,CAAC,EAAE,EAAE;cACjC,IAAId,EAAE,GAAGuC,GAAG,CAACzB,CAAC,CAAC;cACf,IAAIf,OAAO,GAAG4C,IAAI,CAACC,KAAK,CAAC5C,EAAE,CAAC6C,IAAI,GAAGV,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG;cACvD,IAAIW,QAAQ,GAAGxC,aAAa,CAACN,EAAE,CAACO,MAAM,CAAC;cACvC,IAAIwC,QAAQ,GAAG/C,EAAE,CAAC+C,QAAQ;cAC1B,IAAItH,QAAQ,CAACsH,QAAQ,CAAC,IAAIvH,QAAQ,CAACuH,QAAQ,CAAC,EAAE;gBAC1ChB,MAAM,CAAChC,OAAO,CAAC,GAAGgC,MAAM,CAAChC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvCgC,MAAM,CAAChC,OAAO,CAAC,CAAC0C,QAAQ,CAAC,GAAGzC,EAAE,CAAC+C,QAAQ;gBACvC,IAAID,QAAQ,EAAE;kBACVf,MAAM,CAAChC,OAAO,CAAC,CAAC8B,+BAA+B,CAAC,GAAGiB,QAAQ;gBAC/D;cACJ;YACJ;UACJ;QACJ;MACJ;IACJ;IACA,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,GAAG,EAAEkB,CAAC,EAAE,EAAE;MAC1B,IAAIC,QAAQ,GAAGN,SAAS,CAACK,CAAC,CAAC;MAC3B,IAAIkC,UAAU,GAAGjC,QAAQ,CAACkC,UAAU;MACpC,IAAI,CAACD,UAAU,EAAE;QACb,CAACxC,SAAS,IAAIsB,yBAAyB,CAACf,QAAQ,EAAEW,YAAY,CAAC;MACnE,CAAC,MACI,IAAIsB,UAAU,KAAK,OAAO,EAAE;QAC7BlB,yBAAyB,CAACf,QAAQ,EAAEY,QAAQ,CAAC;MACjD;IACJ;IACA,KAAK,IAAI5B,OAAO,IAAI2B,YAAY,EAAE;MAC9B,IAAIxD,SAAS,GAAG,CAAC,CAAC;MAClBnD,aAAa,CAACmD,SAAS,EAAEZ,EAAE,CAAC;MAC5BhC,MAAM,CAAC4C,SAAS,EAAEwD,YAAY,CAAC3B,OAAO,CAAC,CAAC;MACxC,IAAImD,GAAG,GAAG9H,qBAAqB,CAAC8C,SAAS,CAAC;MAC1C,IAAIiF,cAAc,GAAGzB,YAAY,CAAC3B,OAAO,CAAC,CAAC8B,+BAA+B,CAAC;MAC3ED,QAAQ,CAAC7B,OAAO,CAAC,GAAGmD,GAAG,GAAG;QACtBhF,SAAS,EAAEgF;MACf,CAAC,GAAG,CAAC,CAAC;MACNlF,kBAAkB,CAAC4D,QAAQ,CAAC7B,OAAO,CAAC,EAAE7B,SAAS,CAAC;MAChD,IAAIiF,cAAc,EAAE;QAChBvB,QAAQ,CAAC7B,OAAO,CAAC,CAAC8B,+BAA+B,CAAC,GAAGsB,cAAc;MACvE;IACJ;IACA;IACA,IAAI3F,IAAI;IACR,IAAI4F,eAAe,GAAG,IAAI;IAC1B,KAAK,IAAIrD,OAAO,IAAI4B,QAAQ,EAAE;MAC1BC,QAAQ,CAAC7B,OAAO,CAAC,GAAG6B,QAAQ,CAAC7B,OAAO,CAAC,IAAI,CAAC,CAAC;MAC3C,IAAIsD,OAAO,GAAG,CAAC7F,IAAI;MACnB,IAAI2F,cAAc,GAAGxB,QAAQ,CAAC5B,OAAO,CAAC,CAAC8B,+BAA+B,CAAC;MACvE,IAAIwB,OAAO,EAAE;QACT7F,IAAI,GAAG,IAAItC,SAAS,CAAC,CAAC;MAC1B;MACA,IAAIoI,KAAK,GAAG9F,IAAI,CAACoC,GAAG,CAAC,CAAC;MACtBpC,IAAI,CAACI,KAAK,CAAC,CAAC;MACZgE,QAAQ,CAAC7B,OAAO,CAAC,CAACE,CAAC,GAAG5C,eAAe,CAACC,EAAE,EAAEqE,QAAQ,CAAC5B,OAAO,CAAC,EAAEvC,IAAI,CAAC;MAClE,IAAI+F,MAAM,GAAG/F,IAAI,CAACoC,GAAG,CAAC,CAAC;MACvB,IAAI,CAACyD,OAAO,IAAIC,KAAK,KAAKC,MAAM,EAAE;QAC9BH,eAAe,GAAG,KAAK;QACvB;MACJ;MACA,IAAID,cAAc,EAAE;QAChBvB,QAAQ,CAAC7B,OAAO,CAAC,CAAC8B,+BAA+B,CAAC,GAAGsB,cAAc;MACvE;IACJ;IACA;IACA,IAAI,CAACC,eAAe,EAAE;MAClB,KAAK,IAAIrD,OAAO,IAAI6B,QAAQ,EAAE;QAC1B,OAAOA,QAAQ,CAAC7B,OAAO,CAAC,CAACE,CAAC;MAC9B;IACJ;IACA,IAAI,CAACO,SAAS,EAAE;MACZ,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,GAAG,EAAEkB,CAAC,EAAE,EAAE;QAC1B,IAAIC,QAAQ,GAAGN,SAAS,CAACK,CAAC,CAAC;QAC3B,IAAIkC,UAAU,GAAGjC,QAAQ,CAACkC,UAAU;QACpC,IAAID,UAAU,KAAK,OAAO,EAAE;UACxBlB,yBAAyB,CAACf,QAAQ,EAAEa,QAAQ,EAAE,UAAUc,QAAQ,EAAE;YAAE,OAAOrE,iBAAiB,CAACqE,QAAQ,CAAC;UAAE,CAAC,CAAC;QAC9G;MACJ;IACJ;IACA,IAAIc,QAAQ,GAAG9H,IAAI,CAACkG,QAAQ,CAAC;IAC7B,IAAI6B,sBAAsB,GAAG,IAAI;IACjC,IAAIC,eAAe;IACnB,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,QAAQ,CAAC3D,MAAM,EAAEiB,CAAC,EAAE,EAAE;MACtC,IAAI6C,EAAE,GAAGH,QAAQ,CAAC1C,CAAC,GAAG,CAAC,CAAC;MACxB,IAAI8C,EAAE,GAAGJ,QAAQ,CAAC1C,CAAC,CAAC;MACpB,IAAIc,QAAQ,CAAC+B,EAAE,CAAC,CAACvG,kBAAkB,CAAC,KAAKwE,QAAQ,CAACgC,EAAE,CAAC,CAACxG,kBAAkB,CAAC,EAAE;QACvEqG,sBAAsB,GAAG,KAAK;QAC9B;MACJ;MACAC,eAAe,GAAG9B,QAAQ,CAAC+B,EAAE,CAAC,CAACvG,kBAAkB,CAAC;IACtD;IACA,IAAIqG,sBAAsB,IAAIC,eAAe,EAAE;MAC3C,KAAK,IAAI3D,OAAO,IAAI6B,QAAQ,EAAE;QAC1B,IAAIA,QAAQ,CAAC7B,OAAO,CAAC,CAAC3C,kBAAkB,CAAC,EAAE;UACvC,OAAOwE,QAAQ,CAAC7B,OAAO,CAAC,CAAC3C,kBAAkB,CAAC;QAChD;MACJ;MACA8B,KAAK,CAAC9B,kBAAkB,CAAC,GAAGsG,eAAe;IAC/C;IACA,IAAInI,MAAM,CAACiI,QAAQ,EAAE,UAAUzD,OAAO,EAAE;MAAE,OAAOrE,IAAI,CAACkG,QAAQ,CAAC7B,OAAO,CAAC,CAAC,CAACF,MAAM,GAAG,CAAC;IAAE,CAAC,CAAC,CAACA,MAAM,EAAE;MAC5F,IAAIhB,aAAa,GAAGH,YAAY,CAACkD,QAAQ,EAAEhD,KAAK,CAAC;MACjD,OAAOC,aAAa,GAAG,GAAG,GAAG4C,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO;IAC3D;EACJ;EACA,KAAK,IAAIoC,GAAG,IAAIhD,cAAc,EAAE;IAC5B,IAAIF,YAAY,GAAGa,wBAAwB,CAACX,cAAc,CAACgD,GAAG,CAAC,CAAC;IAChE,IAAIlD,YAAY,EAAE;MACdD,aAAa,CAACE,IAAI,CAACD,YAAY,CAAC;IACpC;EACJ;EACA,IAAID,aAAa,CAACb,MAAM,EAAE;IACtB,IAAIK,SAAS,GAAGtB,KAAK,CAACE,IAAI,GAAG,OAAO,GAAGjD,UAAU,CAAC,CAAC;IACnD+C,KAAK,CAACc,QAAQ,CAAC,GAAG,GAAGQ,SAAS,CAAC,GAAG;MAC9BV,SAAS,EAAEkB,aAAa,CAACa,IAAI,CAAC,GAAG;IACrC,CAAC;IACDrC,KAAK,CAAC,OAAO,CAAC,GAAGgB,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}