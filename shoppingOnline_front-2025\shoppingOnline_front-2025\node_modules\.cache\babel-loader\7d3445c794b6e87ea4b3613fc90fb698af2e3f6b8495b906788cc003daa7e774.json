{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, defaults, each, eqNaN, isArray, isObject, isString } from 'zrender/lib/core/util.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport OrdinalMeta from '../../data/OrdinalMeta.js';\nimport Ordinal from '../../scale/Ordinal.js';\nimport { WH, XY } from '../../util/graphic.js';\nimport { ListIterator } from '../../util/model.js';\nimport { createNaNRectLike, setDimXYValue, MatrixCellLayoutInfoType } from './matrixCoordHelper.js';\nimport { error } from '../../util/log.js';\nimport { mathMax } from '../../util/number.js';\n/**\n * Lifetime: the same with `MatrixModel`, but different from `coord/Matrix`.\n */\nvar MatrixDim = /** @class */function () {\n  function MatrixDim(dim, dimModel) {\n    // Under the current definition, every leave corresponds a unit cell,\n    // and leaves can serve as the locator of cells.\n    // Therefore make sure:\n    //  - The first `_leavesCount` elements in `_cells` are leaves.\n    //  - `_cells[leaf.id[XY[this.dimIdx]]]` is the leaf itself.\n    //  - Leaves of each subtree are placed together, that is, the leaves of a dimCell are:\n    //    `this._cells.slice(dimCell.firstLeafLocator, dimCell.span[XY[this.dimIdx]])`\n    this._cells = [];\n    // Can be visited by `_levels[cell.level]` or `_levels[cell.id[1 - dimIdx] + _levels.length]`.\n    // Items are never be null/undefined after initialized.\n    this._levels = [];\n    this.dim = dim;\n    this.dimIdx = dim === 'x' ? 0 : 1;\n    this._model = dimModel;\n    this._uniqueValueGen = createUniqueValueGenerator(dim);\n    var dimModelData = dimModel.get('data', true);\n    if (dimModelData != null && !isArray(dimModelData)) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Illegal echarts option - matrix.\" + this.dim + \".data must be an array if specified.\");\n      }\n      dimModelData = [];\n    }\n    if (dimModelData) {\n      this._initByDimModelData(dimModelData);\n    } else {\n      this._initBySeriesData();\n    }\n  }\n  MatrixDim.prototype._initByDimModelData = function (dimModelData) {\n    var self = this;\n    var _cells = self._cells;\n    var _levels = self._levels;\n    var sameLocatorCellsLists = []; // Save for sorting.\n    var _cellCount = 0;\n    self._leavesCount = traverseInitCells(dimModelData, 0, 0);\n    postInitCells();\n    return;\n    function traverseInitCells(dimModelData, firstLeafLocator, level) {\n      var totalSpan = 0;\n      if (!dimModelData) {\n        return totalSpan;\n      }\n      each(dimModelData, function (option, optionIdx) {\n        var invalidOption = false;\n        var cellOption;\n        if (isString(option)) {\n          cellOption = {\n            value: option\n          };\n        } else if (isObject(option)) {\n          cellOption = option;\n          if (option.value != null && !isString(option.value)) {\n            invalidOption = true;\n            cellOption = {\n              value: null\n            };\n          }\n        } else {\n          cellOption = {\n            value: null\n          };\n          if (option != null) {\n            invalidOption = true;\n          }\n        }\n        if (invalidOption) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Illegal echarts option - matrix.\" + self.dim + \".data[\" + optionIdx + \"]\" + ' must be `string | {value: string}`.');\n          }\n        }\n        var cell = {\n          type: MatrixCellLayoutInfoType.nonLeaf,\n          ordinal: NaN,\n          level: level,\n          firstLeafLocator: firstLeafLocator,\n          id: new Point(),\n          span: setDimXYValue(new Point(), self.dimIdx, 1, 1),\n          option: cellOption,\n          xy: NaN,\n          wh: NaN,\n          dim: self,\n          rect: createNaNRectLike()\n        };\n        _cellCount++;\n        (sameLocatorCellsLists[firstLeafLocator] || (sameLocatorCellsLists[firstLeafLocator] = [])).push(cell);\n        if (!_levels[level]) {\n          // Create a level only if at least one cell exists.\n          _levels[level] = {\n            type: MatrixCellLayoutInfoType.level,\n            xy: NaN,\n            wh: NaN,\n            option: null,\n            id: new Point(),\n            dim: self\n          };\n        }\n        var childrenSpan = traverseInitCells(cellOption.children, firstLeafLocator, level + 1);\n        var subSpan = Math.max(1, childrenSpan);\n        cell.span[XY[self.dimIdx]] = subSpan;\n        totalSpan += subSpan;\n        firstLeafLocator += subSpan;\n      });\n      return totalSpan;\n    }\n    function postInitCells() {\n      // Sort to make sure the leaves are at the beginning, so that\n      // they can be used as the locator of body cells.\n      var categories = [];\n      while (_cells.length < _cellCount) {\n        for (var locator = 0; locator < sameLocatorCellsLists.length; locator++) {\n          var cell = sameLocatorCellsLists[locator].pop();\n          if (cell) {\n            cell.ordinal = categories.length;\n            var val = cell.option.value;\n            categories.push(val);\n            _cells.push(cell);\n            self._uniqueValueGen.calcDupBase(val);\n          }\n        }\n      }\n      self._uniqueValueGen.ensureValueUnique(categories, _cells);\n      var ordinalMeta = self._ordinalMeta = new OrdinalMeta({\n        categories: categories,\n        needCollect: false,\n        deduplication: false\n      });\n      self._scale = new Ordinal({\n        ordinalMeta: ordinalMeta\n      });\n      for (var idx = 0; idx < self._leavesCount; idx++) {\n        var leaf = self._cells[idx];\n        leaf.type = MatrixCellLayoutInfoType.leaf;\n        // Handle the tree level variation: enlarge the span of the leaves to reach the body cells.\n        leaf.span[XY[1 - self.dimIdx]] = self._levels.length - leaf.level;\n      }\n      self._initCellsId();\n      self._initLevelIdOptions();\n    }\n  };\n  MatrixDim.prototype._initBySeriesData = function () {\n    var self = this;\n    self._leavesCount = 0;\n    self._levels = [{\n      type: MatrixCellLayoutInfoType.level,\n      xy: NaN,\n      wh: NaN,\n      option: null,\n      id: new Point(),\n      dim: self\n    }];\n    self._initLevelIdOptions();\n    var ordinalMeta = self._ordinalMeta = new OrdinalMeta({\n      needCollect: true,\n      deduplication: true,\n      onCollect: function (value, ordinalNumber) {\n        var cell = self._cells[ordinalNumber] = {\n          type: MatrixCellLayoutInfoType.leaf,\n          ordinal: ordinalNumber,\n          level: 0,\n          firstLeafLocator: ordinalNumber,\n          id: new Point(),\n          span: setDimXYValue(new Point(), self.dimIdx, 1, 1),\n          // Theoretically `value` is from `dataset` or `series.data`, so it may be any type.\n          // Do not restrict this case for user's convenience, and here simply convert it to\n          // string for display.\n          option: {\n            value: value + ''\n          },\n          xy: NaN,\n          wh: NaN,\n          dim: self,\n          rect: createNaNRectLike()\n        };\n        self._leavesCount++;\n        self._setCellId(cell);\n      }\n    });\n    self._scale = new Ordinal({\n      ordinalMeta: ordinalMeta\n    });\n  };\n  MatrixDim.prototype._setCellId = function (cell) {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    setDimXYValue(cell.id, dimIdx, cell.firstLeafLocator, cell.level - levelsLen);\n  };\n  MatrixDim.prototype._initCellsId = function () {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    each(this._cells, function (cell) {\n      setDimXYValue(cell.id, dimIdx, cell.firstLeafLocator, cell.level - levelsLen);\n    });\n  };\n  MatrixDim.prototype._initLevelIdOptions = function () {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    var levelOptionList = this._model.get('levels', true);\n    levelOptionList = isArray(levelOptionList) ? levelOptionList : [];\n    each(this._levels, function (levelCfg, level) {\n      setDimXYValue(levelCfg.id, dimIdx, 0, level - levelsLen);\n      levelCfg.option = levelOptionList[level];\n    });\n  };\n  MatrixDim.prototype.shouldShow = function () {\n    return !!this._model.getShallow('show', true);\n  };\n  /**\n   * Iterate leaves (they are layout units) if dimIdx === this.dimIdx.\n   * Iterate levels if dimIdx !== this.dimIdx.\n   */\n  MatrixDim.prototype.resetLayoutIterator = function (it, dimIdx, startLocator, count) {\n    it = it || new ListIterator();\n    if (dimIdx === this.dimIdx) {\n      var len = this._leavesCount;\n      var startIdx = startLocator != null ? Math.max(0, startLocator) : 0;\n      count = count != null ? Math.min(count, len) : len;\n      it.reset(this._cells, startIdx, startIdx + count);\n    } else {\n      var len = this._levels.length;\n      // Corner locator is from `-this._levels.length` to `-1`.\n      var startIdx = startLocator != null ? Math.max(0, startLocator + len) : 0;\n      count = count != null ? Math.min(count, len) : len;\n      it.reset(this._levels, startIdx, startIdx + count);\n    }\n    return it;\n  };\n  MatrixDim.prototype.resetCellIterator = function (it) {\n    return (it || new ListIterator()).reset(this._cells, 0);\n  };\n  MatrixDim.prototype.resetLevelIterator = function (it) {\n    return (it || new ListIterator()).reset(this._levels, 0);\n  };\n  MatrixDim.prototype.getLayout = function (outRect, dimIdx, locator) {\n    var layout = this.getUnitLayoutInfo(dimIdx, locator);\n    outRect[XY[dimIdx]] = layout ? layout.xy : NaN;\n    outRect[WH[dimIdx]] = layout ? layout.wh : NaN;\n  };\n  /**\n   * Get leaf cell or get level info.\n   * Should be able to return null/undefined if not found on x or y, thus input `dimIdx` is needed.\n   */\n  MatrixDim.prototype.getUnitLayoutInfo = function (dimIdx, locator) {\n    return dimIdx === this.dimIdx ? locator < this._leavesCount ? this._cells[locator] : undefined : this._levels[locator + this._levels.length];\n  };\n  /**\n   * Get dimension cell by data, including leaves and non-leaves.\n   */\n  MatrixDim.prototype.getCell = function (value) {\n    var ordinal = this._scale.parse(value);\n    return eqNaN(ordinal) ? undefined : this._cells[ordinal];\n  };\n  /**\n   * Get leaf count or get level count.\n   */\n  MatrixDim.prototype.getLocatorCount = function (dimIdx) {\n    return dimIdx === this.dimIdx ? this._leavesCount : this._levels.length;\n  };\n  MatrixDim.prototype.getOrdinalMeta = function () {\n    return this._ordinalMeta;\n  };\n  return MatrixDim;\n}();\nexport { MatrixDim };\nfunction createUniqueValueGenerator(dim) {\n  var dimUpper = dim.toUpperCase();\n  var defaultValReg = new RegExp(\"^\" + dimUpper + \"([0-9]+)$\");\n  var dupBase = 0;\n  function calcDupBase(val) {\n    var matchResult;\n    if (val != null && (matchResult = val.match(defaultValReg))) {\n      dupBase = mathMax(dupBase, +matchResult[1] + 1);\n    }\n  }\n  function makeUniqueValue() {\n    return \"\" + dimUpper + dupBase++;\n  }\n  // Duplicated value is allowed, because the `matrix.x/y.data` can be a tree and it's reasonable\n  // that leaves in different subtrees has the same text. But only the first one is allowed to be\n  // queried by the text, and the other ones can only be queried by index.\n  // Additionally, `matrix.x/y.data: [null, null, ...]` is allowed.\n  function ensureValueUnique(categories, cells) {\n    // A simple way to deduplicate or handle illegal or not specified values to avoid unexpected behaviors.\n    // The tree structure should not be broken even if duplicated.\n    var cateMap = createHashMap();\n    for (var idx = 0; idx < categories.length; idx++) {\n      var value = categories[idx];\n      // value may be set to NullUndefined by users or if illegal.\n      if (value == null || cateMap.get(value) != null) {\n        // Still display the original option.value if duplicated, but loose the ability to query by text.\n        categories[idx] = value = makeUniqueValue();\n        cells[idx].option = defaults({\n          value: value\n        }, cells[idx].option);\n      }\n      cateMap.set(value, true);\n    }\n  }\n  return {\n    calcDupBase: calcDupBase,\n    ensureValueUnique: ensureValueUnique\n  };\n}", "map": {"version": 3, "names": ["createHashMap", "defaults", "each", "eqNaN", "isArray", "isObject", "isString", "Point", "OrdinalMeta", "Ordinal", "WH", "XY", "ListIterator", "createNaNRectLike", "setDimXYValue", "MatrixCellLayoutInfoType", "error", "mathMax", "MatrixDim", "dim", "dimModel", "_cells", "_levels", "dimIdx", "_model", "_uniqueValueGen", "createUniqueValueGenerator", "dimModelData", "get", "process", "env", "NODE_ENV", "_initByDimModelData", "_initBySeriesData", "prototype", "self", "sameLocatorCellsLists", "_cellCount", "_leavesCount", "traverseInitCells", "postInitCells", "firstLeafLocator", "level", "totalSpan", "option", "optionIdx", "invalidOption", "cellOption", "value", "cell", "type", "nonLeaf", "ordinal", "NaN", "id", "span", "xy", "wh", "rect", "push", "childrenSpan", "children", "subSpan", "Math", "max", "categories", "length", "locator", "pop", "val", "calcDupBase", "ensureValueUnique", "ordinalMeta", "_ordinalMeta", "needCollect", "deduplication", "_scale", "idx", "leaf", "_initCellsId", "_initLevelIdOptions", "onCollect", "ordinalNumber", "_setCellId", "levelsLen", "levelOptionList", "levelCfg", "shouldShow", "getShallow", "resetLayoutIterator", "it", "startLocator", "count", "len", "startIdx", "min", "reset", "resetCellIterator", "resetLevelIterator", "getLayout", "outRect", "layout", "getUnitLayoutInfo", "undefined", "getCell", "parse", "getLocatorCount", "getOrdinalMeta", "dimUpper", "toUpperCase", "defaultValReg", "RegExp", "dupBase", "matchResult", "match", "makeUniqueValue", "cells", "cateMap", "set"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/matrix/MatrixDim.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, defaults, each, eqNaN, isArray, isObject, isString } from 'zrender/lib/core/util.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport OrdinalMeta from '../../data/OrdinalMeta.js';\nimport Ordinal from '../../scale/Ordinal.js';\nimport { WH, XY } from '../../util/graphic.js';\nimport { ListIterator } from '../../util/model.js';\nimport { createNaNRectLike, setDimXYValue, MatrixCellLayoutInfoType } from './matrixCoordHelper.js';\nimport { error } from '../../util/log.js';\nimport { mathMax } from '../../util/number.js';\n/**\n * Lifetime: the same with `MatrixModel`, but different from `coord/Matrix`.\n */\nvar MatrixDim = /** @class */function () {\n  function MatrixDim(dim, dimModel) {\n    // Under the current definition, every leave corresponds a unit cell,\n    // and leaves can serve as the locator of cells.\n    // Therefore make sure:\n    //  - The first `_leavesCount` elements in `_cells` are leaves.\n    //  - `_cells[leaf.id[XY[this.dimIdx]]]` is the leaf itself.\n    //  - Leaves of each subtree are placed together, that is, the leaves of a dimCell are:\n    //    `this._cells.slice(dimCell.firstLeafLocator, dimCell.span[XY[this.dimIdx]])`\n    this._cells = [];\n    // Can be visited by `_levels[cell.level]` or `_levels[cell.id[1 - dimIdx] + _levels.length]`.\n    // Items are never be null/undefined after initialized.\n    this._levels = [];\n    this.dim = dim;\n    this.dimIdx = dim === 'x' ? 0 : 1;\n    this._model = dimModel;\n    this._uniqueValueGen = createUniqueValueGenerator(dim);\n    var dimModelData = dimModel.get('data', true);\n    if (dimModelData != null && !isArray(dimModelData)) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(\"Illegal echarts option - matrix.\" + this.dim + \".data must be an array if specified.\");\n      }\n      dimModelData = [];\n    }\n    if (dimModelData) {\n      this._initByDimModelData(dimModelData);\n    } else {\n      this._initBySeriesData();\n    }\n  }\n  MatrixDim.prototype._initByDimModelData = function (dimModelData) {\n    var self = this;\n    var _cells = self._cells;\n    var _levels = self._levels;\n    var sameLocatorCellsLists = []; // Save for sorting.\n    var _cellCount = 0;\n    self._leavesCount = traverseInitCells(dimModelData, 0, 0);\n    postInitCells();\n    return;\n    function traverseInitCells(dimModelData, firstLeafLocator, level) {\n      var totalSpan = 0;\n      if (!dimModelData) {\n        return totalSpan;\n      }\n      each(dimModelData, function (option, optionIdx) {\n        var invalidOption = false;\n        var cellOption;\n        if (isString(option)) {\n          cellOption = {\n            value: option\n          };\n        } else if (isObject(option)) {\n          cellOption = option;\n          if (option.value != null && !isString(option.value)) {\n            invalidOption = true;\n            cellOption = {\n              value: null\n            };\n          }\n        } else {\n          cellOption = {\n            value: null\n          };\n          if (option != null) {\n            invalidOption = true;\n          }\n        }\n        if (invalidOption) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Illegal echarts option - matrix.\" + self.dim + \".data[\" + optionIdx + \"]\" + ' must be `string | {value: string}`.');\n          }\n        }\n        var cell = {\n          type: MatrixCellLayoutInfoType.nonLeaf,\n          ordinal: NaN,\n          level: level,\n          firstLeafLocator: firstLeafLocator,\n          id: new Point(),\n          span: setDimXYValue(new Point(), self.dimIdx, 1, 1),\n          option: cellOption,\n          xy: NaN,\n          wh: NaN,\n          dim: self,\n          rect: createNaNRectLike()\n        };\n        _cellCount++;\n        (sameLocatorCellsLists[firstLeafLocator] || (sameLocatorCellsLists[firstLeafLocator] = [])).push(cell);\n        if (!_levels[level]) {\n          // Create a level only if at least one cell exists.\n          _levels[level] = {\n            type: MatrixCellLayoutInfoType.level,\n            xy: NaN,\n            wh: NaN,\n            option: null,\n            id: new Point(),\n            dim: self\n          };\n        }\n        var childrenSpan = traverseInitCells(cellOption.children, firstLeafLocator, level + 1);\n        var subSpan = Math.max(1, childrenSpan);\n        cell.span[XY[self.dimIdx]] = subSpan;\n        totalSpan += subSpan;\n        firstLeafLocator += subSpan;\n      });\n      return totalSpan;\n    }\n    function postInitCells() {\n      // Sort to make sure the leaves are at the beginning, so that\n      // they can be used as the locator of body cells.\n      var categories = [];\n      while (_cells.length < _cellCount) {\n        for (var locator = 0; locator < sameLocatorCellsLists.length; locator++) {\n          var cell = sameLocatorCellsLists[locator].pop();\n          if (cell) {\n            cell.ordinal = categories.length;\n            var val = cell.option.value;\n            categories.push(val);\n            _cells.push(cell);\n            self._uniqueValueGen.calcDupBase(val);\n          }\n        }\n      }\n      self._uniqueValueGen.ensureValueUnique(categories, _cells);\n      var ordinalMeta = self._ordinalMeta = new OrdinalMeta({\n        categories: categories,\n        needCollect: false,\n        deduplication: false\n      });\n      self._scale = new Ordinal({\n        ordinalMeta: ordinalMeta\n      });\n      for (var idx = 0; idx < self._leavesCount; idx++) {\n        var leaf = self._cells[idx];\n        leaf.type = MatrixCellLayoutInfoType.leaf;\n        // Handle the tree level variation: enlarge the span of the leaves to reach the body cells.\n        leaf.span[XY[1 - self.dimIdx]] = self._levels.length - leaf.level;\n      }\n      self._initCellsId();\n      self._initLevelIdOptions();\n    }\n  };\n  MatrixDim.prototype._initBySeriesData = function () {\n    var self = this;\n    self._leavesCount = 0;\n    self._levels = [{\n      type: MatrixCellLayoutInfoType.level,\n      xy: NaN,\n      wh: NaN,\n      option: null,\n      id: new Point(),\n      dim: self\n    }];\n    self._initLevelIdOptions();\n    var ordinalMeta = self._ordinalMeta = new OrdinalMeta({\n      needCollect: true,\n      deduplication: true,\n      onCollect: function (value, ordinalNumber) {\n        var cell = self._cells[ordinalNumber] = {\n          type: MatrixCellLayoutInfoType.leaf,\n          ordinal: ordinalNumber,\n          level: 0,\n          firstLeafLocator: ordinalNumber,\n          id: new Point(),\n          span: setDimXYValue(new Point(), self.dimIdx, 1, 1),\n          // Theoretically `value` is from `dataset` or `series.data`, so it may be any type.\n          // Do not restrict this case for user's convenience, and here simply convert it to\n          // string for display.\n          option: {\n            value: value + ''\n          },\n          xy: NaN,\n          wh: NaN,\n          dim: self,\n          rect: createNaNRectLike()\n        };\n        self._leavesCount++;\n        self._setCellId(cell);\n      }\n    });\n    self._scale = new Ordinal({\n      ordinalMeta: ordinalMeta\n    });\n  };\n  MatrixDim.prototype._setCellId = function (cell) {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    setDimXYValue(cell.id, dimIdx, cell.firstLeafLocator, cell.level - levelsLen);\n  };\n  MatrixDim.prototype._initCellsId = function () {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    each(this._cells, function (cell) {\n      setDimXYValue(cell.id, dimIdx, cell.firstLeafLocator, cell.level - levelsLen);\n    });\n  };\n  MatrixDim.prototype._initLevelIdOptions = function () {\n    var levelsLen = this._levels.length;\n    var dimIdx = this.dimIdx;\n    var levelOptionList = this._model.get('levels', true);\n    levelOptionList = isArray(levelOptionList) ? levelOptionList : [];\n    each(this._levels, function (levelCfg, level) {\n      setDimXYValue(levelCfg.id, dimIdx, 0, level - levelsLen);\n      levelCfg.option = levelOptionList[level];\n    });\n  };\n  MatrixDim.prototype.shouldShow = function () {\n    return !!this._model.getShallow('show', true);\n  };\n  /**\n   * Iterate leaves (they are layout units) if dimIdx === this.dimIdx.\n   * Iterate levels if dimIdx !== this.dimIdx.\n   */\n  MatrixDim.prototype.resetLayoutIterator = function (it, dimIdx, startLocator, count) {\n    it = it || new ListIterator();\n    if (dimIdx === this.dimIdx) {\n      var len = this._leavesCount;\n      var startIdx = startLocator != null ? Math.max(0, startLocator) : 0;\n      count = count != null ? Math.min(count, len) : len;\n      it.reset(this._cells, startIdx, startIdx + count);\n    } else {\n      var len = this._levels.length;\n      // Corner locator is from `-this._levels.length` to `-1`.\n      var startIdx = startLocator != null ? Math.max(0, startLocator + len) : 0;\n      count = count != null ? Math.min(count, len) : len;\n      it.reset(this._levels, startIdx, startIdx + count);\n    }\n    return it;\n  };\n  MatrixDim.prototype.resetCellIterator = function (it) {\n    return (it || new ListIterator()).reset(this._cells, 0);\n  };\n  MatrixDim.prototype.resetLevelIterator = function (it) {\n    return (it || new ListIterator()).reset(this._levels, 0);\n  };\n  MatrixDim.prototype.getLayout = function (outRect, dimIdx, locator) {\n    var layout = this.getUnitLayoutInfo(dimIdx, locator);\n    outRect[XY[dimIdx]] = layout ? layout.xy : NaN;\n    outRect[WH[dimIdx]] = layout ? layout.wh : NaN;\n  };\n  /**\n   * Get leaf cell or get level info.\n   * Should be able to return null/undefined if not found on x or y, thus input `dimIdx` is needed.\n   */\n  MatrixDim.prototype.getUnitLayoutInfo = function (dimIdx, locator) {\n    return dimIdx === this.dimIdx ? locator < this._leavesCount ? this._cells[locator] : undefined : this._levels[locator + this._levels.length];\n  };\n  /**\n   * Get dimension cell by data, including leaves and non-leaves.\n   */\n  MatrixDim.prototype.getCell = function (value) {\n    var ordinal = this._scale.parse(value);\n    return eqNaN(ordinal) ? undefined : this._cells[ordinal];\n  };\n  /**\n   * Get leaf count or get level count.\n   */\n  MatrixDim.prototype.getLocatorCount = function (dimIdx) {\n    return dimIdx === this.dimIdx ? this._leavesCount : this._levels.length;\n  };\n  MatrixDim.prototype.getOrdinalMeta = function () {\n    return this._ordinalMeta;\n  };\n  return MatrixDim;\n}();\nexport { MatrixDim };\nfunction createUniqueValueGenerator(dim) {\n  var dimUpper = dim.toUpperCase();\n  var defaultValReg = new RegExp(\"^\" + dimUpper + \"([0-9]+)$\");\n  var dupBase = 0;\n  function calcDupBase(val) {\n    var matchResult;\n    if (val != null && (matchResult = val.match(defaultValReg))) {\n      dupBase = mathMax(dupBase, +matchResult[1] + 1);\n    }\n  }\n  function makeUniqueValue() {\n    return \"\" + dimUpper + dupBase++;\n  }\n  // Duplicated value is allowed, because the `matrix.x/y.data` can be a tree and it's reasonable\n  // that leaves in different subtrees has the same text. But only the first one is allowed to be\n  // queried by the text, and the other ones can only be queried by index.\n  // Additionally, `matrix.x/y.data: [null, null, ...]` is allowed.\n  function ensureValueUnique(categories, cells) {\n    // A simple way to deduplicate or handle illegal or not specified values to avoid unexpected behaviors.\n    // The tree structure should not be broken even if duplicated.\n    var cateMap = createHashMap();\n    for (var idx = 0; idx < categories.length; idx++) {\n      var value = categories[idx];\n      // value may be set to NullUndefined by users or if illegal.\n      if (value == null || cateMap.get(value) != null) {\n        // Still display the original option.value if duplicated, but loose the ability to query by text.\n        categories[idx] = value = makeUniqueValue();\n        cells[idx].option = defaults({\n          value: value\n        }, cells[idx].option);\n      }\n      cateMap.set(value, true);\n    }\n  }\n  return {\n    calcDupBase: calcDupBase,\n    ensureValueUnique: ensureValueUnique\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,0BAA0B;AAC5G,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,SAASC,EAAE,EAAEC,EAAE,QAAQ,uBAAuB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,wBAAwB,QAAQ,wBAAwB;AACnG,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAACC,GAAG,EAAEC,QAAQ,EAAE;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA;IACA,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACI,MAAM,GAAGJ,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;IACjC,IAAI,CAACK,MAAM,GAAGJ,QAAQ;IACtB,IAAI,CAACK,eAAe,GAAGC,0BAA0B,CAACP,GAAG,CAAC;IACtD,IAAIQ,YAAY,GAAGP,QAAQ,CAACQ,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;IAC7C,IAAID,YAAY,IAAI,IAAI,IAAI,CAACvB,OAAO,CAACuB,YAAY,CAAC,EAAE;MAClD,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCf,KAAK,CAAC,kCAAkC,GAAG,IAAI,CAACG,GAAG,GAAG,sCAAsC,CAAC;MAC/F;MACAQ,YAAY,GAAG,EAAE;IACnB;IACA,IAAIA,YAAY,EAAE;MAChB,IAAI,CAACK,mBAAmB,CAACL,YAAY,CAAC;IACxC,CAAC,MAAM;MACL,IAAI,CAACM,iBAAiB,CAAC,CAAC;IAC1B;EACF;EACAf,SAAS,CAACgB,SAAS,CAACF,mBAAmB,GAAG,UAAUL,YAAY,EAAE;IAChE,IAAIQ,IAAI,GAAG,IAAI;IACf,IAAId,MAAM,GAAGc,IAAI,CAACd,MAAM;IACxB,IAAIC,OAAO,GAAGa,IAAI,CAACb,OAAO;IAC1B,IAAIc,qBAAqB,GAAG,EAAE,CAAC,CAAC;IAChC,IAAIC,UAAU,GAAG,CAAC;IAClBF,IAAI,CAACG,YAAY,GAAGC,iBAAiB,CAACZ,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;IACzDa,aAAa,CAAC,CAAC;IACf;IACA,SAASD,iBAAiBA,CAACZ,YAAY,EAAEc,gBAAgB,EAAEC,KAAK,EAAE;MAChE,IAAIC,SAAS,GAAG,CAAC;MACjB,IAAI,CAAChB,YAAY,EAAE;QACjB,OAAOgB,SAAS;MAClB;MACAzC,IAAI,CAACyB,YAAY,EAAE,UAAUiB,MAAM,EAAEC,SAAS,EAAE;QAC9C,IAAIC,aAAa,GAAG,KAAK;QACzB,IAAIC,UAAU;QACd,IAAIzC,QAAQ,CAACsC,MAAM,CAAC,EAAE;UACpBG,UAAU,GAAG;YACXC,KAAK,EAAEJ;UACT,CAAC;QACH,CAAC,MAAM,IAAIvC,QAAQ,CAACuC,MAAM,CAAC,EAAE;UAC3BG,UAAU,GAAGH,MAAM;UACnB,IAAIA,MAAM,CAACI,KAAK,IAAI,IAAI,IAAI,CAAC1C,QAAQ,CAACsC,MAAM,CAACI,KAAK,CAAC,EAAE;YACnDF,aAAa,GAAG,IAAI;YACpBC,UAAU,GAAG;cACXC,KAAK,EAAE;YACT,CAAC;UACH;QACF,CAAC,MAAM;UACLD,UAAU,GAAG;YACXC,KAAK,EAAE;UACT,CAAC;UACD,IAAIJ,MAAM,IAAI,IAAI,EAAE;YAClBE,aAAa,GAAG,IAAI;UACtB;QACF;QACA,IAAIA,aAAa,EAAE;UACjB,IAAIjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCf,KAAK,CAAC,kCAAkC,GAAGmB,IAAI,CAAChB,GAAG,GAAG,QAAQ,GAAG0B,SAAS,GAAG,GAAG,GAAG,sCAAsC,CAAC;UAC5H;QACF;QACA,IAAII,IAAI,GAAG;UACTC,IAAI,EAAEnC,wBAAwB,CAACoC,OAAO;UACtCC,OAAO,EAAEC,GAAG;UACZX,KAAK,EAAEA,KAAK;UACZD,gBAAgB,EAAEA,gBAAgB;UAClCa,EAAE,EAAE,IAAI/C,KAAK,CAAC,CAAC;UACfgD,IAAI,EAAEzC,aAAa,CAAC,IAAIP,KAAK,CAAC,CAAC,EAAE4B,IAAI,CAACZ,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;UACnDqB,MAAM,EAAEG,UAAU;UAClBS,EAAE,EAAEH,GAAG;UACPI,EAAE,EAAEJ,GAAG;UACPlC,GAAG,EAAEgB,IAAI;UACTuB,IAAI,EAAE7C,iBAAiB,CAAC;QAC1B,CAAC;QACDwB,UAAU,EAAE;QACZ,CAACD,qBAAqB,CAACK,gBAAgB,CAAC,KAAKL,qBAAqB,CAACK,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAEkB,IAAI,CAACV,IAAI,CAAC;QACtG,IAAI,CAAC3B,OAAO,CAACoB,KAAK,CAAC,EAAE;UACnB;UACApB,OAAO,CAACoB,KAAK,CAAC,GAAG;YACfQ,IAAI,EAAEnC,wBAAwB,CAAC2B,KAAK;YACpCc,EAAE,EAAEH,GAAG;YACPI,EAAE,EAAEJ,GAAG;YACPT,MAAM,EAAE,IAAI;YACZU,EAAE,EAAE,IAAI/C,KAAK,CAAC,CAAC;YACfY,GAAG,EAAEgB;UACP,CAAC;QACH;QACA,IAAIyB,YAAY,GAAGrB,iBAAiB,CAACQ,UAAU,CAACc,QAAQ,EAAEpB,gBAAgB,EAAEC,KAAK,GAAG,CAAC,CAAC;QACtF,IAAIoB,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,YAAY,CAAC;QACvCX,IAAI,CAACM,IAAI,CAAC5C,EAAE,CAACwB,IAAI,CAACZ,MAAM,CAAC,CAAC,GAAGuC,OAAO;QACpCnB,SAAS,IAAImB,OAAO;QACpBrB,gBAAgB,IAAIqB,OAAO;MAC7B,CAAC,CAAC;MACF,OAAOnB,SAAS;IAClB;IACA,SAASH,aAAaA,CAAA,EAAG;MACvB;MACA;MACA,IAAIyB,UAAU,GAAG,EAAE;MACnB,OAAO5C,MAAM,CAAC6C,MAAM,GAAG7B,UAAU,EAAE;QACjC,KAAK,IAAI8B,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG/B,qBAAqB,CAAC8B,MAAM,EAAEC,OAAO,EAAE,EAAE;UACvE,IAAIlB,IAAI,GAAGb,qBAAqB,CAAC+B,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC;UAC/C,IAAInB,IAAI,EAAE;YACRA,IAAI,CAACG,OAAO,GAAGa,UAAU,CAACC,MAAM;YAChC,IAAIG,GAAG,GAAGpB,IAAI,CAACL,MAAM,CAACI,KAAK;YAC3BiB,UAAU,CAACN,IAAI,CAACU,GAAG,CAAC;YACpBhD,MAAM,CAACsC,IAAI,CAACV,IAAI,CAAC;YACjBd,IAAI,CAACV,eAAe,CAAC6C,WAAW,CAACD,GAAG,CAAC;UACvC;QACF;MACF;MACAlC,IAAI,CAACV,eAAe,CAAC8C,iBAAiB,CAACN,UAAU,EAAE5C,MAAM,CAAC;MAC1D,IAAImD,WAAW,GAAGrC,IAAI,CAACsC,YAAY,GAAG,IAAIjE,WAAW,CAAC;QACpDyD,UAAU,EAAEA,UAAU;QACtBS,WAAW,EAAE,KAAK;QAClBC,aAAa,EAAE;MACjB,CAAC,CAAC;MACFxC,IAAI,CAACyC,MAAM,GAAG,IAAInE,OAAO,CAAC;QACxB+D,WAAW,EAAEA;MACf,CAAC,CAAC;MACF,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG1C,IAAI,CAACG,YAAY,EAAEuC,GAAG,EAAE,EAAE;QAChD,IAAIC,IAAI,GAAG3C,IAAI,CAACd,MAAM,CAACwD,GAAG,CAAC;QAC3BC,IAAI,CAAC5B,IAAI,GAAGnC,wBAAwB,CAAC+D,IAAI;QACzC;QACAA,IAAI,CAACvB,IAAI,CAAC5C,EAAE,CAAC,CAAC,GAAGwB,IAAI,CAACZ,MAAM,CAAC,CAAC,GAAGY,IAAI,CAACb,OAAO,CAAC4C,MAAM,GAAGY,IAAI,CAACpC,KAAK;MACnE;MACAP,IAAI,CAAC4C,YAAY,CAAC,CAAC;MACnB5C,IAAI,CAAC6C,mBAAmB,CAAC,CAAC;IAC5B;EACF,CAAC;EACD9D,SAAS,CAACgB,SAAS,CAACD,iBAAiB,GAAG,YAAY;IAClD,IAAIE,IAAI,GAAG,IAAI;IACfA,IAAI,CAACG,YAAY,GAAG,CAAC;IACrBH,IAAI,CAACb,OAAO,GAAG,CAAC;MACd4B,IAAI,EAAEnC,wBAAwB,CAAC2B,KAAK;MACpCc,EAAE,EAAEH,GAAG;MACPI,EAAE,EAAEJ,GAAG;MACPT,MAAM,EAAE,IAAI;MACZU,EAAE,EAAE,IAAI/C,KAAK,CAAC,CAAC;MACfY,GAAG,EAAEgB;IACP,CAAC,CAAC;IACFA,IAAI,CAAC6C,mBAAmB,CAAC,CAAC;IAC1B,IAAIR,WAAW,GAAGrC,IAAI,CAACsC,YAAY,GAAG,IAAIjE,WAAW,CAAC;MACpDkE,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,IAAI;MACnBM,SAAS,EAAE,SAAAA,CAAUjC,KAAK,EAAEkC,aAAa,EAAE;QACzC,IAAIjC,IAAI,GAAGd,IAAI,CAACd,MAAM,CAAC6D,aAAa,CAAC,GAAG;UACtChC,IAAI,EAAEnC,wBAAwB,CAAC+D,IAAI;UACnC1B,OAAO,EAAE8B,aAAa;UACtBxC,KAAK,EAAE,CAAC;UACRD,gBAAgB,EAAEyC,aAAa;UAC/B5B,EAAE,EAAE,IAAI/C,KAAK,CAAC,CAAC;UACfgD,IAAI,EAAEzC,aAAa,CAAC,IAAIP,KAAK,CAAC,CAAC,EAAE4B,IAAI,CAACZ,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;UACnD;UACA;UACA;UACAqB,MAAM,EAAE;YACNI,KAAK,EAAEA,KAAK,GAAG;UACjB,CAAC;UACDQ,EAAE,EAAEH,GAAG;UACPI,EAAE,EAAEJ,GAAG;UACPlC,GAAG,EAAEgB,IAAI;UACTuB,IAAI,EAAE7C,iBAAiB,CAAC;QAC1B,CAAC;QACDsB,IAAI,CAACG,YAAY,EAAE;QACnBH,IAAI,CAACgD,UAAU,CAAClC,IAAI,CAAC;MACvB;IACF,CAAC,CAAC;IACFd,IAAI,CAACyC,MAAM,GAAG,IAAInE,OAAO,CAAC;MACxB+D,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC;EACDtD,SAAS,CAACgB,SAAS,CAACiD,UAAU,GAAG,UAAUlC,IAAI,EAAE;IAC/C,IAAImC,SAAS,GAAG,IAAI,CAAC9D,OAAO,CAAC4C,MAAM;IACnC,IAAI3C,MAAM,GAAG,IAAI,CAACA,MAAM;IACxBT,aAAa,CAACmC,IAAI,CAACK,EAAE,EAAE/B,MAAM,EAAE0B,IAAI,CAACR,gBAAgB,EAAEQ,IAAI,CAACP,KAAK,GAAG0C,SAAS,CAAC;EAC/E,CAAC;EACDlE,SAAS,CAACgB,SAAS,CAAC6C,YAAY,GAAG,YAAY;IAC7C,IAAIK,SAAS,GAAG,IAAI,CAAC9D,OAAO,CAAC4C,MAAM;IACnC,IAAI3C,MAAM,GAAG,IAAI,CAACA,MAAM;IACxBrB,IAAI,CAAC,IAAI,CAACmB,MAAM,EAAE,UAAU4B,IAAI,EAAE;MAChCnC,aAAa,CAACmC,IAAI,CAACK,EAAE,EAAE/B,MAAM,EAAE0B,IAAI,CAACR,gBAAgB,EAAEQ,IAAI,CAACP,KAAK,GAAG0C,SAAS,CAAC;IAC/E,CAAC,CAAC;EACJ,CAAC;EACDlE,SAAS,CAACgB,SAAS,CAAC8C,mBAAmB,GAAG,YAAY;IACpD,IAAII,SAAS,GAAG,IAAI,CAAC9D,OAAO,CAAC4C,MAAM;IACnC,IAAI3C,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI8D,eAAe,GAAG,IAAI,CAAC7D,MAAM,CAACI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;IACrDyD,eAAe,GAAGjF,OAAO,CAACiF,eAAe,CAAC,GAAGA,eAAe,GAAG,EAAE;IACjEnF,IAAI,CAAC,IAAI,CAACoB,OAAO,EAAE,UAAUgE,QAAQ,EAAE5C,KAAK,EAAE;MAC5C5B,aAAa,CAACwE,QAAQ,CAAChC,EAAE,EAAE/B,MAAM,EAAE,CAAC,EAAEmB,KAAK,GAAG0C,SAAS,CAAC;MACxDE,QAAQ,CAAC1C,MAAM,GAAGyC,eAAe,CAAC3C,KAAK,CAAC;IAC1C,CAAC,CAAC;EACJ,CAAC;EACDxB,SAAS,CAACgB,SAAS,CAACqD,UAAU,GAAG,YAAY;IAC3C,OAAO,CAAC,CAAC,IAAI,CAAC/D,MAAM,CAACgE,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;EAC/C,CAAC;EACD;AACF;AACA;AACA;EACEtE,SAAS,CAACgB,SAAS,CAACuD,mBAAmB,GAAG,UAAUC,EAAE,EAAEnE,MAAM,EAAEoE,YAAY,EAAEC,KAAK,EAAE;IACnFF,EAAE,GAAGA,EAAE,IAAI,IAAI9E,YAAY,CAAC,CAAC;IAC7B,IAAIW,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;MAC1B,IAAIsE,GAAG,GAAG,IAAI,CAACvD,YAAY;MAC3B,IAAIwD,QAAQ,GAAGH,YAAY,IAAI,IAAI,GAAG5B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE2B,YAAY,CAAC,GAAG,CAAC;MACnEC,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG7B,IAAI,CAACgC,GAAG,CAACH,KAAK,EAAEC,GAAG,CAAC,GAAGA,GAAG;MAClDH,EAAE,CAACM,KAAK,CAAC,IAAI,CAAC3E,MAAM,EAAEyE,QAAQ,EAAEA,QAAQ,GAAGF,KAAK,CAAC;IACnD,CAAC,MAAM;MACL,IAAIC,GAAG,GAAG,IAAI,CAACvE,OAAO,CAAC4C,MAAM;MAC7B;MACA,IAAI4B,QAAQ,GAAGH,YAAY,IAAI,IAAI,GAAG5B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE2B,YAAY,GAAGE,GAAG,CAAC,GAAG,CAAC;MACzED,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG7B,IAAI,CAACgC,GAAG,CAACH,KAAK,EAAEC,GAAG,CAAC,GAAGA,GAAG;MAClDH,EAAE,CAACM,KAAK,CAAC,IAAI,CAAC1E,OAAO,EAAEwE,QAAQ,EAAEA,QAAQ,GAAGF,KAAK,CAAC;IACpD;IACA,OAAOF,EAAE;EACX,CAAC;EACDxE,SAAS,CAACgB,SAAS,CAAC+D,iBAAiB,GAAG,UAAUP,EAAE,EAAE;IACpD,OAAO,CAACA,EAAE,IAAI,IAAI9E,YAAY,CAAC,CAAC,EAAEoF,KAAK,CAAC,IAAI,CAAC3E,MAAM,EAAE,CAAC,CAAC;EACzD,CAAC;EACDH,SAAS,CAACgB,SAAS,CAACgE,kBAAkB,GAAG,UAAUR,EAAE,EAAE;IACrD,OAAO,CAACA,EAAE,IAAI,IAAI9E,YAAY,CAAC,CAAC,EAAEoF,KAAK,CAAC,IAAI,CAAC1E,OAAO,EAAE,CAAC,CAAC;EAC1D,CAAC;EACDJ,SAAS,CAACgB,SAAS,CAACiE,SAAS,GAAG,UAAUC,OAAO,EAAE7E,MAAM,EAAE4C,OAAO,EAAE;IAClE,IAAIkC,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAAC/E,MAAM,EAAE4C,OAAO,CAAC;IACpDiC,OAAO,CAACzF,EAAE,CAACY,MAAM,CAAC,CAAC,GAAG8E,MAAM,GAAGA,MAAM,CAAC7C,EAAE,GAAGH,GAAG;IAC9C+C,OAAO,CAAC1F,EAAE,CAACa,MAAM,CAAC,CAAC,GAAG8E,MAAM,GAAGA,MAAM,CAAC5C,EAAE,GAAGJ,GAAG;EAChD,CAAC;EACD;AACF;AACA;AACA;EACEnC,SAAS,CAACgB,SAAS,CAACoE,iBAAiB,GAAG,UAAU/E,MAAM,EAAE4C,OAAO,EAAE;IACjE,OAAO5C,MAAM,KAAK,IAAI,CAACA,MAAM,GAAG4C,OAAO,GAAG,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAACjB,MAAM,CAAC8C,OAAO,CAAC,GAAGoC,SAAS,GAAG,IAAI,CAACjF,OAAO,CAAC6C,OAAO,GAAG,IAAI,CAAC7C,OAAO,CAAC4C,MAAM,CAAC;EAC9I,CAAC;EACD;AACF;AACA;EACEhD,SAAS,CAACgB,SAAS,CAACsE,OAAO,GAAG,UAAUxD,KAAK,EAAE;IAC7C,IAAII,OAAO,GAAG,IAAI,CAACwB,MAAM,CAAC6B,KAAK,CAACzD,KAAK,CAAC;IACtC,OAAO7C,KAAK,CAACiD,OAAO,CAAC,GAAGmD,SAAS,GAAG,IAAI,CAAClF,MAAM,CAAC+B,OAAO,CAAC;EAC1D,CAAC;EACD;AACF;AACA;EACElC,SAAS,CAACgB,SAAS,CAACwE,eAAe,GAAG,UAAUnF,MAAM,EAAE;IACtD,OAAOA,MAAM,KAAK,IAAI,CAACA,MAAM,GAAG,IAAI,CAACe,YAAY,GAAG,IAAI,CAAChB,OAAO,CAAC4C,MAAM;EACzE,CAAC;EACDhD,SAAS,CAACgB,SAAS,CAACyE,cAAc,GAAG,YAAY;IAC/C,OAAO,IAAI,CAAClC,YAAY;EAC1B,CAAC;EACD,OAAOvD,SAAS;AAClB,CAAC,CAAC,CAAC;AACH,SAASA,SAAS;AAClB,SAASQ,0BAA0BA,CAACP,GAAG,EAAE;EACvC,IAAIyF,QAAQ,GAAGzF,GAAG,CAAC0F,WAAW,CAAC,CAAC;EAChC,IAAIC,aAAa,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGH,QAAQ,GAAG,WAAW,CAAC;EAC5D,IAAII,OAAO,GAAG,CAAC;EACf,SAAS1C,WAAWA,CAACD,GAAG,EAAE;IACxB,IAAI4C,WAAW;IACf,IAAI5C,GAAG,IAAI,IAAI,KAAK4C,WAAW,GAAG5C,GAAG,CAAC6C,KAAK,CAACJ,aAAa,CAAC,CAAC,EAAE;MAC3DE,OAAO,GAAG/F,OAAO,CAAC+F,OAAO,EAAE,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjD;EACF;EACA,SAASE,eAAeA,CAAA,EAAG;IACzB,OAAO,EAAE,GAAGP,QAAQ,GAAGI,OAAO,EAAE;EAClC;EACA;EACA;EACA;EACA;EACA,SAASzC,iBAAiBA,CAACN,UAAU,EAAEmD,KAAK,EAAE;IAC5C;IACA;IACA,IAAIC,OAAO,GAAGrH,aAAa,CAAC,CAAC;IAC7B,KAAK,IAAI6E,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGZ,UAAU,CAACC,MAAM,EAAEW,GAAG,EAAE,EAAE;MAChD,IAAI7B,KAAK,GAAGiB,UAAU,CAACY,GAAG,CAAC;MAC3B;MACA,IAAI7B,KAAK,IAAI,IAAI,IAAIqE,OAAO,CAACzF,GAAG,CAACoB,KAAK,CAAC,IAAI,IAAI,EAAE;QAC/C;QACAiB,UAAU,CAACY,GAAG,CAAC,GAAG7B,KAAK,GAAGmE,eAAe,CAAC,CAAC;QAC3CC,KAAK,CAACvC,GAAG,CAAC,CAACjC,MAAM,GAAG3C,QAAQ,CAAC;UAC3B+C,KAAK,EAAEA;QACT,CAAC,EAAEoE,KAAK,CAACvC,GAAG,CAAC,CAACjC,MAAM,CAAC;MACvB;MACAyE,OAAO,CAACC,GAAG,CAACtE,KAAK,EAAE,IAAI,CAAC;IAC1B;EACF;EACA,OAAO;IACLsB,WAAW,EAAEA,WAAW;IACxBC,iBAAiB,EAAEA;EACrB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}