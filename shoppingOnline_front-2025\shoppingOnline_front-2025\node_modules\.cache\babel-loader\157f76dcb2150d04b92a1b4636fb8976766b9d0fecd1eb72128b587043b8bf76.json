{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { toCamelCase } from '../../util/format.js';\nimport env from 'zrender/lib/core/env.js';\n/* global document */\nexport function shouldTooltipConfine(tooltipModel) {\n  var confineOption = tooltipModel.get('confine');\n  return confineOption != null ? !!confineOption\n  // In richText mode, the outside part can not be visible.\n  : tooltipModel.get('renderMode') === 'richText';\n}\nfunction testStyle(styleProps) {\n  if (!env.domSupported) {\n    return;\n  }\n  var style = document.documentElement.style;\n  for (var i = 0, len = styleProps.length; i < len; i++) {\n    if (styleProps[i] in style) {\n      return styleProps[i];\n    }\n  }\n}\nexport var TRANSFORM_VENDOR = testStyle(['transform', 'webkitTransform', 'OTransform', 'MozTransform', 'msTransform']);\nexport var TRANSITION_VENDOR = testStyle(['webkitTransition', 'transition', 'OTransition', 'MozTransition', 'msTransition']);\nexport function toCSSVendorPrefix(styleVendor, styleProp) {\n  if (!styleVendor) {\n    return styleProp;\n  }\n  styleProp = toCamelCase(styleProp, true);\n  var idx = styleVendor.indexOf(styleProp);\n  styleVendor = idx === -1 ? styleProp : \"-\" + styleVendor.slice(0, idx) + \"-\" + styleProp;\n  return styleVendor.toLowerCase();\n}\nexport function getComputedStyle(el, style) {\n  var stl = el.currentStyle || document.defaultView && document.defaultView.getComputedStyle(el);\n  return stl ? style ? stl[style] : stl : null;\n}", "map": {"version": 3, "names": ["toCamelCase", "env", "shouldTooltipConfine", "tooltipModel", "confineOption", "get", "testStyle", "styleProps", "domSupported", "style", "document", "documentElement", "i", "len", "length", "TRANSFORM_VENDOR", "TRANSITION_VENDOR", "toCSSVendorPrefix", "styleVendor", "styleProp", "idx", "indexOf", "slice", "toLowerCase", "getComputedStyle", "el", "stl", "currentStyle", "defaultView"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/tooltip/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { toCamelCase } from '../../util/format.js';\nimport env from 'zrender/lib/core/env.js';\n/* global document */\nexport function shouldTooltipConfine(tooltipModel) {\n  var confineOption = tooltipModel.get('confine');\n  return confineOption != null ? !!confineOption\n  // In richText mode, the outside part can not be visible.\n  : tooltipModel.get('renderMode') === 'richText';\n}\nfunction testStyle(styleProps) {\n  if (!env.domSupported) {\n    return;\n  }\n  var style = document.documentElement.style;\n  for (var i = 0, len = styleProps.length; i < len; i++) {\n    if (styleProps[i] in style) {\n      return styleProps[i];\n    }\n  }\n}\nexport var TRANSFORM_VENDOR = testStyle(['transform', 'webkitTransform', 'OTransform', 'MozTransform', 'msTransform']);\nexport var TRANSITION_VENDOR = testStyle(['webkitTransition', 'transition', 'OTransition', 'MozTransition', 'msTransition']);\nexport function toCSSVendorPrefix(styleVendor, styleProp) {\n  if (!styleVendor) {\n    return styleProp;\n  }\n  styleProp = toCamelCase(styleProp, true);\n  var idx = styleVendor.indexOf(styleProp);\n  styleVendor = idx === -1 ? styleProp : \"-\" + styleVendor.slice(0, idx) + \"-\" + styleProp;\n  return styleVendor.toLowerCase();\n}\nexport function getComputedStyle(el, style) {\n  var stl = el.currentStyle || document.defaultView && document.defaultView.getComputedStyle(el);\n  return stl ? style ? stl[style] : stl : null;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,GAAG,MAAM,yBAAyB;AACzC;AACA,OAAO,SAASC,oBAAoBA,CAACC,YAAY,EAAE;EACjD,IAAIC,aAAa,GAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC;EAC/C,OAAOD,aAAa,IAAI,IAAI,GAAG,CAAC,CAACA;EACjC;EAAA,EACED,YAAY,CAACE,GAAG,CAAC,YAAY,CAAC,KAAK,UAAU;AACjD;AACA,SAASC,SAASA,CAACC,UAAU,EAAE;EAC7B,IAAI,CAACN,GAAG,CAACO,YAAY,EAAE;IACrB;EACF;EACA,IAAIC,KAAK,GAAGC,QAAQ,CAACC,eAAe,CAACF,KAAK;EAC1C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,UAAU,CAACO,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACrD,IAAIL,UAAU,CAACK,CAAC,CAAC,IAAIH,KAAK,EAAE;MAC1B,OAAOF,UAAU,CAACK,CAAC,CAAC;IACtB;EACF;AACF;AACA,OAAO,IAAIG,gBAAgB,GAAGT,SAAS,CAAC,CAAC,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;AACtH,OAAO,IAAIU,iBAAiB,GAAGV,SAAS,CAAC,CAAC,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;AAC5H,OAAO,SAASW,iBAAiBA,CAACC,WAAW,EAAEC,SAAS,EAAE;EACxD,IAAI,CAACD,WAAW,EAAE;IAChB,OAAOC,SAAS;EAClB;EACAA,SAAS,GAAGnB,WAAW,CAACmB,SAAS,EAAE,IAAI,CAAC;EACxC,IAAIC,GAAG,GAAGF,WAAW,CAACG,OAAO,CAACF,SAAS,CAAC;EACxCD,WAAW,GAAGE,GAAG,KAAK,CAAC,CAAC,GAAGD,SAAS,GAAG,GAAG,GAAGD,WAAW,CAACI,KAAK,CAAC,CAAC,EAAEF,GAAG,CAAC,GAAG,GAAG,GAAGD,SAAS;EACxF,OAAOD,WAAW,CAACK,WAAW,CAAC,CAAC;AAClC;AACA,OAAO,SAASC,gBAAgBA,CAACC,EAAE,EAAEhB,KAAK,EAAE;EAC1C,IAAIiB,GAAG,GAAGD,EAAE,CAACE,YAAY,IAAIjB,QAAQ,CAACkB,WAAW,IAAIlB,QAAQ,CAACkB,WAAW,CAACJ,gBAAgB,CAACC,EAAE,CAAC;EAC9F,OAAOC,GAAG,GAAGjB,KAAK,GAAGiB,GAAG,CAACjB,KAAK,CAAC,GAAGiB,GAAG,GAAG,IAAI;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}