{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nimport { getScaleBreakHelper } from './break.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    // `Scale#parse` (and its overrids) are typically applied at the axis values input\n    // in echarts option. e.g., `axis.min/max`, `dataZoom.min/max`, etc.\n    // but `series.data` is not included, which uses `dataValueHelper.ts`#`parseDataValue`.\n    // `Scale#parse` originally introduced in fb8c813215098b9d2458966229bb95c510883d5e\n    // at 2016 for dataZoom start/end settings (See `parseAxisModelMinMax`).\n    //\n    // Historically `scale/Interval.ts` returns the input value directly. But numeric\n    // values (such as a number-like string '123') effectively passed through here and\n    // were involved in calculations, which was error-prone and inconsistent with the\n    // declared TS return type. Previously such issues are fixed separately in different\n    // places case by case (such as #2475).\n    //\n    // Now, we perform actual parse to ensure its `number` type here. The parsing rule\n    // follows the series data parsing rule (`dataValueHelper.ts`#`parseDataValue`)\n    // and maintains compatibility as much as possible (thus a more strict parsing\n    // `number.ts`#`numericToNumber` is not used here.)\n    //\n    // FIXME: `ScaleDataValue` also need to be modified to include numeric string type,\n    //  since it effectively does.\n    return val == null || val === '' ? NaN\n    // If string (like '-'), using '+' parse to NaN\n    // If object, also parse to NaN\n    : Number(val);\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return this._calculator.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return this._calculator.scale(val, this._extent);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\n   * @override\n   */\n  IntervalScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var scaleBreakHelper = getScaleBreakHelper();\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    if (opt.breakTicks === 'only_break' && scaleBreakHelper) {\n      scaleBreakHelper.addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (opt.expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var estimateNiceMultiple = function (tickVal, targetTick) {\n      return Math.round((targetTick - tickVal) / interval);\n    };\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (this._brkCtx) {\n        var moreMultiple = this._brkCtx.calcNiceTickMultiple(tick, estimateNiceMultiple);\n        if (moreMultiple >= 0) {\n          tick = roundNumber(tick + moreMultiple * interval, intervalPrecision);\n        }\n      }\n      if (ticks.length > 0 && tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (opt.expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    if (scaleBreakHelper) {\n      scaleBreakHelper.pruneTicksByBreak(opt.pruneByBreak, ticks, this._brkCtx.breaks, function (item) {\n        return item.value;\n      }, this._interval, this._extent);\n    }\n    if (opt.breakTicks !== 'none' && scaleBreakHelper) {\n      scaleBreakHelper.addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks({\n      expandToNicedExtent: true\n    });\n    // NOTE: In log-scale, do not support minor ticks when breaks exist.\n    //  because currently log-scale minor ticks is calculated based on raw values\n    //  rather than log-transformed value, due to an odd effect when breaks exist.\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      if (prevTick[\"break\"] || nextTick[\"break\"]) {\n        // Do not build minor ticks to the adjacent ticks to breaks ticks,\n        // since the interval might be irregular.\n        continue;\n      }\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      var minorIntervalPrecision = helper.getIntervalPrecision(minorInterval);\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval, minorIntervalPrecision);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      var scaleBreakHelper = getScaleBreakHelper();\n      scaleBreakHelper && scaleBreakHelper.pruneTicksByBreak('auto', minorTicksGroup, this._getNonTransBreaks(), function (value) {\n        return value;\n      }, this._interval, extent);\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  IntervalScale.prototype._getNonTransBreaks = function () {\n    return this._brkCtx ? this._brkCtx.breaks : [];\n  };\n  /**\n   * @param opt.precision If 'auto', use nice presision.\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\n   * FIXME: refactor - disallow override, use composition instead.\n   *\n   * The override of `calcNiceTicks` should ensure these members are provided:\n   *  this._intervalPrecision\n   *  this._interval\n   *\n   * @param splitNumber By default `5`.\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent.slice();\n    var span = this._getExtentSpanWithBreaks();\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n      this._innerSetExtent(extent[0], extent[1]);\n      extent = this._extent.slice();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, span, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent.slice();\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n    extent = this._extent.slice();\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    var interval = this._interval;\n    var intervalPrecition = this._intervalPrecision;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval, intervalPrecition);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval, intervalPrecition);\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;", "map": {"version": 3, "names": ["__extends", "numberUtil", "formatUtil", "Scale", "helper", "getScaleBreakHelper", "roundNumber", "round", "IntervalScale", "_super", "_this", "apply", "arguments", "type", "_interval", "_intervalPrecision", "prototype", "parse", "val", "NaN", "Number", "contain", "_extent", "normalize", "_calculator", "scale", "getInterval", "setInterval", "interval", "_niceExtent", "slice", "getIntervalPrecision", "getTicks", "opt", "extent", "niceTickExtent", "intervalPrecision", "scaleBreakHelper", "ticks", "breakTicks", "addBreaksToTicks", "_brkCtx", "breaks", "safeLimit", "expandToNicedExtent", "push", "value", "estimateNiceMultiple", "tickVal", "targetTick", "Math", "tick", "moreMultiple", "calcNiceTickMultiple", "length", "lastNiceTick", "pruneTicksByBreak", "pruneByBreak", "item", "getMinorTicks", "splitNumber", "minorTicks", "getExtent", "i", "nextTick", "prevTick", "count", "minorTicksGroup", "minorInterval", "minorIntervalPrecision", "minor<PERSON><PERSON>", "_getNonTransBreaks", "get<PERSON><PERSON><PERSON>", "data", "precision", "getPrecision", "dataNum", "addCommas", "calcNiceTicks", "minInterval", "maxInterval", "span", "_getExtentSpanWithBreaks", "isFinite", "reverse", "_innerSetExtent", "result", "intervalScaleNiceTicks", "calcNiceExtent", "expandSize", "abs", "fixMax", "intervalPrecition", "fixMin", "floor", "ceil", "setNiceExtent", "min", "max", "registerClass"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/scale/Interval.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nimport { getScaleBreakHelper } from './break.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    // `Scale#parse` (and its overrids) are typically applied at the axis values input\n    // in echarts option. e.g., `axis.min/max`, `dataZoom.min/max`, etc.\n    // but `series.data` is not included, which uses `dataValueHelper.ts`#`parseDataValue`.\n    // `Scale#parse` originally introduced in fb8c813215098b9d2458966229bb95c510883d5e\n    // at 2016 for dataZoom start/end settings (See `parseAxisModelMinMax`).\n    //\n    // Historically `scale/Interval.ts` returns the input value directly. But numeric\n    // values (such as a number-like string '123') effectively passed through here and\n    // were involved in calculations, which was error-prone and inconsistent with the\n    // declared TS return type. Previously such issues are fixed separately in different\n    // places case by case (such as #2475).\n    //\n    // Now, we perform actual parse to ensure its `number` type here. The parsing rule\n    // follows the series data parsing rule (`dataValueHelper.ts`#`parseDataValue`)\n    // and maintains compatibility as much as possible (thus a more strict parsing\n    // `number.ts`#`numericToNumber` is not used here.)\n    //\n    // FIXME: `ScaleDataValue` also need to be modified to include numeric string type,\n    //  since it effectively does.\n    return val == null || val === '' ? NaN\n    // If string (like '-'), using '+' parse to NaN\n    // If object, also parse to NaN\n    : Number(val);\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return this._calculator.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return this._calculator.scale(val, this._extent);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\n   * @override\n   */\n  IntervalScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var scaleBreakHelper = getScaleBreakHelper();\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    if (opt.breakTicks === 'only_break' && scaleBreakHelper) {\n      scaleBreakHelper.addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (opt.expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var estimateNiceMultiple = function (tickVal, targetTick) {\n      return Math.round((targetTick - tickVal) / interval);\n    };\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (this._brkCtx) {\n        var moreMultiple = this._brkCtx.calcNiceTickMultiple(tick, estimateNiceMultiple);\n        if (moreMultiple >= 0) {\n          tick = roundNumber(tick + moreMultiple * interval, intervalPrecision);\n        }\n      }\n      if (ticks.length > 0 && tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (opt.expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    if (scaleBreakHelper) {\n      scaleBreakHelper.pruneTicksByBreak(opt.pruneByBreak, ticks, this._brkCtx.breaks, function (item) {\n        return item.value;\n      }, this._interval, this._extent);\n    }\n    if (opt.breakTicks !== 'none' && scaleBreakHelper) {\n      scaleBreakHelper.addBreaksToTicks(ticks, this._brkCtx.breaks, this._extent);\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks({\n      expandToNicedExtent: true\n    });\n    // NOTE: In log-scale, do not support minor ticks when breaks exist.\n    //  because currently log-scale minor ticks is calculated based on raw values\n    //  rather than log-transformed value, due to an odd effect when breaks exist.\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      if (prevTick[\"break\"] || nextTick[\"break\"]) {\n        // Do not build minor ticks to the adjacent ticks to breaks ticks,\n        // since the interval might be irregular.\n        continue;\n      }\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      var minorIntervalPrecision = helper.getIntervalPrecision(minorInterval);\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval, minorIntervalPrecision);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      var scaleBreakHelper = getScaleBreakHelper();\n      scaleBreakHelper && scaleBreakHelper.pruneTicksByBreak('auto', minorTicksGroup, this._getNonTransBreaks(), function (value) {\n        return value;\n      }, this._interval, extent);\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  IntervalScale.prototype._getNonTransBreaks = function () {\n    return this._brkCtx ? this._brkCtx.breaks : [];\n  };\n  /**\n   * @param opt.precision If 'auto', use nice presision.\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\n   * FIXME: refactor - disallow override, use composition instead.\n   *\n   * The override of `calcNiceTicks` should ensure these members are provided:\n   *  this._intervalPrecision\n   *  this._interval\n   *\n   * @param splitNumber By default `5`.\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent.slice();\n    var span = this._getExtentSpanWithBreaks();\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n      this._innerSetExtent(extent[0], extent[1]);\n      extent = this._extent.slice();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, span, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent.slice();\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n    extent = this._extent.slice();\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    var interval = this._interval;\n    var intervalPrecition = this._intervalPrecision;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval, intervalPrecition);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval, intervalPrecition);\n    }\n    this._innerSetExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,SAASC,mBAAmB,QAAQ,YAAY;AAChD,IAAIC,WAAW,GAAGL,UAAU,CAACM,KAAK;AAClC,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDT,SAAS,CAACQ,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,UAAU;IACvB;IACAH,KAAK,CAACI,SAAS,GAAG,CAAC;IACnBJ,KAAK,CAACK,kBAAkB,GAAG,CAAC;IAC5B,OAAOL,KAAK;EACd;EACAF,aAAa,CAACQ,SAAS,CAACC,KAAK,GAAG,UAAUC,GAAG,EAAE;IAC7C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOA,GAAG,IAAI,IAAI,IAAIA,GAAG,KAAK,EAAE,GAAGC;IACnC;IACA;IAAA,EACEC,MAAM,CAACF,GAAG,CAAC;EACf,CAAC;EACDV,aAAa,CAACQ,SAAS,CAACK,OAAO,GAAG,UAAUH,GAAG,EAAE;IAC/C,OAAOd,MAAM,CAACiB,OAAO,CAACH,GAAG,EAAE,IAAI,CAACI,OAAO,CAAC;EAC1C,CAAC;EACDd,aAAa,CAACQ,SAAS,CAACO,SAAS,GAAG,UAAUL,GAAG,EAAE;IACjD,OAAO,IAAI,CAACM,WAAW,CAACD,SAAS,CAACL,GAAG,EAAE,IAAI,CAACI,OAAO,CAAC;EACtD,CAAC;EACDd,aAAa,CAACQ,SAAS,CAACS,KAAK,GAAG,UAAUP,GAAG,EAAE;IAC7C,OAAO,IAAI,CAACM,WAAW,CAACC,KAAK,CAACP,GAAG,EAAE,IAAI,CAACI,OAAO,CAAC;EAClD,CAAC;EACDd,aAAa,CAACQ,SAAS,CAACU,WAAW,GAAG,YAAY;IAChD,OAAO,IAAI,CAACZ,SAAS;EACvB,CAAC;EACDN,aAAa,CAACQ,SAAS,CAACW,WAAW,GAAG,UAAUC,QAAQ,EAAE;IACxD,IAAI,CAACd,SAAS,GAAGc,QAAQ;IACzB;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACP,OAAO,CAACQ,KAAK,CAAC,CAAC;IACvC,IAAI,CAACf,kBAAkB,GAAGX,MAAM,CAAC2B,oBAAoB,CAACH,QAAQ,CAAC;EACjE,CAAC;EACD;AACF;AACA;EACEpB,aAAa,CAACQ,SAAS,CAACgB,QAAQ,GAAG,UAAUC,GAAG,EAAE;IAChDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIL,QAAQ,GAAG,IAAI,CAACd,SAAS;IAC7B,IAAIoB,MAAM,GAAG,IAAI,CAACZ,OAAO;IACzB,IAAIa,cAAc,GAAG,IAAI,CAACN,WAAW;IACrC,IAAIO,iBAAiB,GAAG,IAAI,CAACrB,kBAAkB;IAC/C,IAAIsB,gBAAgB,GAAGhC,mBAAmB,CAAC,CAAC;IAC5C,IAAIiC,KAAK,GAAG,EAAE;IACd;IACA,IAAI,CAACV,QAAQ,EAAE;MACb,OAAOU,KAAK;IACd;IACA,IAAIL,GAAG,CAACM,UAAU,KAAK,YAAY,IAAIF,gBAAgB,EAAE;MACvDA,gBAAgB,CAACG,gBAAgB,CAACF,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,IAAI,CAACpB,OAAO,CAAC;MAC3E,OAAOgB,KAAK;IACd;IACA;IACA,IAAIK,SAAS,GAAG,KAAK;IACrB,IAAIT,MAAM,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,EAAE;MACjC,IAAIF,GAAG,CAACW,mBAAmB,EAAE;QAC3BN,KAAK,CAACO,IAAI,CAAC;UACTC,KAAK,EAAExC,WAAW,CAAC6B,cAAc,CAAC,CAAC,CAAC,GAAGP,QAAQ,EAAEQ,iBAAiB;QACpE,CAAC,CAAC;MACJ,CAAC,MAAM;QACLE,KAAK,CAACO,IAAI,CAAC;UACTC,KAAK,EAAEZ,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;IACF;IACA,IAAIa,oBAAoB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,UAAU,EAAE;MACxD,OAAOC,IAAI,CAAC3C,KAAK,CAAC,CAAC0C,UAAU,GAAGD,OAAO,IAAIpB,QAAQ,CAAC;IACtD,CAAC;IACD,IAAIuB,IAAI,GAAGhB,cAAc,CAAC,CAAC,CAAC;IAC5B,OAAOgB,IAAI,IAAIhB,cAAc,CAAC,CAAC,CAAC,EAAE;MAChCG,KAAK,CAACO,IAAI,CAAC;QACTC,KAAK,EAAEK;MACT,CAAC,CAAC;MACF;MACAA,IAAI,GAAG7C,WAAW,CAAC6C,IAAI,GAAGvB,QAAQ,EAAEQ,iBAAiB,CAAC;MACtD,IAAI,IAAI,CAACK,OAAO,EAAE;QAChB,IAAIW,YAAY,GAAG,IAAI,CAACX,OAAO,CAACY,oBAAoB,CAACF,IAAI,EAAEJ,oBAAoB,CAAC;QAChF,IAAIK,YAAY,IAAI,CAAC,EAAE;UACrBD,IAAI,GAAG7C,WAAW,CAAC6C,IAAI,GAAGC,YAAY,GAAGxB,QAAQ,EAAEQ,iBAAiB,CAAC;QACvE;MACF;MACA,IAAIE,KAAK,CAACgB,MAAM,GAAG,CAAC,IAAIH,IAAI,KAAKb,KAAK,CAACA,KAAK,CAACgB,MAAM,GAAG,CAAC,CAAC,CAACR,KAAK,EAAE;QAC9D;QACA;QACA;MACF;MACA,IAAIR,KAAK,CAACgB,MAAM,GAAGX,SAAS,EAAE;QAC5B,OAAO,EAAE;MACX;IACF;IACA;IACA;IACA,IAAIY,YAAY,GAAGjB,KAAK,CAACgB,MAAM,GAAGhB,KAAK,CAACA,KAAK,CAACgB,MAAM,GAAG,CAAC,CAAC,CAACR,KAAK,GAAGX,cAAc,CAAC,CAAC,CAAC;IACnF,IAAID,MAAM,CAAC,CAAC,CAAC,GAAGqB,YAAY,EAAE;MAC5B,IAAItB,GAAG,CAACW,mBAAmB,EAAE;QAC3BN,KAAK,CAACO,IAAI,CAAC;UACTC,KAAK,EAAExC,WAAW,CAACiD,YAAY,GAAG3B,QAAQ,EAAEQ,iBAAiB;QAC/D,CAAC,CAAC;MACJ,CAAC,MAAM;QACLE,KAAK,CAACO,IAAI,CAAC;UACTC,KAAK,EAAEZ,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;IACF;IACA,IAAIG,gBAAgB,EAAE;MACpBA,gBAAgB,CAACmB,iBAAiB,CAACvB,GAAG,CAACwB,YAAY,EAAEnB,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,UAAUgB,IAAI,EAAE;QAC/F,OAAOA,IAAI,CAACZ,KAAK;MACnB,CAAC,EAAE,IAAI,CAAChC,SAAS,EAAE,IAAI,CAACQ,OAAO,CAAC;IAClC;IACA,IAAIW,GAAG,CAACM,UAAU,KAAK,MAAM,IAAIF,gBAAgB,EAAE;MACjDA,gBAAgB,CAACG,gBAAgB,CAACF,KAAK,EAAE,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE,IAAI,CAACpB,OAAO,CAAC;IAC7E;IACA,OAAOgB,KAAK;EACd,CAAC;EACD9B,aAAa,CAACQ,SAAS,CAAC2C,aAAa,GAAG,UAAUC,WAAW,EAAE;IAC7D,IAAItB,KAAK,GAAG,IAAI,CAACN,QAAQ,CAAC;MACxBY,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAIiB,UAAU,GAAG,EAAE;IACnB,IAAI3B,MAAM,GAAG,IAAI,CAAC4B,SAAS,CAAC,CAAC;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,KAAK,CAACgB,MAAM,EAAES,CAAC,EAAE,EAAE;MACrC,IAAIC,QAAQ,GAAG1B,KAAK,CAACyB,CAAC,CAAC;MACvB,IAAIE,QAAQ,GAAG3B,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAC;MAC3B,IAAIE,QAAQ,CAAC,OAAO,CAAC,IAAID,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC1C;QACA;QACA;MACF;MACA,IAAIE,KAAK,GAAG,CAAC;MACb,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIvC,QAAQ,GAAGoC,QAAQ,CAAClB,KAAK,GAAGmB,QAAQ,CAACnB,KAAK;MAC9C,IAAIsB,aAAa,GAAGxC,QAAQ,GAAGgC,WAAW;MAC1C,IAAIS,sBAAsB,GAAGjE,MAAM,CAAC2B,oBAAoB,CAACqC,aAAa,CAAC;MACvE,OAAOF,KAAK,GAAGN,WAAW,GAAG,CAAC,EAAE;QAC9B,IAAIU,SAAS,GAAGhE,WAAW,CAAC2D,QAAQ,CAACnB,KAAK,GAAG,CAACoB,KAAK,GAAG,CAAC,IAAIE,aAAa,EAAEC,sBAAsB,CAAC;QACjG;QACA,IAAIC,SAAS,GAAGpC,MAAM,CAAC,CAAC,CAAC,IAAIoC,SAAS,GAAGpC,MAAM,CAAC,CAAC,CAAC,EAAE;UAClDiC,eAAe,CAACtB,IAAI,CAACyB,SAAS,CAAC;QACjC;QACAJ,KAAK,EAAE;MACT;MACA,IAAI7B,gBAAgB,GAAGhC,mBAAmB,CAAC,CAAC;MAC5CgC,gBAAgB,IAAIA,gBAAgB,CAACmB,iBAAiB,CAAC,MAAM,EAAEW,eAAe,EAAE,IAAI,CAACI,kBAAkB,CAAC,CAAC,EAAE,UAAUzB,KAAK,EAAE;QAC1H,OAAOA,KAAK;MACd,CAAC,EAAE,IAAI,CAAChC,SAAS,EAAEoB,MAAM,CAAC;MAC1B2B,UAAU,CAAChB,IAAI,CAACsB,eAAe,CAAC;IAClC;IACA,OAAON,UAAU;EACnB,CAAC;EACDrD,aAAa,CAACQ,SAAS,CAACuD,kBAAkB,GAAG,YAAY;IACvD,OAAO,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,MAAM,GAAG,EAAE;EAChD,CAAC;EACD;AACF;AACA;AACA;EACElC,aAAa,CAACQ,SAAS,CAACwD,QAAQ,GAAG,UAAUC,IAAI,EAAExC,GAAG,EAAE;IACtD,IAAIwC,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,EAAE;IACX;IACA,IAAIC,SAAS,GAAGzC,GAAG,IAAIA,GAAG,CAACyC,SAAS;IACpC,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAGzE,UAAU,CAAC0E,YAAY,CAACF,IAAI,CAAC3B,KAAK,CAAC,IAAI,CAAC;IACtD,CAAC,MAAM,IAAI4B,SAAS,KAAK,MAAM,EAAE;MAC/B;MACAA,SAAS,GAAG,IAAI,CAAC3D,kBAAkB;IACrC;IACA;IACA;IACA,IAAI6D,OAAO,GAAGtE,WAAW,CAACmE,IAAI,CAAC3B,KAAK,EAAE4B,SAAS,EAAE,IAAI,CAAC;IACtD,OAAOxE,UAAU,CAAC2E,SAAS,CAACD,OAAO,CAAC;EACtC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpE,aAAa,CAACQ,SAAS,CAAC8D,aAAa,GAAG,UAAUlB,WAAW,EAAEmB,WAAW,EAAEC,WAAW,EAAE;IACvFpB,WAAW,GAAGA,WAAW,IAAI,CAAC;IAC9B,IAAI1B,MAAM,GAAG,IAAI,CAACZ,OAAO,CAACQ,KAAK,CAAC,CAAC;IACjC,IAAImD,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC1C,IAAI,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAE;MACnB;IACF;IACA;IACA;IACA,IAAIA,IAAI,GAAG,CAAC,EAAE;MACZA,IAAI,GAAG,CAACA,IAAI;MACZ/C,MAAM,CAACkD,OAAO,CAAC,CAAC;MAChB,IAAI,CAACC,eAAe,CAACnD,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;MAC1CA,MAAM,GAAG,IAAI,CAACZ,OAAO,CAACQ,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIwD,MAAM,GAAGlF,MAAM,CAACmF,sBAAsB,CAACrD,MAAM,EAAE+C,IAAI,EAAErB,WAAW,EAAEmB,WAAW,EAAEC,WAAW,CAAC;IAC/F,IAAI,CAACjE,kBAAkB,GAAGuE,MAAM,CAAClD,iBAAiB;IAClD,IAAI,CAACtB,SAAS,GAAGwE,MAAM,CAAC1D,QAAQ;IAChC,IAAI,CAACC,WAAW,GAAGyD,MAAM,CAACnD,cAAc;EAC1C,CAAC;EACD3B,aAAa,CAACQ,SAAS,CAACwE,cAAc,GAAG,UAAUvD,GAAG,EAAE;IACtD,IAAIC,MAAM,GAAG,IAAI,CAACZ,OAAO,CAACQ,KAAK,CAAC,CAAC;IACjC;IACA,IAAII,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;MAC3B,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACnB;QACA;QACA,IAAIuD,UAAU,GAAGvC,IAAI,CAACwC,GAAG,CAACxD,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC;QACA;QACA;QACA;QACA;QACA,IAAI,CAACD,GAAG,CAAC0D,MAAM,EAAE;UACfzD,MAAM,CAAC,CAAC,CAAC,IAAIuD,UAAU,GAAG,CAAC;UAC3BvD,MAAM,CAAC,CAAC,CAAC,IAAIuD,UAAU,GAAG,CAAC;QAC7B,CAAC,MAAM;UACLvD,MAAM,CAAC,CAAC,CAAC,IAAIuD,UAAU,GAAG,CAAC;QAC7B;MACF,CAAC,MAAM;QACLvD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;MACf;IACF;IACA,IAAI+C,IAAI,GAAG/C,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAChC;IACA,IAAI,CAACiD,QAAQ,CAACF,IAAI,CAAC,EAAE;MACnB/C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;MACbA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACf;IACA,IAAI,CAACmD,eAAe,CAACnD,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1CA,MAAM,GAAG,IAAI,CAACZ,OAAO,CAACQ,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACgD,aAAa,CAAC7C,GAAG,CAAC2B,WAAW,EAAE3B,GAAG,CAAC8C,WAAW,EAAE9C,GAAG,CAAC+C,WAAW,CAAC;IACrE,IAAIpD,QAAQ,GAAG,IAAI,CAACd,SAAS;IAC7B,IAAI8E,iBAAiB,GAAG,IAAI,CAAC7E,kBAAkB;IAC/C,IAAI,CAACkB,GAAG,CAAC4D,MAAM,EAAE;MACf3D,MAAM,CAAC,CAAC,CAAC,GAAG5B,WAAW,CAAC4C,IAAI,CAAC4C,KAAK,CAAC5D,MAAM,CAAC,CAAC,CAAC,GAAGN,QAAQ,CAAC,GAAGA,QAAQ,EAAEgE,iBAAiB,CAAC;IACzF;IACA,IAAI,CAAC3D,GAAG,CAAC0D,MAAM,EAAE;MACfzD,MAAM,CAAC,CAAC,CAAC,GAAG5B,WAAW,CAAC4C,IAAI,CAAC6C,IAAI,CAAC7D,MAAM,CAAC,CAAC,CAAC,GAAGN,QAAQ,CAAC,GAAGA,QAAQ,EAAEgE,iBAAiB,CAAC;IACxF;IACA,IAAI,CAACP,eAAe,CAACnD,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC;EACD1B,aAAa,CAACQ,SAAS,CAACgF,aAAa,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAC1D,IAAI,CAACrE,WAAW,GAAG,CAACoE,GAAG,EAAEC,GAAG,CAAC;EAC/B,CAAC;EACD1F,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/B,OAAOL,aAAa;AACtB,CAAC,CAACL,KAAK,CAAC;AACRA,KAAK,CAACgG,aAAa,CAAC3F,aAAa,CAAC;AAClC,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}