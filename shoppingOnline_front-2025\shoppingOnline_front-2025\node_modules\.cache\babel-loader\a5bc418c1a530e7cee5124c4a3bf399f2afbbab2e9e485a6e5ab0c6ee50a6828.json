{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { DataFormatMixin } from '../../model/mixin/dataFormat.js';\nimport ComponentModel from '../../model/Component.js';\nimport { makeInner, defaultEmphasis } from '../../util/model.js';\nimport { createTooltipMarkup } from '../tooltip/tooltipMarkup.js';\nfunction fillLabel(opt) {\n  defaultEmphasis(opt, 'label', ['show']);\n}\n// { [componentType]: MarkerModel }\nvar inner = makeInner();\nvar MarkerModel = /** @class */function (_super) {\n  __extends(MarkerModel, _super);\n  function MarkerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkerModel.type;\n    /**\n     * If marker model is created by self from series\n     */\n    _this.createdBySelf = false;\n    _this.preventAutoZ = true;\n    return _this;\n  }\n  /**\n   * @overrite\n   */\n  MarkerModel.prototype.init = function (option, parentModel, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.type === 'marker') {\n        throw new Error('Marker component is abstract component. Use markLine, markPoint, markArea instead.');\n      }\n    }\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._mergeOption(option, ecModel, false, true);\n  };\n  MarkerModel.prototype.isAnimationEnabled = function () {\n    if (env.node) {\n      return false;\n    }\n    var hostSeries = this.__hostSeries;\n    return this.getShallow('animation') && hostSeries && hostSeries.isAnimationEnabled();\n  };\n  /**\n   * @overrite\n   */\n  MarkerModel.prototype.mergeOption = function (newOpt, ecModel) {\n    this._mergeOption(newOpt, ecModel, false, false);\n  };\n  MarkerModel.prototype._mergeOption = function (newOpt, ecModel, createdBySelf, isInit) {\n    var componentType = this.mainType;\n    if (!createdBySelf) {\n      ecModel.eachSeries(function (seriesModel) {\n        // mainType can be markPoint, markLine, markArea\n        var markerOpt = seriesModel.get(this.mainType, true);\n        var markerModel = inner(seriesModel)[componentType];\n        if (!markerOpt || !markerOpt.data) {\n          inner(seriesModel)[componentType] = null;\n          return;\n        }\n        if (!markerModel) {\n          if (isInit) {\n            // Default label emphasis `position` and `show`\n            fillLabel(markerOpt);\n          }\n          zrUtil.each(markerOpt.data, function (item) {\n            // FIXME Overwrite fillLabel method ?\n            if (item instanceof Array) {\n              fillLabel(item[0]);\n              fillLabel(item[1]);\n            } else {\n              fillLabel(item);\n            }\n          });\n          markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);\n          // markerModel = new ImplementedMarkerModel(\n          //     markerOpt, this, ecModel\n          // );\n          zrUtil.extend(markerModel, {\n            mainType: this.mainType,\n            // Use the same series index and name\n            seriesIndex: seriesModel.seriesIndex,\n            name: seriesModel.name,\n            createdBySelf: true\n          });\n          markerModel.__hostSeries = seriesModel;\n        } else {\n          markerModel._mergeOption(markerOpt, ecModel, true);\n        }\n        inner(seriesModel)[componentType] = markerModel;\n      }, this);\n    }\n  };\n  MarkerModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var itemName = data.getName(dataIndex);\n    return createTooltipMarkup('section', {\n      header: this.name,\n      blocks: [createTooltipMarkup('nameValue', {\n        name: itemName,\n        value: value,\n        noName: !itemName,\n        noValue: value == null\n      })]\n    });\n  };\n  MarkerModel.prototype.getData = function () {\n    return this._data;\n  };\n  MarkerModel.prototype.setData = function (data) {\n    this._data = data;\n  };\n  MarkerModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);\n    var hostSeries = this.__hostSeries;\n    if (hostSeries) {\n      params.seriesId = hostSeries.id;\n      params.seriesName = hostSeries.name;\n      params.seriesType = hostSeries.subType;\n    }\n    return params;\n  };\n  MarkerModel.getMarkerModelFromSeries = function (seriesModel,\n  // Support three types of markers. Strict check.\n  componentType) {\n    return inner(seriesModel)[componentType];\n  };\n  MarkerModel.type = 'marker';\n  MarkerModel.dependencies = ['series', 'grid', 'polar', 'geo'];\n  return MarkerModel;\n}(ComponentModel);\nzrUtil.mixin(MarkerModel, DataFormatMixin.prototype);\nexport default MarkerModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "env", "DataFormatMixin", "ComponentModel", "makeInner", "defaultEmphasis", "createTooltipMarkup", "<PERSON><PERSON><PERSON><PERSON>", "opt", "inner", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "createdBySelf", "preventAutoZ", "prototype", "init", "option", "parentModel", "ecModel", "process", "NODE_ENV", "Error", "mergeDefaultAndTheme", "_mergeOption", "isAnimationEnabled", "node", "hostSeries", "__hostSeries", "getShallow", "mergeOption", "newOpt", "isInit", "componentType", "mainType", "eachSeries", "seriesModel", "markerOpt", "get", "markerModel", "data", "each", "item", "Array", "createMarkerModelFromSeries", "extend", "seriesIndex", "name", "formatTooltip", "dataIndex", "multipleSeries", "dataType", "getData", "value", "getRawValue", "itemName", "getName", "header", "blocks", "noName", "noValue", "_data", "setData", "getDataParams", "params", "call", "seriesId", "id", "seriesName", "seriesType", "subType", "getMarkerModelFromSeries", "dependencies", "mixin"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/marker/MarkerModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { DataFormatMixin } from '../../model/mixin/dataFormat.js';\nimport ComponentModel from '../../model/Component.js';\nimport { makeInner, defaultEmphasis } from '../../util/model.js';\nimport { createTooltipMarkup } from '../tooltip/tooltipMarkup.js';\nfunction fillLabel(opt) {\n  defaultEmphasis(opt, 'label', ['show']);\n}\n// { [componentType]: MarkerModel }\nvar inner = makeInner();\nvar MarkerModel = /** @class */function (_super) {\n  __extends(MarkerModel, _super);\n  function MarkerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkerModel.type;\n    /**\n     * If marker model is created by self from series\n     */\n    _this.createdBySelf = false;\n    _this.preventAutoZ = true;\n    return _this;\n  }\n  /**\n   * @overrite\n   */\n  MarkerModel.prototype.init = function (option, parentModel, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.type === 'marker') {\n        throw new Error('Marker component is abstract component. Use markLine, markPoint, markArea instead.');\n      }\n    }\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._mergeOption(option, ecModel, false, true);\n  };\n  MarkerModel.prototype.isAnimationEnabled = function () {\n    if (env.node) {\n      return false;\n    }\n    var hostSeries = this.__hostSeries;\n    return this.getShallow('animation') && hostSeries && hostSeries.isAnimationEnabled();\n  };\n  /**\n   * @overrite\n   */\n  MarkerModel.prototype.mergeOption = function (newOpt, ecModel) {\n    this._mergeOption(newOpt, ecModel, false, false);\n  };\n  MarkerModel.prototype._mergeOption = function (newOpt, ecModel, createdBySelf, isInit) {\n    var componentType = this.mainType;\n    if (!createdBySelf) {\n      ecModel.eachSeries(function (seriesModel) {\n        // mainType can be markPoint, markLine, markArea\n        var markerOpt = seriesModel.get(this.mainType, true);\n        var markerModel = inner(seriesModel)[componentType];\n        if (!markerOpt || !markerOpt.data) {\n          inner(seriesModel)[componentType] = null;\n          return;\n        }\n        if (!markerModel) {\n          if (isInit) {\n            // Default label emphasis `position` and `show`\n            fillLabel(markerOpt);\n          }\n          zrUtil.each(markerOpt.data, function (item) {\n            // FIXME Overwrite fillLabel method ?\n            if (item instanceof Array) {\n              fillLabel(item[0]);\n              fillLabel(item[1]);\n            } else {\n              fillLabel(item);\n            }\n          });\n          markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);\n          // markerModel = new ImplementedMarkerModel(\n          //     markerOpt, this, ecModel\n          // );\n          zrUtil.extend(markerModel, {\n            mainType: this.mainType,\n            // Use the same series index and name\n            seriesIndex: seriesModel.seriesIndex,\n            name: seriesModel.name,\n            createdBySelf: true\n          });\n          markerModel.__hostSeries = seriesModel;\n        } else {\n          markerModel._mergeOption(markerOpt, ecModel, true);\n        }\n        inner(seriesModel)[componentType] = markerModel;\n      }, this);\n    }\n  };\n  MarkerModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var itemName = data.getName(dataIndex);\n    return createTooltipMarkup('section', {\n      header: this.name,\n      blocks: [createTooltipMarkup('nameValue', {\n        name: itemName,\n        value: value,\n        noName: !itemName,\n        noValue: value == null\n      })]\n    });\n  };\n  MarkerModel.prototype.getData = function () {\n    return this._data;\n  };\n  MarkerModel.prototype.setData = function (data) {\n    this._data = data;\n  };\n  MarkerModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);\n    var hostSeries = this.__hostSeries;\n    if (hostSeries) {\n      params.seriesId = hostSeries.id;\n      params.seriesName = hostSeries.name;\n      params.seriesType = hostSeries.subType;\n    }\n    return params;\n  };\n  MarkerModel.getMarkerModelFromSeries = function (seriesModel,\n  // Support three types of markers. Strict check.\n  componentType) {\n    return inner(seriesModel)[componentType];\n  };\n  MarkerModel.type = 'marker';\n  MarkerModel.dependencies = ['series', 'grid', 'polar', 'geo'];\n  return MarkerModel;\n}(ComponentModel);\nzrUtil.mixin(MarkerModel, DataFormatMixin.prototype);\nexport default MarkerModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,eAAe,QAAQ,iCAAiC;AACjE,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,SAAS,EAAEC,eAAe,QAAQ,qBAAqB;AAChE,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,SAASA,CAACC,GAAG,EAAE;EACtBH,eAAe,CAACG,GAAG,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC;AACzC;AACA;AACA,IAAIC,KAAK,GAAGL,SAAS,CAAC,CAAC;AACvB,IAAIM,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CZ,SAAS,CAACW,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7B;AACJ;AACA;IACIH,KAAK,CAACI,aAAa,GAAG,KAAK;IAC3BJ,KAAK,CAACK,YAAY,GAAG,IAAI;IACzB,OAAOL,KAAK;EACd;EACA;AACF;AACA;EACEF,WAAW,CAACQ,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACnE,IAAIC,OAAO,CAACtB,GAAG,CAACuB,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,IAAI,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC1B,MAAM,IAAIU,KAAK,CAAC,oFAAoF,CAAC;MACvG;IACF;IACA,IAAI,CAACC,oBAAoB,CAACN,MAAM,EAAEE,OAAO,CAAC;IAC1C,IAAI,CAACK,YAAY,CAACP,MAAM,EAAEE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;EACjD,CAAC;EACDZ,WAAW,CAACQ,SAAS,CAACU,kBAAkB,GAAG,YAAY;IACrD,IAAI3B,GAAG,CAAC4B,IAAI,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIC,UAAU,GAAG,IAAI,CAACC,YAAY;IAClC,OAAO,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,IAAIF,UAAU,IAAIA,UAAU,CAACF,kBAAkB,CAAC,CAAC;EACtF,CAAC;EACD;AACF;AACA;EACElB,WAAW,CAACQ,SAAS,CAACe,WAAW,GAAG,UAAUC,MAAM,EAAEZ,OAAO,EAAE;IAC7D,IAAI,CAACK,YAAY,CAACO,MAAM,EAAEZ,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;EAClD,CAAC;EACDZ,WAAW,CAACQ,SAAS,CAACS,YAAY,GAAG,UAAUO,MAAM,EAAEZ,OAAO,EAAEN,aAAa,EAAEmB,MAAM,EAAE;IACrF,IAAIC,aAAa,GAAG,IAAI,CAACC,QAAQ;IACjC,IAAI,CAACrB,aAAa,EAAE;MAClBM,OAAO,CAACgB,UAAU,CAAC,UAAUC,WAAW,EAAE;QACxC;QACA,IAAIC,SAAS,GAAGD,WAAW,CAACE,GAAG,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAAC;QACpD,IAAIK,WAAW,GAAGjC,KAAK,CAAC8B,WAAW,CAAC,CAACH,aAAa,CAAC;QACnD,IAAI,CAACI,SAAS,IAAI,CAACA,SAAS,CAACG,IAAI,EAAE;UACjClC,KAAK,CAAC8B,WAAW,CAAC,CAACH,aAAa,CAAC,GAAG,IAAI;UACxC;QACF;QACA,IAAI,CAACM,WAAW,EAAE;UAChB,IAAIP,MAAM,EAAE;YACV;YACA5B,SAAS,CAACiC,SAAS,CAAC;UACtB;UACAxC,MAAM,CAAC4C,IAAI,CAACJ,SAAS,CAACG,IAAI,EAAE,UAAUE,IAAI,EAAE;YAC1C;YACA,IAAIA,IAAI,YAAYC,KAAK,EAAE;cACzBvC,SAAS,CAACsC,IAAI,CAAC,CAAC,CAAC,CAAC;cAClBtC,SAAS,CAACsC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,MAAM;cACLtC,SAAS,CAACsC,IAAI,CAAC;YACjB;UACF,CAAC,CAAC;UACFH,WAAW,GAAG,IAAI,CAACK,2BAA2B,CAACP,SAAS,EAAE,IAAI,EAAElB,OAAO,CAAC;UACxE;UACA;UACA;UACAtB,MAAM,CAACgD,MAAM,CAACN,WAAW,EAAE;YACzBL,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvB;YACAY,WAAW,EAAEV,WAAW,CAACU,WAAW;YACpCC,IAAI,EAAEX,WAAW,CAACW,IAAI;YACtBlC,aAAa,EAAE;UACjB,CAAC,CAAC;UACF0B,WAAW,CAACX,YAAY,GAAGQ,WAAW;QACxC,CAAC,MAAM;UACLG,WAAW,CAACf,YAAY,CAACa,SAAS,EAAElB,OAAO,EAAE,IAAI,CAAC;QACpD;QACAb,KAAK,CAAC8B,WAAW,CAAC,CAACH,aAAa,CAAC,GAAGM,WAAW;MACjD,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EACDhC,WAAW,CAACQ,SAAS,CAACiC,aAAa,GAAG,UAAUC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IACnF,IAAIX,IAAI,GAAG,IAAI,CAACY,OAAO,CAAC,CAAC;IACzB,IAAIC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACL,SAAS,CAAC;IACvC,IAAIM,QAAQ,GAAGf,IAAI,CAACgB,OAAO,CAACP,SAAS,CAAC;IACtC,OAAO9C,mBAAmB,CAAC,SAAS,EAAE;MACpCsD,MAAM,EAAE,IAAI,CAACV,IAAI;MACjBW,MAAM,EAAE,CAACvD,mBAAmB,CAAC,WAAW,EAAE;QACxC4C,IAAI,EAAEQ,QAAQ;QACdF,KAAK,EAAEA,KAAK;QACZM,MAAM,EAAE,CAACJ,QAAQ;QACjBK,OAAO,EAAEP,KAAK,IAAI;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD9C,WAAW,CAACQ,SAAS,CAACqC,OAAO,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACS,KAAK;EACnB,CAAC;EACDtD,WAAW,CAACQ,SAAS,CAAC+C,OAAO,GAAG,UAAUtB,IAAI,EAAE;IAC9C,IAAI,CAACqB,KAAK,GAAGrB,IAAI;EACnB,CAAC;EACDjC,WAAW,CAACQ,SAAS,CAACgD,aAAa,GAAG,UAAUd,SAAS,EAAEE,QAAQ,EAAE;IACnE,IAAIa,MAAM,GAAGjE,eAAe,CAACgB,SAAS,CAACgD,aAAa,CAACE,IAAI,CAAC,IAAI,EAAEhB,SAAS,EAAEE,QAAQ,CAAC;IACpF,IAAIxB,UAAU,GAAG,IAAI,CAACC,YAAY;IAClC,IAAID,UAAU,EAAE;MACdqC,MAAM,CAACE,QAAQ,GAAGvC,UAAU,CAACwC,EAAE;MAC/BH,MAAM,CAACI,UAAU,GAAGzC,UAAU,CAACoB,IAAI;MACnCiB,MAAM,CAACK,UAAU,GAAG1C,UAAU,CAAC2C,OAAO;IACxC;IACA,OAAON,MAAM;EACf,CAAC;EACDzD,WAAW,CAACgE,wBAAwB,GAAG,UAAUnC,WAAW;EAC5D;EACAH,aAAa,EAAE;IACb,OAAO3B,KAAK,CAAC8B,WAAW,CAAC,CAACH,aAAa,CAAC;EAC1C,CAAC;EACD1B,WAAW,CAACK,IAAI,GAAG,QAAQ;EAC3BL,WAAW,CAACiE,YAAY,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAC7D,OAAOjE,WAAW;AACpB,CAAC,CAACP,cAAc,CAAC;AACjBH,MAAM,CAAC4E,KAAK,CAAClE,WAAW,EAAER,eAAe,CAACgB,SAAS,CAAC;AACpD,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}