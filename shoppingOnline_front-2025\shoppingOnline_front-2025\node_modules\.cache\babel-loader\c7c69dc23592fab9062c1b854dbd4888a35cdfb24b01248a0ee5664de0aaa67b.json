{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, axisDim, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var seriesModel = data.hostModel;\n  var dataIndex = seriesModel.indicesOfNearest(axisDim, calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\n * Transform markPoint data item to format used in List by do the following\n * 1. Calculate statistic like `max`, `min`, `average`\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.valueAxis.dim, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n    var baseAxis = seriesModel.getBaseAxis();\n    if (baseAxis && item.type && markerTypeCalculator[item.type]) {\n      var otherAxis = coordSys.getOtherAxis(baseAxis);\n      if (otherAxis) {\n        item.value = numCalculate(data, data.mapDimension(otherAxis.dim), item.type);\n      }\n    }\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\n * Filter data which is out of coordinateSystem range\n * [dataFilter description]\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}", "map": {"version": 3, "names": ["numberUtil", "isDimensionStacked", "indexOf", "curry", "clone", "isArray", "parseDataValue", "hasXOrY", "item", "isNaN", "parseFloat", "x", "y", "hasXAndY", "markerTypeCalculatorWithExtent", "markerType", "data", "axisDim", "otherDataDim", "targetDataDim", "otherCoordIndex", "targetCoordIndex", "coordArr", "stacked", "calcDataDim", "getCalculationInfo", "value", "numCalculate", "seriesModel", "hostModel", "dataIndex", "indicesOfNearest", "get", "coordArrValue", "precision", "getPrecision", "Math", "min", "toFixed", "markerTypeCalculator", "max", "average", "median", "dataTransform", "getData", "coordSys", "coordinateSystem", "dims", "dimensions", "coord", "axisInfo", "getAxisInfo", "type", "baseAxis", "valueAxis", "dim", "coordInfo", "baseDataDim", "valueDataDim", "xAxis", "radiusAxis", "yAxis", "angleAxis", "getBaseAxis", "otherAxis", "getOtherAxis", "mapDimension", "i", "ret", "valueIndex", "valueDim", "getDimension", "getAxis", "dataDimToCoordDim", "dataDim", "dimItem", "getDimensionInfo", "coordDim", "dataFilter", "containData", "zoneFilter", "item1", "item2", "containZone", "createMarkerDimValueGetter", "inCoordSys", "dimName", "dimIndex", "rawVal", "sum_1", "count_1", "each", "val", "idx", "getMedian", "getDataExtent"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/marker/markerHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, axisDim, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var seriesModel = data.hostModel;\n  var dataIndex = seriesModel.indicesOfNearest(axisDim, calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\n * Transform markPoint data item to format used in List by do the following\n * 1. Calculate statistic like `max`, `min`, `average`\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.valueAxis.dim, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n    var baseAxis = seriesModel.getBaseAxis();\n    if (baseAxis && item.type && markerTypeCalculator[item.type]) {\n      var otherAxis = coordSys.getOtherAxis(baseAxis);\n      if (otherAxis) {\n        item.value = numCalculate(data, data.mapDimension(otherAxis.dim), item.type);\n      }\n    }\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\n * Filter data which is out of coordinateSystem range\n * [dataFilter description]\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,UAAU,MAAM,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,0BAA0B;AACzE,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,OAAOA,CAACC,IAAI,EAAE;EACrB,OAAO,EAAEC,KAAK,CAACC,UAAU,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACC,UAAU,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC;AAClE;AACA,SAASC,QAAQA,CAACL,IAAI,EAAE;EACtB,OAAO,CAACC,KAAK,CAACC,UAAU,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,IAAI,CAACF,KAAK,CAACC,UAAU,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC;AACjE;AACA,SAASE,8BAA8BA,CAACC,UAAU,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAE;EACjI,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAGtB,kBAAkB,CAACe,IAAI,EAAEG,aAAa,CAAC,oBAAoB,CAAC;EAC1E,IAAIK,WAAW,GAAGD,OAAO,GAAGP,IAAI,CAACS,kBAAkB,CAAC,sBAAsB,CAAC,GAAGN,aAAa;EAC3F,IAAIO,KAAK,GAAGC,YAAY,CAACX,IAAI,EAAEQ,WAAW,EAAET,UAAU,CAAC;EACvD,IAAIa,WAAW,GAAGZ,IAAI,CAACa,SAAS;EAChC,IAAIC,SAAS,GAAGF,WAAW,CAACG,gBAAgB,CAACd,OAAO,EAAEO,WAAW,EAAEE,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5EJ,QAAQ,CAACF,eAAe,CAAC,GAAGJ,IAAI,CAACgB,GAAG,CAACd,YAAY,EAAEY,SAAS,CAAC;EAC7DR,QAAQ,CAACD,gBAAgB,CAAC,GAAGL,IAAI,CAACgB,GAAG,CAACR,WAAW,EAAEM,SAAS,CAAC;EAC7D,IAAIG,aAAa,GAAGjB,IAAI,CAACgB,GAAG,CAACb,aAAa,EAAEW,SAAS,CAAC;EACtD;EACA,IAAII,SAAS,GAAGlC,UAAU,CAACmC,YAAY,CAACnB,IAAI,CAACgB,GAAG,CAACb,aAAa,EAAEW,SAAS,CAAC,CAAC;EAC3EI,SAAS,GAAGE,IAAI,CAACC,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;EACnC,IAAIA,SAAS,IAAI,CAAC,EAAE;IAClBZ,QAAQ,CAACD,gBAAgB,CAAC,GAAG,CAACC,QAAQ,CAACD,gBAAgB,CAAC,CAACiB,OAAO,CAACJ,SAAS,CAAC;EAC7E;EACA,OAAO,CAACZ,QAAQ,EAAEW,aAAa,CAAC;AAClC;AACA;AACA,IAAIM,oBAAoB,GAAG;EACzBF,GAAG,EAAElC,KAAK,CAACW,8BAA8B,EAAE,KAAK,CAAC;EACjD0B,GAAG,EAAErC,KAAK,CAACW,8BAA8B,EAAE,KAAK,CAAC;EACjD2B,OAAO,EAAEtC,KAAK,CAACW,8BAA8B,EAAE,SAAS,CAAC;EACzD4B,MAAM,EAAEvC,KAAK,CAACW,8BAA8B,EAAE,QAAQ;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS6B,aAAaA,CAACf,WAAW,EAAEpB,IAAI,EAAE;EAC/C,IAAI,CAACA,IAAI,EAAE;IACT;EACF;EACA,IAAIQ,IAAI,GAAGY,WAAW,CAACgB,OAAO,CAAC,CAAC;EAChC,IAAIC,QAAQ,GAAGjB,WAAW,CAACkB,gBAAgB;EAC3C,IAAIC,IAAI,GAAGF,QAAQ,IAAIA,QAAQ,CAACG,UAAU;EAC1C;EACA;EACA;EACA;EACA,IAAI,CAACnC,QAAQ,CAACL,IAAI,CAAC,IAAI,CAACH,OAAO,CAACG,IAAI,CAACyC,KAAK,CAAC,IAAI5C,OAAO,CAAC0C,IAAI,CAAC,EAAE;IAC5D,IAAIG,QAAQ,GAAGC,WAAW,CAAC3C,IAAI,EAAEQ,IAAI,EAAE6B,QAAQ,EAAEjB,WAAW,CAAC;IAC7D;IACA;IACApB,IAAI,GAAGJ,KAAK,CAACI,IAAI,CAAC;IAClB,IAAIA,IAAI,CAAC4C,IAAI,IAAIb,oBAAoB,CAAC/B,IAAI,CAAC4C,IAAI,CAAC,IAAIF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACI,SAAS,EAAE;MAC3F,IAAIlC,eAAe,GAAGlB,OAAO,CAAC6C,IAAI,EAAEG,QAAQ,CAACG,QAAQ,CAACE,GAAG,CAAC;MAC1D,IAAIlC,gBAAgB,GAAGnB,OAAO,CAAC6C,IAAI,EAAEG,QAAQ,CAACI,SAAS,CAACC,GAAG,CAAC;MAC5D,IAAIC,SAAS,GAAGjB,oBAAoB,CAAC/B,IAAI,CAAC4C,IAAI,CAAC,CAACpC,IAAI,EAAEkC,QAAQ,CAACI,SAAS,CAACC,GAAG,EAAEL,QAAQ,CAACO,WAAW,EAAEP,QAAQ,CAACQ,YAAY,EAAEtC,eAAe,EAAEC,gBAAgB,CAAC;MAC7Jb,IAAI,CAACyC,KAAK,GAAGO,SAAS,CAAC,CAAC,CAAC;MACzB;MACA;MACAhD,IAAI,CAACkB,KAAK,GAAG8B,SAAS,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL;MACAhD,IAAI,CAACyC,KAAK,GAAG,CAACzC,IAAI,CAACmD,KAAK,IAAI,IAAI,GAAGnD,IAAI,CAACmD,KAAK,GAAGnD,IAAI,CAACoD,UAAU,EAAEpD,IAAI,CAACqD,KAAK,IAAI,IAAI,GAAGrD,IAAI,CAACqD,KAAK,GAAGrD,IAAI,CAACsD,SAAS,CAAC;IACpH;EACF;EACA;EACA,IAAItD,IAAI,CAACyC,KAAK,IAAI,IAAI,IAAI,CAAC5C,OAAO,CAAC0C,IAAI,CAAC,EAAE;IACxCvC,IAAI,CAACyC,KAAK,GAAG,EAAE;IACf,IAAII,QAAQ,GAAGzB,WAAW,CAACmC,WAAW,CAAC,CAAC;IACxC,IAAIV,QAAQ,IAAI7C,IAAI,CAAC4C,IAAI,IAAIb,oBAAoB,CAAC/B,IAAI,CAAC4C,IAAI,CAAC,EAAE;MAC5D,IAAIY,SAAS,GAAGnB,QAAQ,CAACoB,YAAY,CAACZ,QAAQ,CAAC;MAC/C,IAAIW,SAAS,EAAE;QACbxD,IAAI,CAACkB,KAAK,GAAGC,YAAY,CAACX,IAAI,EAAEA,IAAI,CAACkD,YAAY,CAACF,SAAS,CAACT,GAAG,CAAC,EAAE/C,IAAI,CAAC4C,IAAI,CAAC;MAC9E;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIH,KAAK,GAAGzC,IAAI,CAACyC,KAAK;IACtB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAI5B,oBAAoB,CAACU,KAAK,CAACkB,CAAC,CAAC,CAAC,EAAE;QAClClB,KAAK,CAACkB,CAAC,CAAC,GAAGxC,YAAY,CAACX,IAAI,EAAEA,IAAI,CAACkD,YAAY,CAACnB,IAAI,CAACoB,CAAC,CAAC,CAAC,EAAElB,KAAK,CAACkB,CAAC,CAAC,CAAC;MACrE;IACF;EACF;EACA,OAAO3D,IAAI;AACb;AACA,OAAO,SAAS2C,WAAWA,CAAC3C,IAAI,EAAEQ,IAAI,EAAE6B,QAAQ,EAAEjB,WAAW,EAAE;EAC7D,IAAIwC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAI5D,IAAI,CAAC6D,UAAU,IAAI,IAAI,IAAI7D,IAAI,CAAC8D,QAAQ,IAAI,IAAI,EAAE;IACpDF,GAAG,CAACV,YAAY,GAAGlD,IAAI,CAAC6D,UAAU,IAAI,IAAI,GAAGrD,IAAI,CAACuD,YAAY,CAAC/D,IAAI,CAAC6D,UAAU,CAAC,GAAG7D,IAAI,CAAC8D,QAAQ;IAC/FF,GAAG,CAACd,SAAS,GAAGT,QAAQ,CAAC2B,OAAO,CAACC,iBAAiB,CAAC7C,WAAW,EAAEwC,GAAG,CAACV,YAAY,CAAC,CAAC;IAClFU,GAAG,CAACf,QAAQ,GAAGR,QAAQ,CAACoB,YAAY,CAACG,GAAG,CAACd,SAAS,CAAC;IACnDc,GAAG,CAACX,WAAW,GAAGzC,IAAI,CAACkD,YAAY,CAACE,GAAG,CAACf,QAAQ,CAACE,GAAG,CAAC;EACvD,CAAC,MAAM;IACLa,GAAG,CAACf,QAAQ,GAAGzB,WAAW,CAACmC,WAAW,CAAC,CAAC;IACxCK,GAAG,CAACd,SAAS,GAAGT,QAAQ,CAACoB,YAAY,CAACG,GAAG,CAACf,QAAQ,CAAC;IACnDe,GAAG,CAACX,WAAW,GAAGzC,IAAI,CAACkD,YAAY,CAACE,GAAG,CAACf,QAAQ,CAACE,GAAG,CAAC;IACrDa,GAAG,CAACV,YAAY,GAAG1C,IAAI,CAACkD,YAAY,CAACE,GAAG,CAACd,SAAS,CAACC,GAAG,CAAC;EACzD;EACA,OAAOa,GAAG;AACZ;AACA,SAASK,iBAAiBA,CAAC7C,WAAW,EAAE8C,OAAO,EAAE;EAC/C,IAAIC,OAAO,GAAG/C,WAAW,CAACgB,OAAO,CAAC,CAAC,CAACgC,gBAAgB,CAACF,OAAO,CAAC;EAC7D,OAAOC,OAAO,IAAIA,OAAO,CAACE,QAAQ;AACpC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA;AAC1B;AACAjC,QAAQ,EAAErC,IAAI,EAAE;EACd;EACA,OAAOqC,QAAQ,IAAIA,QAAQ,CAACkC,WAAW,IAAIvE,IAAI,CAACyC,KAAK,IAAI,CAAC1C,OAAO,CAACC,IAAI,CAAC,GAAGqC,QAAQ,CAACkC,WAAW,CAACvE,IAAI,CAACyC,KAAK,CAAC,GAAG,IAAI;AACnH;AACA,OAAO,SAAS+B,UAAUA;AAC1B;AACAnC,QAAQ,EAAEoC,KAAK,EAAEC,KAAK,EAAE;EACtB;EACA,OAAOrC,QAAQ,IAAIA,QAAQ,CAACsC,WAAW,IAAIF,KAAK,CAAChC,KAAK,IAAIiC,KAAK,CAACjC,KAAK,IAAI,CAAC1C,OAAO,CAAC0E,KAAK,CAAC,IAAI,CAAC1E,OAAO,CAAC2E,KAAK,CAAC,GAAGrC,QAAQ,CAACsC,WAAW,CAACF,KAAK,CAAChC,KAAK,EAAEiC,KAAK,CAACjC,KAAK,CAAC,GAAG,IAAI;AACrK;AACA,OAAO,SAASmC,0BAA0BA,CAACC,UAAU,EAAEtC,IAAI,EAAE;EAC3D,OAAOsC,UAAU,GAAG,UAAU7E,IAAI,EAAE8E,OAAO,EAAExD,SAAS,EAAEyD,QAAQ,EAAE;IAChE,IAAIC,MAAM,GAAGD,QAAQ,GAAG;IACxB;IAAA,EACE/E,IAAI,CAACyC,KAAK,IAAIzC,IAAI,CAACyC,KAAK,CAACsC,QAAQ,CAAC,GAAG/E,IAAI,CAACkB,KAAK;IACjD,OAAOpB,cAAc,CAACkF,MAAM,EAAEzC,IAAI,CAACwC,QAAQ,CAAC,CAAC;EAC/C,CAAC,GAAG,UAAU/E,IAAI,EAAE8E,OAAO,EAAExD,SAAS,EAAEyD,QAAQ,EAAE;IAChD,OAAOjF,cAAc,CAACE,IAAI,CAACkB,KAAK,EAAEqB,IAAI,CAACwC,QAAQ,CAAC,CAAC;EACnD,CAAC;AACH;AACA,OAAO,SAAS5D,YAAYA,CAACX,IAAI,EAAE0C,YAAY,EAAEN,IAAI,EAAE;EACrD,IAAIA,IAAI,KAAK,SAAS,EAAE;IACtB,IAAIqC,KAAK,GAAG,CAAC;IACb,IAAIC,OAAO,GAAG,CAAC;IACf1E,IAAI,CAAC2E,IAAI,CAACjC,YAAY,EAAE,UAAUkC,GAAG,EAAEC,GAAG,EAAE;MAC1C,IAAI,CAACpF,KAAK,CAACmF,GAAG,CAAC,EAAE;QACfH,KAAK,IAAIG,GAAG;QACZF,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,OAAOD,KAAK,GAAGC,OAAO;EACxB,CAAC,MAAM,IAAItC,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOpC,IAAI,CAAC8E,SAAS,CAACpC,YAAY,CAAC;EACrC,CAAC,MAAM;IACL;IACA,OAAO1C,IAAI,CAAC+E,aAAa,CAACrC,YAAY,CAAC,CAACN,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}