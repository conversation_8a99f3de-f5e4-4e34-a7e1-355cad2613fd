{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { eqNaN, isArray, isNumber } from 'zrender/lib/core/util.js';\nimport { WH, XY } from '../../util/graphic.js';\nimport { mathMax, mathMin } from '../../util/number.js';\nexport var MatrixCellLayoutInfoType = {\n  level: 1,\n  leaf: 2,\n  nonLeaf: 3\n};\n/**\n * @public Public to users in `chart.convertFromPixel`.\n */\nexport var MatrixClampOption = {\n  // No clamp, be falsy, equals to null/undefined. It means if the input part is\n  // null/undefined/NaN/outOfBoundary, the result part is NaN, rather than clamp to\n  // the boundary of the matrix.\n  none: 0,\n  // Clamp, where null/undefined/NaN/outOfBoundary can be used to cover the entire row/column.\n  all: 1,\n  body: 2,\n  corner: 3\n};\n/**\n * For the x direction,\n *  - find dimension cell from `xMatrixDim`,\n *      - If `xDimCell` or `yDimCell` is not a leaf, return the non-leaf cell itself.\n *  - otherwise find level from `yMatrixDim`.\n *  - otherwise return `NullUndefined`.\n *\n * For the y direction, it's the opposite.\n */\nexport function coordDataToAllCellLevelLayout(coordValue, dims, thisDimIdx // 0 | 1\n) {\n  // Find in body.\n  var result = dims[XY[thisDimIdx]].getCell(coordValue);\n  // Find in corner or dimension area.\n  if (!result && isNumber(coordValue) && coordValue < 0) {\n    result = dims[XY[1 - thisDimIdx]].getUnitLayoutInfo(thisDimIdx, Math.round(coordValue));\n  }\n  return result;\n}\nexport function resetXYLocatorRange(out) {\n  var rg = out || [];\n  rg[0] = rg[0] || [];\n  rg[1] = rg[1] || [];\n  rg[0][0] = rg[0][1] = rg[1][0] = rg[1][1] = NaN;\n  return rg;\n}\n/**\n * If illegal or out of boundary, set NaN to `locOut`. See `isXYLocatorRangeInvalidOnDim`.\n * x dimension and y dimension are calculated separately.\n */\nexport function parseCoordRangeOption(locOut,\n// If illegal input or can not find any target, save reason to it.\n// Do nothing if `NullUndefined`.\nreasonOut, data, dims, clamp) {\n  // x and y are supported to be handled separately - if one dimension is invalid\n  // (may be users do not need that), the other one should also be calculated.\n  parseCoordRangeOptionOnOneDim(locOut[0], reasonOut, clamp, data, dims, 0);\n  parseCoordRangeOptionOnOneDim(locOut[1], reasonOut, clamp, data, dims, 1);\n}\nfunction parseCoordRangeOptionOnOneDim(locDimOut, reasonOut, clamp, data, dims, dimIdx) {\n  locDimOut[0] = Infinity;\n  locDimOut[1] = -Infinity;\n  var dataOnDim = data[dimIdx];\n  var coordValArr = isArray(dataOnDim) ? dataOnDim : [dataOnDim];\n  var len = coordValArr.length;\n  var hasClamp = !!clamp;\n  if (len >= 1) {\n    parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, 0);\n    if (len > 1) {\n      // Users may intuitively input the coords like `[[x1, x2, x3], ...]`;\n      // consider the range as `[x1, x3]` in this case.\n      parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, len - 1);\n    }\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (reasonOut) {\n        reasonOut.push('Should be like [[\"x1\", \"x2\"], [\"y1\", \"y2\"]], or [\"x1\", \"y1\"], rather than empty.');\n      }\n    }\n    locDimOut[0] = locDimOut[1] = NaN;\n  }\n  if (hasClamp) {\n    // null/undefined/NaN or illegal data represents the entire row/column;\n    // Cover the entire locator regardless of body or corner, and confine it later.\n    var locLowerBound = -dims[XY[1 - dimIdx]].getLocatorCount(dimIdx);\n    var locUpperBound = dims[XY[dimIdx]].getLocatorCount(dimIdx) - 1;\n    if (clamp === MatrixClampOption.body) {\n      locLowerBound = mathMax(0, locLowerBound);\n    } else if (clamp === MatrixClampOption.corner) {\n      locUpperBound = mathMin(-1, locUpperBound);\n    }\n    if (locUpperBound < locLowerBound) {\n      // Also considered that both x and y has no cell.\n      locLowerBound = locUpperBound = NaN;\n    }\n    if (eqNaN(locDimOut[0])) {\n      locDimOut[0] = locLowerBound;\n    }\n    if (eqNaN(locDimOut[1])) {\n      locDimOut[1] = locUpperBound;\n    }\n    locDimOut[0] = mathMax(mathMin(locDimOut[0], locUpperBound), locLowerBound);\n    locDimOut[1] = mathMax(mathMin(locDimOut[1], locUpperBound), locLowerBound);\n  }\n}\n// The return val must be finite or NaN.\nfunction parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, partIdx) {\n  var layout = coordDataToAllCellLevelLayout(coordValArr[partIdx], dims, dimIdx);\n  if (!layout) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!hasClamp && reasonOut) {\n        reasonOut.push(\"Can not find cell by coord[\" + dimIdx + \"][\" + partIdx + \"].\");\n      }\n    }\n    locDimOut[0] = locDimOut[1] = NaN;\n    return;\n  }\n  var locatorA = layout.id[XY[dimIdx]];\n  var locatorB = locatorA;\n  var dimCell = cellLayoutInfoToDimCell(layout);\n  if (dimCell) {\n    // Handle non-leaf\n    locatorB += dimCell.span[XY[dimIdx]] - 1;\n  }\n  locDimOut[0] = mathMin(locDimOut[0], locatorA, locatorB);\n  locDimOut[1] = mathMax(locDimOut[1], locatorA, locatorB);\n}\n/**\n * @param locatorRange Must be the return of `parseCoordRangeOption`,\n *  where if not NaN, it must be a valid locator.\n */\nexport function isXYLocatorRangeInvalidOnDim(locatorRange, dimIdx) {\n  return eqNaN(locatorRange[dimIdx][0]) || eqNaN(locatorRange[dimIdx][1]);\n}\n// `locatorRange` will be expanded (modified) if an intersection is encountered.\nexport function resolveXYLocatorRangeByCellMerge(inOutLocatorRange,\n// Item indices coorespond to mergeDefList (len: mergeDefListTravelLen).\n// Indicating whether each item has be merged into the `locatorRange`\noutMergedMarkList, mergeDefList, mergeDefListTravelLen) {\n  outMergedMarkList = outMergedMarkList || _tmpOutMergedMarkList;\n  for (var idx = 0; idx < mergeDefListTravelLen; idx++) {\n    outMergedMarkList[idx] = false;\n  }\n  // In most case, cell merging definition list length is smaller than the range extent,\n  // therefore, to detection intersection, travelling cell merging definition list is probably\n  // performant than traveling the four edges of the rect formed by the locator range.\n  while (true) {\n    var expanded = false;\n    for (var idx = 0; idx < mergeDefListTravelLen; idx++) {\n      var mergeDef = mergeDefList[idx];\n      if (!outMergedMarkList[idx] && mergeDef.cellMergeOwner && expandXYLocatorRangeIfIntersect(inOutLocatorRange, mergeDef.locatorRange)) {\n        outMergedMarkList[idx] = true;\n        expanded = true;\n      }\n    }\n    if (!expanded) {\n      break;\n    }\n  }\n}\nvar _tmpOutMergedMarkList = [];\n// Return whether intersect.\n// `thisLocRange` will be expanded (modified) if an intersection is encountered.\nfunction expandXYLocatorRangeIfIntersect(thisLocRange, otherLocRange) {\n  if (!locatorRangeIntersectOneDim(thisLocRange[0], otherLocRange[0]) || !locatorRangeIntersectOneDim(thisLocRange[1], otherLocRange[1])) {\n    return false;\n  }\n  thisLocRange[0][0] = mathMin(thisLocRange[0][0], otherLocRange[0][0]);\n  thisLocRange[0][1] = mathMax(thisLocRange[0][1], otherLocRange[0][1]);\n  thisLocRange[1][0] = mathMin(thisLocRange[1][0], otherLocRange[1][0]);\n  thisLocRange[1][1] = mathMax(thisLocRange[1][1], otherLocRange[1][1]);\n  return true;\n}\n// Notice: If containing NaN, not intersect.\nfunction locatorRangeIntersectOneDim(locRange1OneDim, locRange2OneDim) {\n  return locRange1OneDim[1] >= locRange2OneDim[0] && locRange1OneDim[0] <= locRange2OneDim[1];\n}\nexport function fillIdSpanFromLocatorRange(owner, locatorRange) {\n  owner.id.set(locatorRange[0][0], locatorRange[1][0]);\n  owner.span.set(locatorRange[0][1] - owner.id.x + 1, locatorRange[1][1] - owner.id.y + 1);\n}\nexport function cloneXYLocatorRange(target, source) {\n  target[0][0] = source[0][0];\n  target[0][1] = source[0][1];\n  target[1][0] = source[1][0];\n  target[1][1] = source[1][1];\n}\n/**\n * If illegal, the corresponding x/y/width/height is set to `NaN`.\n * `x/width` or `y/height` is supported to be calculated separately,\n * i.e., one side are NaN, the other side are normal.\n * @param oneDimOut only write to `x/width` or `y/height`, depending on `dimIdx`.\n */\nexport function xyLocatorRangeToRectOneDim(oneDimOut, locRange, dims, dimIdx) {\n  var layoutMin = coordDataToAllCellLevelLayout(locRange[dimIdx][0], dims, dimIdx);\n  var layoutMax = coordDataToAllCellLevelLayout(locRange[dimIdx][1], dims, dimIdx);\n  oneDimOut[XY[dimIdx]] = oneDimOut[WH[dimIdx]] = NaN;\n  if (layoutMin && layoutMax) {\n    oneDimOut[XY[dimIdx]] = layoutMin.xy;\n    oneDimOut[WH[dimIdx]] = layoutMax.xy + layoutMax.wh - layoutMin.xy;\n  }\n}\n// No need currently, since `span` is not allowed to be defined directly by users.\n// /**\n//  * If either span x or y is valid and > 1, return parsed span, otherwise return `NullUndefined`.\n//  */\n// export function parseSpanOption(\n//     spanOptionHost: MatrixCellSpanOptionHost,\n//     dimCellPair: MatrixCellLayoutInfo[]\n// ): Point | NullUndefined {\n//     const spanX = parseSpanOnDim(spanOptionHost.spanX, dimCellPair[0], 0);\n//     const spanY = parseSpanOnDim(spanOptionHost.spanY, dimCellPair[1], 1);\n//     if (!eqNaN(spanX) || !eqNaN(spanY)) {\n//         return new Point(spanX || 1, spanY || 1);\n//     }\n//     function parseSpanOnDim(spanOption: unknown, dimCell: MatrixCellLayoutInfo, dimIdx: number): number {\n//         if (!isNumber(spanOption)) {\n//             return NaN;\n//         }\n//         // Ensure positive integer (not NaN) to avoid dead loop.\n//         const span = mathMax(1, Math.round(spanOption || 1)) || 1;\n//         // Clamp, and consider may also be specified as `Infinity` to span the entire col/row.\n//         return mathMin(span, mathMax(1, dimCell.dim.getLocatorCount(dimIdx) - dimCell.id[XY[dimIdx]]));\n//     }\n// }\n/**\n * @usage To get/set on dimension, use:\n *  `xyVal[XY[dim]] = val;` // set on this dimension.\n *  `xyVal[XY[1 - dim]] = val;` // set on the perpendicular dimension.\n */\nexport function setDimXYValue(out, dimIdx,\n// 0 | 1\nvalueOnThisDim, valueOnOtherDim) {\n  out[XY[dimIdx]] = valueOnThisDim;\n  out[XY[1 - dimIdx]] = valueOnOtherDim;\n  return out;\n}\n/**\n * Return NullUndefined if not dimension cell.\n */\nfunction cellLayoutInfoToDimCell(cellLayoutInfo) {\n  return cellLayoutInfo && (cellLayoutInfo.type === MatrixCellLayoutInfoType.leaf || cellLayoutInfo.type === MatrixCellLayoutInfoType.nonLeaf) ? cellLayoutInfo : null;\n}\nexport function createNaNRectLike() {\n  return {\n    x: NaN,\n    y: NaN,\n    width: NaN,\n    height: NaN\n  };\n}", "map": {"version": 3, "names": ["eqNaN", "isArray", "isNumber", "WH", "XY", "mathMax", "mathMin", "MatrixCellLayoutInfoType", "level", "leaf", "nonLeaf", "MatrixClampOption", "none", "all", "body", "corner", "coordDataToAllCellLevelLayout", "coordValue", "dims", "thisDimIdx", "result", "getCell", "getUnitLayoutInfo", "Math", "round", "resetXYLocatorRange", "out", "rg", "NaN", "parseCoordRangeOption", "locOut", "reasonOut", "data", "clamp", "parseCoordRangeOptionOnOneDim", "locDimOut", "dimIdx", "Infinity", "dataOnDim", "coordValArr", "len", "length", "hasClamp", "parseCoordRangeOptionOnOneDimOnePart", "process", "env", "NODE_ENV", "push", "locLowerBound", "getLocatorCount", "locUpperBound", "partIdx", "layout", "locatorA", "id", "locatorB", "dimCell", "cellLayoutInfoToDimCell", "span", "isXYLocatorRangeInvalidOnDim", "locatorRange", "resolveXYLocatorRangeByCellMerge", "inOutLocatorRange", "outMergedMarkList", "mergeDefList", "mergeDefListTravelLen", "_tmpOutMergedMarkList", "idx", "expanded", "mergeDef", "cellMergeOwner", "expandXYLocatorRangeIfIntersect", "thisLocRange", "otherLocRange", "locatorRangeIntersectOneDim", "locRange1OneDim", "locRange2OneDim", "fillIdSpanFromLocatorRange", "owner", "set", "x", "y", "cloneXYLocatorRange", "target", "source", "xyLocatorRangeToRectOneDim", "oneDimOut", "locRange", "layoutMin", "layoutMax", "xy", "wh", "setDimXYValue", "valueOnThisDim", "valueOnOtherDim", "cellLayoutInfo", "type", "createNaNRectLike", "width", "height"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/matrix/matrixCoordHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { eqNaN, isArray, isNumber } from 'zrender/lib/core/util.js';\nimport { WH, XY } from '../../util/graphic.js';\nimport { mathMax, mathMin } from '../../util/number.js';\nexport var MatrixCellLayoutInfoType = {\n  level: 1,\n  leaf: 2,\n  nonLeaf: 3\n};\n/**\n * @public Public to users in `chart.convertFromPixel`.\n */\nexport var MatrixClampOption = {\n  // No clamp, be falsy, equals to null/undefined. It means if the input part is\n  // null/undefined/NaN/outOfBoundary, the result part is NaN, rather than clamp to\n  // the boundary of the matrix.\n  none: 0,\n  // Clamp, where null/undefined/NaN/outOfBoundary can be used to cover the entire row/column.\n  all: 1,\n  body: 2,\n  corner: 3\n};\n/**\n * For the x direction,\n *  - find dimension cell from `xMatrixDim`,\n *      - If `xDimCell` or `yDimCell` is not a leaf, return the non-leaf cell itself.\n *  - otherwise find level from `yMatrixDim`.\n *  - otherwise return `NullUndefined`.\n *\n * For the y direction, it's the opposite.\n */\nexport function coordDataToAllCellLevelLayout(coordValue, dims, thisDimIdx // 0 | 1\n) {\n  // Find in body.\n  var result = dims[XY[thisDimIdx]].getCell(coordValue);\n  // Find in corner or dimension area.\n  if (!result && isNumber(coordValue) && coordValue < 0) {\n    result = dims[XY[1 - thisDimIdx]].getUnitLayoutInfo(thisDimIdx, Math.round(coordValue));\n  }\n  return result;\n}\nexport function resetXYLocatorRange(out) {\n  var rg = out || [];\n  rg[0] = rg[0] || [];\n  rg[1] = rg[1] || [];\n  rg[0][0] = rg[0][1] = rg[1][0] = rg[1][1] = NaN;\n  return rg;\n}\n/**\n * If illegal or out of boundary, set NaN to `locOut`. See `isXYLocatorRangeInvalidOnDim`.\n * x dimension and y dimension are calculated separately.\n */\nexport function parseCoordRangeOption(locOut,\n// If illegal input or can not find any target, save reason to it.\n// Do nothing if `NullUndefined`.\nreasonOut, data, dims, clamp) {\n  // x and y are supported to be handled separately - if one dimension is invalid\n  // (may be users do not need that), the other one should also be calculated.\n  parseCoordRangeOptionOnOneDim(locOut[0], reasonOut, clamp, data, dims, 0);\n  parseCoordRangeOptionOnOneDim(locOut[1], reasonOut, clamp, data, dims, 1);\n}\nfunction parseCoordRangeOptionOnOneDim(locDimOut, reasonOut, clamp, data, dims, dimIdx) {\n  locDimOut[0] = Infinity;\n  locDimOut[1] = -Infinity;\n  var dataOnDim = data[dimIdx];\n  var coordValArr = isArray(dataOnDim) ? dataOnDim : [dataOnDim];\n  var len = coordValArr.length;\n  var hasClamp = !!clamp;\n  if (len >= 1) {\n    parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, 0);\n    if (len > 1) {\n      // Users may intuitively input the coords like `[[x1, x2, x3], ...]`;\n      // consider the range as `[x1, x3]` in this case.\n      parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, len - 1);\n    }\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      if (reasonOut) {\n        reasonOut.push('Should be like [[\"x1\", \"x2\"], [\"y1\", \"y2\"]], or [\"x1\", \"y1\"], rather than empty.');\n      }\n    }\n    locDimOut[0] = locDimOut[1] = NaN;\n  }\n  if (hasClamp) {\n    // null/undefined/NaN or illegal data represents the entire row/column;\n    // Cover the entire locator regardless of body or corner, and confine it later.\n    var locLowerBound = -dims[XY[1 - dimIdx]].getLocatorCount(dimIdx);\n    var locUpperBound = dims[XY[dimIdx]].getLocatorCount(dimIdx) - 1;\n    if (clamp === MatrixClampOption.body) {\n      locLowerBound = mathMax(0, locLowerBound);\n    } else if (clamp === MatrixClampOption.corner) {\n      locUpperBound = mathMin(-1, locUpperBound);\n    }\n    if (locUpperBound < locLowerBound) {\n      // Also considered that both x and y has no cell.\n      locLowerBound = locUpperBound = NaN;\n    }\n    if (eqNaN(locDimOut[0])) {\n      locDimOut[0] = locLowerBound;\n    }\n    if (eqNaN(locDimOut[1])) {\n      locDimOut[1] = locUpperBound;\n    }\n    locDimOut[0] = mathMax(mathMin(locDimOut[0], locUpperBound), locLowerBound);\n    locDimOut[1] = mathMax(mathMin(locDimOut[1], locUpperBound), locLowerBound);\n  }\n}\n// The return val must be finite or NaN.\nfunction parseCoordRangeOptionOnOneDimOnePart(locDimOut, reasonOut, coordValArr, hasClamp, dims, dimIdx, partIdx) {\n  var layout = coordDataToAllCellLevelLayout(coordValArr[partIdx], dims, dimIdx);\n  if (!layout) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!hasClamp && reasonOut) {\n        reasonOut.push(\"Can not find cell by coord[\" + dimIdx + \"][\" + partIdx + \"].\");\n      }\n    }\n    locDimOut[0] = locDimOut[1] = NaN;\n    return;\n  }\n  var locatorA = layout.id[XY[dimIdx]];\n  var locatorB = locatorA;\n  var dimCell = cellLayoutInfoToDimCell(layout);\n  if (dimCell) {\n    // Handle non-leaf\n    locatorB += dimCell.span[XY[dimIdx]] - 1;\n  }\n  locDimOut[0] = mathMin(locDimOut[0], locatorA, locatorB);\n  locDimOut[1] = mathMax(locDimOut[1], locatorA, locatorB);\n}\n/**\n * @param locatorRange Must be the return of `parseCoordRangeOption`,\n *  where if not NaN, it must be a valid locator.\n */\nexport function isXYLocatorRangeInvalidOnDim(locatorRange, dimIdx) {\n  return eqNaN(locatorRange[dimIdx][0]) || eqNaN(locatorRange[dimIdx][1]);\n}\n// `locatorRange` will be expanded (modified) if an intersection is encountered.\nexport function resolveXYLocatorRangeByCellMerge(inOutLocatorRange,\n// Item indices coorespond to mergeDefList (len: mergeDefListTravelLen).\n// Indicating whether each item has be merged into the `locatorRange`\noutMergedMarkList, mergeDefList, mergeDefListTravelLen) {\n  outMergedMarkList = outMergedMarkList || _tmpOutMergedMarkList;\n  for (var idx = 0; idx < mergeDefListTravelLen; idx++) {\n    outMergedMarkList[idx] = false;\n  }\n  // In most case, cell merging definition list length is smaller than the range extent,\n  // therefore, to detection intersection, travelling cell merging definition list is probably\n  // performant than traveling the four edges of the rect formed by the locator range.\n  while (true) {\n    var expanded = false;\n    for (var idx = 0; idx < mergeDefListTravelLen; idx++) {\n      var mergeDef = mergeDefList[idx];\n      if (!outMergedMarkList[idx] && mergeDef.cellMergeOwner && expandXYLocatorRangeIfIntersect(inOutLocatorRange, mergeDef.locatorRange)) {\n        outMergedMarkList[idx] = true;\n        expanded = true;\n      }\n    }\n    if (!expanded) {\n      break;\n    }\n  }\n}\nvar _tmpOutMergedMarkList = [];\n// Return whether intersect.\n// `thisLocRange` will be expanded (modified) if an intersection is encountered.\nfunction expandXYLocatorRangeIfIntersect(thisLocRange, otherLocRange) {\n  if (!locatorRangeIntersectOneDim(thisLocRange[0], otherLocRange[0]) || !locatorRangeIntersectOneDim(thisLocRange[1], otherLocRange[1])) {\n    return false;\n  }\n  thisLocRange[0][0] = mathMin(thisLocRange[0][0], otherLocRange[0][0]);\n  thisLocRange[0][1] = mathMax(thisLocRange[0][1], otherLocRange[0][1]);\n  thisLocRange[1][0] = mathMin(thisLocRange[1][0], otherLocRange[1][0]);\n  thisLocRange[1][1] = mathMax(thisLocRange[1][1], otherLocRange[1][1]);\n  return true;\n}\n// Notice: If containing NaN, not intersect.\nfunction locatorRangeIntersectOneDim(locRange1OneDim, locRange2OneDim) {\n  return locRange1OneDim[1] >= locRange2OneDim[0] && locRange1OneDim[0] <= locRange2OneDim[1];\n}\nexport function fillIdSpanFromLocatorRange(owner, locatorRange) {\n  owner.id.set(locatorRange[0][0], locatorRange[1][0]);\n  owner.span.set(locatorRange[0][1] - owner.id.x + 1, locatorRange[1][1] - owner.id.y + 1);\n}\nexport function cloneXYLocatorRange(target, source) {\n  target[0][0] = source[0][0];\n  target[0][1] = source[0][1];\n  target[1][0] = source[1][0];\n  target[1][1] = source[1][1];\n}\n/**\n * If illegal, the corresponding x/y/width/height is set to `NaN`.\n * `x/width` or `y/height` is supported to be calculated separately,\n * i.e., one side are NaN, the other side are normal.\n * @param oneDimOut only write to `x/width` or `y/height`, depending on `dimIdx`.\n */\nexport function xyLocatorRangeToRectOneDim(oneDimOut, locRange, dims, dimIdx) {\n  var layoutMin = coordDataToAllCellLevelLayout(locRange[dimIdx][0], dims, dimIdx);\n  var layoutMax = coordDataToAllCellLevelLayout(locRange[dimIdx][1], dims, dimIdx);\n  oneDimOut[XY[dimIdx]] = oneDimOut[WH[dimIdx]] = NaN;\n  if (layoutMin && layoutMax) {\n    oneDimOut[XY[dimIdx]] = layoutMin.xy;\n    oneDimOut[WH[dimIdx]] = layoutMax.xy + layoutMax.wh - layoutMin.xy;\n  }\n}\n// No need currently, since `span` is not allowed to be defined directly by users.\n// /**\n//  * If either span x or y is valid and > 1, return parsed span, otherwise return `NullUndefined`.\n//  */\n// export function parseSpanOption(\n//     spanOptionHost: MatrixCellSpanOptionHost,\n//     dimCellPair: MatrixCellLayoutInfo[]\n// ): Point | NullUndefined {\n//     const spanX = parseSpanOnDim(spanOptionHost.spanX, dimCellPair[0], 0);\n//     const spanY = parseSpanOnDim(spanOptionHost.spanY, dimCellPair[1], 1);\n//     if (!eqNaN(spanX) || !eqNaN(spanY)) {\n//         return new Point(spanX || 1, spanY || 1);\n//     }\n//     function parseSpanOnDim(spanOption: unknown, dimCell: MatrixCellLayoutInfo, dimIdx: number): number {\n//         if (!isNumber(spanOption)) {\n//             return NaN;\n//         }\n//         // Ensure positive integer (not NaN) to avoid dead loop.\n//         const span = mathMax(1, Math.round(spanOption || 1)) || 1;\n//         // Clamp, and consider may also be specified as `Infinity` to span the entire col/row.\n//         return mathMin(span, mathMax(1, dimCell.dim.getLocatorCount(dimIdx) - dimCell.id[XY[dimIdx]]));\n//     }\n// }\n/**\n * @usage To get/set on dimension, use:\n *  `xyVal[XY[dim]] = val;` // set on this dimension.\n *  `xyVal[XY[1 - dim]] = val;` // set on the perpendicular dimension.\n */\nexport function setDimXYValue(out, dimIdx,\n// 0 | 1\nvalueOnThisDim, valueOnOtherDim) {\n  out[XY[dimIdx]] = valueOnThisDim;\n  out[XY[1 - dimIdx]] = valueOnOtherDim;\n  return out;\n}\n/**\n * Return NullUndefined if not dimension cell.\n */\nfunction cellLayoutInfoToDimCell(cellLayoutInfo) {\n  return cellLayoutInfo && (cellLayoutInfo.type === MatrixCellLayoutInfoType.leaf || cellLayoutInfo.type === MatrixCellLayoutInfoType.nonLeaf) ? cellLayoutInfo : null;\n}\nexport function createNaNRectLike() {\n  return {\n    x: NaN,\n    y: NaN,\n    width: NaN,\n    height: NaN\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AACnE,SAASC,EAAE,EAAEC,EAAE,QAAQ,uBAAuB;AAC9C,SAASC,OAAO,EAAEC,OAAO,QAAQ,sBAAsB;AACvD,OAAO,IAAIC,wBAAwB,GAAG;EACpCC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIC,iBAAiB,GAAG;EAC7B;EACA;EACA;EACAC,IAAI,EAAE,CAAC;EACP;EACAC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACC,UAAU,EAAEC,IAAI,EAAEC,UAAU,CAAC;AAAA,EACzE;EACA;EACA,IAAIC,MAAM,GAAGF,IAAI,CAACd,EAAE,CAACe,UAAU,CAAC,CAAC,CAACE,OAAO,CAACJ,UAAU,CAAC;EACrD;EACA,IAAI,CAACG,MAAM,IAAIlB,QAAQ,CAACe,UAAU,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACrDG,MAAM,GAAGF,IAAI,CAACd,EAAE,CAAC,CAAC,GAAGe,UAAU,CAAC,CAAC,CAACG,iBAAiB,CAACH,UAAU,EAAEI,IAAI,CAACC,KAAK,CAACP,UAAU,CAAC,CAAC;EACzF;EACA,OAAOG,MAAM;AACf;AACA,OAAO,SAASK,mBAAmBA,CAACC,GAAG,EAAE;EACvC,IAAIC,EAAE,GAAGD,GAAG,IAAI,EAAE;EAClBC,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;EACnBA,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;EACnBA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,GAAG;EAC/C,OAAOD,EAAE;AACX;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,qBAAqBA,CAACC,MAAM;AAC5C;AACA;AACAC,SAAS,EAAEC,IAAI,EAAEd,IAAI,EAAEe,KAAK,EAAE;EAC5B;EACA;EACAC,6BAA6B,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAEE,KAAK,EAAED,IAAI,EAAEd,IAAI,EAAE,CAAC,CAAC;EACzEgB,6BAA6B,CAACJ,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,EAAEE,KAAK,EAAED,IAAI,EAAEd,IAAI,EAAE,CAAC,CAAC;AAC3E;AACA,SAASgB,6BAA6BA,CAACC,SAAS,EAAEJ,SAAS,EAAEE,KAAK,EAAED,IAAI,EAAEd,IAAI,EAAEkB,MAAM,EAAE;EACtFD,SAAS,CAAC,CAAC,CAAC,GAAGE,QAAQ;EACvBF,SAAS,CAAC,CAAC,CAAC,GAAG,CAACE,QAAQ;EACxB,IAAIC,SAAS,GAAGN,IAAI,CAACI,MAAM,CAAC;EAC5B,IAAIG,WAAW,GAAGtC,OAAO,CAACqC,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;EAC9D,IAAIE,GAAG,GAAGD,WAAW,CAACE,MAAM;EAC5B,IAAIC,QAAQ,GAAG,CAAC,CAACT,KAAK;EACtB,IAAIO,GAAG,IAAI,CAAC,EAAE;IACZG,oCAAoC,CAACR,SAAS,EAAEJ,SAAS,EAAEQ,WAAW,EAAEG,QAAQ,EAAExB,IAAI,EAAEkB,MAAM,EAAE,CAAC,CAAC;IAClG,IAAII,GAAG,GAAG,CAAC,EAAE;MACX;MACA;MACAG,oCAAoC,CAACR,SAAS,EAAEJ,SAAS,EAAEQ,WAAW,EAAEG,QAAQ,EAAExB,IAAI,EAAEkB,MAAM,EAAEI,GAAG,GAAG,CAAC,CAAC;IAC1G;EACF,CAAC,MAAM;IACL,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIf,SAAS,EAAE;QACbA,SAAS,CAACgB,IAAI,CAAC,kFAAkF,CAAC;MACpG;IACF;IACAZ,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGP,GAAG;EACnC;EACA,IAAIc,QAAQ,EAAE;IACZ;IACA;IACA,IAAIM,aAAa,GAAG,CAAC9B,IAAI,CAACd,EAAE,CAAC,CAAC,GAAGgC,MAAM,CAAC,CAAC,CAACa,eAAe,CAACb,MAAM,CAAC;IACjE,IAAIc,aAAa,GAAGhC,IAAI,CAACd,EAAE,CAACgC,MAAM,CAAC,CAAC,CAACa,eAAe,CAACb,MAAM,CAAC,GAAG,CAAC;IAChE,IAAIH,KAAK,KAAKtB,iBAAiB,CAACG,IAAI,EAAE;MACpCkC,aAAa,GAAG3C,OAAO,CAAC,CAAC,EAAE2C,aAAa,CAAC;IAC3C,CAAC,MAAM,IAAIf,KAAK,KAAKtB,iBAAiB,CAACI,MAAM,EAAE;MAC7CmC,aAAa,GAAG5C,OAAO,CAAC,CAAC,CAAC,EAAE4C,aAAa,CAAC;IAC5C;IACA,IAAIA,aAAa,GAAGF,aAAa,EAAE;MACjC;MACAA,aAAa,GAAGE,aAAa,GAAGtB,GAAG;IACrC;IACA,IAAI5B,KAAK,CAACmC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACvBA,SAAS,CAAC,CAAC,CAAC,GAAGa,aAAa;IAC9B;IACA,IAAIhD,KAAK,CAACmC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACvBA,SAAS,CAAC,CAAC,CAAC,GAAGe,aAAa;IAC9B;IACAf,SAAS,CAAC,CAAC,CAAC,GAAG9B,OAAO,CAACC,OAAO,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAEe,aAAa,CAAC,EAAEF,aAAa,CAAC;IAC3Eb,SAAS,CAAC,CAAC,CAAC,GAAG9B,OAAO,CAACC,OAAO,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAEe,aAAa,CAAC,EAAEF,aAAa,CAAC;EAC7E;AACF;AACA;AACA,SAASL,oCAAoCA,CAACR,SAAS,EAAEJ,SAAS,EAAEQ,WAAW,EAAEG,QAAQ,EAAExB,IAAI,EAAEkB,MAAM,EAAEe,OAAO,EAAE;EAChH,IAAIC,MAAM,GAAGpC,6BAA6B,CAACuB,WAAW,CAACY,OAAO,CAAC,EAAEjC,IAAI,EAAEkB,MAAM,CAAC;EAC9E,IAAI,CAACgB,MAAM,EAAE;IACX,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACJ,QAAQ,IAAIX,SAAS,EAAE;QAC1BA,SAAS,CAACgB,IAAI,CAAC,6BAA6B,GAAGX,MAAM,GAAG,IAAI,GAAGe,OAAO,GAAG,IAAI,CAAC;MAChF;IACF;IACAhB,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGP,GAAG;IACjC;EACF;EACA,IAAIyB,QAAQ,GAAGD,MAAM,CAACE,EAAE,CAAClD,EAAE,CAACgC,MAAM,CAAC,CAAC;EACpC,IAAImB,QAAQ,GAAGF,QAAQ;EACvB,IAAIG,OAAO,GAAGC,uBAAuB,CAACL,MAAM,CAAC;EAC7C,IAAII,OAAO,EAAE;IACX;IACAD,QAAQ,IAAIC,OAAO,CAACE,IAAI,CAACtD,EAAE,CAACgC,MAAM,CAAC,CAAC,GAAG,CAAC;EAC1C;EACAD,SAAS,CAAC,CAAC,CAAC,GAAG7B,OAAO,CAAC6B,SAAS,CAAC,CAAC,CAAC,EAAEkB,QAAQ,EAAEE,QAAQ,CAAC;EACxDpB,SAAS,CAAC,CAAC,CAAC,GAAG9B,OAAO,CAAC8B,SAAS,CAAC,CAAC,CAAC,EAAEkB,QAAQ,EAAEE,QAAQ,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,4BAA4BA,CAACC,YAAY,EAAExB,MAAM,EAAE;EACjE,OAAOpC,KAAK,CAAC4D,YAAY,CAACxB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIpC,KAAK,CAAC4D,YAAY,CAACxB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE;AACA;AACA,OAAO,SAASyB,gCAAgCA,CAACC,iBAAiB;AAClE;AACA;AACAC,iBAAiB,EAAEC,YAAY,EAAEC,qBAAqB,EAAE;EACtDF,iBAAiB,GAAGA,iBAAiB,IAAIG,qBAAqB;EAC9D,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,qBAAqB,EAAEE,GAAG,EAAE,EAAE;IACpDJ,iBAAiB,CAACI,GAAG,CAAC,GAAG,KAAK;EAChC;EACA;EACA;EACA;EACA,OAAO,IAAI,EAAE;IACX,IAAIC,QAAQ,GAAG,KAAK;IACpB,KAAK,IAAID,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,qBAAqB,EAAEE,GAAG,EAAE,EAAE;MACpD,IAAIE,QAAQ,GAAGL,YAAY,CAACG,GAAG,CAAC;MAChC,IAAI,CAACJ,iBAAiB,CAACI,GAAG,CAAC,IAAIE,QAAQ,CAACC,cAAc,IAAIC,+BAA+B,CAACT,iBAAiB,EAAEO,QAAQ,CAACT,YAAY,CAAC,EAAE;QACnIG,iBAAiB,CAACI,GAAG,CAAC,GAAG,IAAI;QAC7BC,QAAQ,GAAG,IAAI;MACjB;IACF;IACA,IAAI,CAACA,QAAQ,EAAE;MACb;IACF;EACF;AACF;AACA,IAAIF,qBAAqB,GAAG,EAAE;AAC9B;AACA;AACA,SAASK,+BAA+BA,CAACC,YAAY,EAAEC,aAAa,EAAE;EACpE,IAAI,CAACC,2BAA2B,CAACF,YAAY,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,2BAA2B,CAACF,YAAY,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;IACtI,OAAO,KAAK;EACd;EACAD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlE,OAAO,CAACkE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrED,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGnE,OAAO,CAACmE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrED,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlE,OAAO,CAACkE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrED,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGnE,OAAO,CAACmE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrE,OAAO,IAAI;AACb;AACA;AACA,SAASC,2BAA2BA,CAACC,eAAe,EAAEC,eAAe,EAAE;EACrE,OAAOD,eAAe,CAAC,CAAC,CAAC,IAAIC,eAAe,CAAC,CAAC,CAAC,IAAID,eAAe,CAAC,CAAC,CAAC,IAAIC,eAAe,CAAC,CAAC,CAAC;AAC7F;AACA,OAAO,SAASC,0BAA0BA,CAACC,KAAK,EAAElB,YAAY,EAAE;EAC9DkB,KAAK,CAACxB,EAAE,CAACyB,GAAG,CAACnB,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpDkB,KAAK,CAACpB,IAAI,CAACqB,GAAG,CAACnB,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkB,KAAK,CAACxB,EAAE,CAAC0B,CAAC,GAAG,CAAC,EAAEpB,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkB,KAAK,CAACxB,EAAE,CAAC2B,CAAC,GAAG,CAAC,CAAC;AAC1F;AACA,OAAO,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClDD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3BD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3BD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3BD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,SAAS,EAAEC,QAAQ,EAAErE,IAAI,EAAEkB,MAAM,EAAE;EAC5E,IAAIoD,SAAS,GAAGxE,6BAA6B,CAACuE,QAAQ,CAACnD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAElB,IAAI,EAAEkB,MAAM,CAAC;EAChF,IAAIqD,SAAS,GAAGzE,6BAA6B,CAACuE,QAAQ,CAACnD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAElB,IAAI,EAAEkB,MAAM,CAAC;EAChFkD,SAAS,CAAClF,EAAE,CAACgC,MAAM,CAAC,CAAC,GAAGkD,SAAS,CAACnF,EAAE,CAACiC,MAAM,CAAC,CAAC,GAAGR,GAAG;EACnD,IAAI4D,SAAS,IAAIC,SAAS,EAAE;IAC1BH,SAAS,CAAClF,EAAE,CAACgC,MAAM,CAAC,CAAC,GAAGoD,SAAS,CAACE,EAAE;IACpCJ,SAAS,CAACnF,EAAE,CAACiC,MAAM,CAAC,CAAC,GAAGqD,SAAS,CAACC,EAAE,GAAGD,SAAS,CAACE,EAAE,GAAGH,SAAS,CAACE,EAAE;EACpE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAAClE,GAAG,EAAEU,MAAM;AACzC;AACAyD,cAAc,EAAEC,eAAe,EAAE;EAC/BpE,GAAG,CAACtB,EAAE,CAACgC,MAAM,CAAC,CAAC,GAAGyD,cAAc;EAChCnE,GAAG,CAACtB,EAAE,CAAC,CAAC,GAAGgC,MAAM,CAAC,CAAC,GAAG0D,eAAe;EACrC,OAAOpE,GAAG;AACZ;AACA;AACA;AACA;AACA,SAAS+B,uBAAuBA,CAACsC,cAAc,EAAE;EAC/C,OAAOA,cAAc,KAAKA,cAAc,CAACC,IAAI,KAAKzF,wBAAwB,CAACE,IAAI,IAAIsF,cAAc,CAACC,IAAI,KAAKzF,wBAAwB,CAACG,OAAO,CAAC,GAAGqF,cAAc,GAAG,IAAI;AACtK;AACA,OAAO,SAASE,iBAAiBA,CAAA,EAAG;EAClC,OAAO;IACLjB,CAAC,EAAEpD,GAAG;IACNqD,CAAC,EAAErD,GAAG;IACNsE,KAAK,EAAEtE,GAAG;IACVuE,MAAM,EAAEvE;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}