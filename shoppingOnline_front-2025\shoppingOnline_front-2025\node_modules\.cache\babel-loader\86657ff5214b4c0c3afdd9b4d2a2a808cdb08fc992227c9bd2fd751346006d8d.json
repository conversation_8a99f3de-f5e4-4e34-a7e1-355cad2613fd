{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { unionAxisExtentFromData } from '../../coord/axisHelper.js';\nimport { ensureScaleRawExtentInfo } from '../../coord/scaleRawExtentInfo.js';\nimport { getAxisMainType, isCoordSupported } from './helper.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\n/**\n * Operate single axis.\n * One axis can only operated by one axis operator.\n * Different dataZoomModels may be defined to operate the same axis.\n * (i.e. 'inside' data zoom and 'slider' data zoom components)\n * So dataZoomModels share one axisProxy in that case.\n */\nvar AxisProxy = /** @class */function () {\n  function AxisProxy(dimName, axisIndex, dataZoomModel, ecModel) {\n    this._dimName = dimName;\n    this._axisIndex = axisIndex;\n    this.ecModel = ecModel;\n    this._dataZoomModel = dataZoomModel;\n    // /**\n    //  * @readOnly\n    //  * @private\n    //  */\n    // this.hasSeriesStacked;\n  }\n  /**\n   * Whether the axisProxy is hosted by dataZoomModel.\n   */\n  AxisProxy.prototype.hostedBy = function (dataZoomModel) {\n    return this._dataZoomModel === dataZoomModel;\n  };\n  /**\n   * @return Value can only be NaN or finite value.\n   */\n  AxisProxy.prototype.getDataValueWindow = function () {\n    return this._valueWindow.slice();\n  };\n  /**\n   * @return {Array.<number>}\n   */\n  AxisProxy.prototype.getDataPercentWindow = function () {\n    return this._percentWindow.slice();\n  };\n  AxisProxy.prototype.getTargetSeriesModels = function () {\n    var seriesModels = [];\n    this.ecModel.eachSeries(function (seriesModel) {\n      if (isCoordSupported(seriesModel)) {\n        var axisMainType = getAxisMainType(this._dimName);\n        var axisModel = seriesModel.getReferringComponents(axisMainType, SINGLE_REFERRING).models[0];\n        if (axisModel && this._axisIndex === axisModel.componentIndex) {\n          seriesModels.push(seriesModel);\n        }\n      }\n    }, this);\n    return seriesModels;\n  };\n  AxisProxy.prototype.getAxisModel = function () {\n    return this.ecModel.getComponent(this._dimName + 'Axis', this._axisIndex);\n  };\n  AxisProxy.prototype.getMinMaxSpan = function () {\n    return zrUtil.clone(this._minMaxSpan);\n  };\n  /**\n   * Only calculate by given range and this._dataExtent, do not change anything.\n   */\n  AxisProxy.prototype.calculateDataWindow = function (opt) {\n    var dataExtent = this._dataExtent;\n    var axisModel = this.getAxisModel();\n    var scale = axisModel.axis.scale;\n    var rangePropMode = this._dataZoomModel.getRangePropMode();\n    var percentExtent = [0, 100];\n    var percentWindow = [];\n    var valueWindow = [];\n    var hasPropModeValue;\n    each(['start', 'end'], function (prop, idx) {\n      var boundPercent = opt[prop];\n      var boundValue = opt[prop + 'Value'];\n      // Notice: dataZoom is based either on `percentProp` ('start', 'end') or\n      // on `valueProp` ('startValue', 'endValue'). (They are based on the data extent\n      // but not min/max of axis, which will be calculated by data window then).\n      // The former one is suitable for cases that a dataZoom component controls multiple\n      // axes with different unit or extent, and the latter one is suitable for accurate\n      // zoom by pixel (e.g., in dataZoomSelect).\n      // we use `getRangePropMode()` to mark which prop is used. `rangePropMode` is updated\n      // only when setOption or dispatchAction, otherwise it remains its original value.\n      // (Why not only record `percentProp` and always map to `valueProp`? Because\n      // the map `valueProp` -> `percentProp` -> `valueProp` probably not the original\n      // `valueProp`. consider two axes constrolled by one dataZoom. They have different\n      // data extent. All of values that are overflow the `dataExtent` will be calculated\n      // to percent '100%').\n      if (rangePropMode[idx] === 'percent') {\n        boundPercent == null && (boundPercent = percentExtent[idx]);\n        // Use scale.parse to math round for category or time axis.\n        boundValue = scale.parse(numberUtil.linearMap(boundPercent, percentExtent, dataExtent));\n      } else {\n        hasPropModeValue = true;\n        boundValue = boundValue == null ? dataExtent[idx] : scale.parse(boundValue);\n        // Calculating `percent` from `value` may be not accurate, because\n        // This calculation can not be inversed, because all of values that\n        // are overflow the `dataExtent` will be calculated to percent '100%'\n        boundPercent = numberUtil.linearMap(boundValue, dataExtent, percentExtent);\n      }\n      // valueWindow[idx] = round(boundValue);\n      // percentWindow[idx] = round(boundPercent);\n      // fallback to extent start/end when parsed value or percent is invalid\n      valueWindow[idx] = boundValue == null || isNaN(boundValue) ? dataExtent[idx] : boundValue;\n      percentWindow[idx] = boundPercent == null || isNaN(boundPercent) ? percentExtent[idx] : boundPercent;\n    });\n    asc(valueWindow);\n    asc(percentWindow);\n    // The windows from user calling of `dispatchAction` might be out of the extent,\n    // or do not obey the `min/maxSpan`, `min/maxValueSpan`. But we don't restrict window\n    // by `zoomLock` here, because we see `zoomLock` just as a interaction constraint,\n    // where API is able to initialize/modify the window size even though `zoomLock`\n    // specified.\n    var spans = this._minMaxSpan;\n    hasPropModeValue ? restrictSet(valueWindow, percentWindow, dataExtent, percentExtent, false) : restrictSet(percentWindow, valueWindow, percentExtent, dataExtent, true);\n    function restrictSet(fromWindow, toWindow, fromExtent, toExtent, toValue) {\n      var suffix = toValue ? 'Span' : 'ValueSpan';\n      sliderMove(0, fromWindow, fromExtent, 'all', spans['min' + suffix], spans['max' + suffix]);\n      for (var i = 0; i < 2; i++) {\n        toWindow[i] = numberUtil.linearMap(fromWindow[i], fromExtent, toExtent, true);\n        toValue && (toWindow[i] = scale.parse(toWindow[i]));\n      }\n    }\n    return {\n      valueWindow: valueWindow,\n      percentWindow: percentWindow\n    };\n  };\n  /**\n   * Notice: reset should not be called before series.restoreData() is called,\n   * so it is recommended to be called in \"process stage\" but not \"model init\n   * stage\".\n   */\n  AxisProxy.prototype.reset = function (dataZoomModel) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var targetSeries = this.getTargetSeriesModels();\n    // Culculate data window and data extent, and record them.\n    this._dataExtent = calculateDataExtent(this, this._dimName, targetSeries);\n    // `calculateDataWindow` uses min/maxSpan.\n    this._updateMinMaxSpan();\n    var dataWindow = this.calculateDataWindow(dataZoomModel.settledOption);\n    this._valueWindow = dataWindow.valueWindow;\n    this._percentWindow = dataWindow.percentWindow;\n    // Update axis setting then.\n    this._setAxisModel();\n  };\n  AxisProxy.prototype.filterData = function (dataZoomModel, api) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var axisDim = this._dimName;\n    var seriesModels = this.getTargetSeriesModels();\n    var filterMode = dataZoomModel.get('filterMode');\n    var valueWindow = this._valueWindow;\n    if (filterMode === 'none') {\n      return;\n    }\n    // FIXME\n    // Toolbox may has dataZoom injected. And if there are stacked bar chart\n    // with NaN data, NaN will be filtered and stack will be wrong.\n    // So we need to force the mode to be set empty.\n    // In fect, it is not a big deal that do not support filterMode-'filter'\n    // when using toolbox#dataZoom, utill tooltip#dataZoom support \"single axis\n    // selection\" some day, which might need \"adapt to data extent on the\n    // otherAxis\", which is disabled by filterMode-'empty'.\n    // But currently, stack has been fixed to based on value but not index,\n    // so this is not an issue any more.\n    // let otherAxisModel = this.getOtherAxisModel();\n    // if (dataZoomModel.get('$fromToolbox')\n    //     && otherAxisModel\n    //     && otherAxisModel.hasSeriesStacked\n    // ) {\n    //     filterMode = 'empty';\n    // }\n    // TODO\n    // filterMode 'weakFilter' and 'empty' is not optimized for huge data yet.\n    each(seriesModels, function (seriesModel) {\n      var seriesData = seriesModel.getData();\n      var dataDims = seriesData.mapDimensionsAll(axisDim);\n      if (!dataDims.length) {\n        return;\n      }\n      if (filterMode === 'weakFilter') {\n        var store_1 = seriesData.getStore();\n        var dataDimIndices_1 = zrUtil.map(dataDims, function (dim) {\n          return seriesData.getDimensionIndex(dim);\n        }, seriesData);\n        seriesData.filterSelf(function (dataIndex) {\n          var leftOut;\n          var rightOut;\n          var hasValue;\n          for (var i = 0; i < dataDims.length; i++) {\n            var value = store_1.get(dataDimIndices_1[i], dataIndex);\n            var thisHasValue = !isNaN(value);\n            var thisLeftOut = value < valueWindow[0];\n            var thisRightOut = value > valueWindow[1];\n            if (thisHasValue && !thisLeftOut && !thisRightOut) {\n              return true;\n            }\n            thisHasValue && (hasValue = true);\n            thisLeftOut && (leftOut = true);\n            thisRightOut && (rightOut = true);\n          }\n          // If both left out and right out, do not filter.\n          return hasValue && leftOut && rightOut;\n        });\n      } else {\n        each(dataDims, function (dim) {\n          if (filterMode === 'empty') {\n            seriesModel.setData(seriesData = seriesData.map(dim, function (value) {\n              return !isInWindow(value) ? NaN : value;\n            }));\n          } else {\n            var range = {};\n            range[dim] = valueWindow;\n            // console.time('select');\n            seriesData.selectRange(range);\n            // console.timeEnd('select');\n          }\n        });\n      }\n      each(dataDims, function (dim) {\n        seriesData.setApproximateExtent(valueWindow, dim);\n      });\n    });\n    function isInWindow(value) {\n      return value >= valueWindow[0] && value <= valueWindow[1];\n    }\n  };\n  AxisProxy.prototype._updateMinMaxSpan = function () {\n    var minMaxSpan = this._minMaxSpan = {};\n    var dataZoomModel = this._dataZoomModel;\n    var dataExtent = this._dataExtent;\n    each(['min', 'max'], function (minMax) {\n      var percentSpan = dataZoomModel.get(minMax + 'Span');\n      var valueSpan = dataZoomModel.get(minMax + 'ValueSpan');\n      valueSpan != null && (valueSpan = this.getAxisModel().axis.scale.parse(valueSpan));\n      // minValueSpan and maxValueSpan has higher priority than minSpan and maxSpan\n      if (valueSpan != null) {\n        percentSpan = numberUtil.linearMap(dataExtent[0] + valueSpan, dataExtent, [0, 100], true);\n      } else if (percentSpan != null) {\n        valueSpan = numberUtil.linearMap(percentSpan, [0, 100], dataExtent, true) - dataExtent[0];\n      }\n      minMaxSpan[minMax + 'Span'] = percentSpan;\n      minMaxSpan[minMax + 'ValueSpan'] = valueSpan;\n    }, this);\n  };\n  AxisProxy.prototype._setAxisModel = function () {\n    var axisModel = this.getAxisModel();\n    var percentWindow = this._percentWindow;\n    var valueWindow = this._valueWindow;\n    if (!percentWindow) {\n      return;\n    }\n    // [0, 500]: arbitrary value, guess axis extent.\n    var precision = numberUtil.getPixelPrecision(valueWindow, [0, 500]);\n    precision = Math.min(precision, 20);\n    // For value axis, if min/max/scale are not set, we just use the extent obtained\n    // by series data, which may be a little different from the extent calculated by\n    // `axisHelper.getScaleExtent`. But the different just affects the experience a\n    // little when zooming. So it will not be fixed until some users require it strongly.\n    var rawExtentInfo = axisModel.axis.scale.rawExtentInfo;\n    if (percentWindow[0] !== 0) {\n      rawExtentInfo.setDeterminedMinMax('min', +valueWindow[0].toFixed(precision));\n    }\n    if (percentWindow[1] !== 100) {\n      rawExtentInfo.setDeterminedMinMax('max', +valueWindow[1].toFixed(precision));\n    }\n    rawExtentInfo.freeze();\n  };\n  return AxisProxy;\n}();\nfunction calculateDataExtent(axisProxy, axisDim, seriesModels) {\n  var dataExtent = [Infinity, -Infinity];\n  each(seriesModels, function (seriesModel) {\n    unionAxisExtentFromData(dataExtent, seriesModel.getData(), axisDim);\n  });\n  // It is important to get \"consistent\" extent when more then one axes is\n  // controlled by a `dataZoom`, otherwise those axes will not be synchronized\n  // when zooming. But it is difficult to know what is \"consistent\", considering\n  // axes have different type or even different meanings (For example, two\n  // time axes are used to compare data of the same date in different years).\n  // So basically dataZoom just obtains extent by series.data (in category axis\n  // extent can be obtained from axis.data).\n  // Nevertheless, user can set min/max/scale on axes to make extent of axes\n  // consistent.\n  var axisModel = axisProxy.getAxisModel();\n  var rawExtentResult = ensureScaleRawExtentInfo(axisModel.axis.scale, axisModel, dataExtent).calculate();\n  return [rawExtentResult.min, rawExtentResult.max];\n}\nexport default AxisProxy;", "map": {"version": 3, "names": ["zrUtil", "numberUtil", "slider<PERSON><PERSON>", "unionAxisExtentFromData", "ensureScaleRawExtentInfo", "getAxisMainType", "isCoordSupported", "SINGLE_REFERRING", "each", "asc", "AxisProxy", "dimName", "axisIndex", "dataZoomModel", "ecModel", "_dimName", "_axisIndex", "_dataZoomModel", "prototype", "hostedBy", "getDataValueWindow", "_valueWindow", "slice", "getDataPercentWindow", "_percentWindow", "getTargetSeriesModels", "seriesModels", "eachSeries", "seriesModel", "axisMainType", "axisModel", "getReferringComponents", "models", "componentIndex", "push", "getAxisModel", "getComponent", "getMinMaxSpan", "clone", "_minMaxSpan", "calculateDataWindow", "opt", "dataExtent", "_dataExtent", "scale", "axis", "rangePropMode", "getRangePropMode", "percentExtent", "percentWindow", "valueWindow", "hasPropModeValue", "prop", "idx", "boundPercent", "boundValue", "parse", "linearMap", "isNaN", "spans", "restrictSet", "fromWindow", "toWindow", "fromExtent", "toExtent", "toValue", "suffix", "i", "reset", "targetSeries", "calculateDataExtent", "_updateMinMaxSpan", "dataWindow", "settledOption", "_setAxisModel", "filterData", "api", "axisDim", "filterMode", "get", "seriesData", "getData", "dataDims", "mapDimensionsAll", "length", "store_1", "getStore", "dataDimIndices_1", "map", "dim", "getDimensionIndex", "filterSelf", "dataIndex", "leftOut", "rightOut", "hasValue", "value", "thisHasValue", "thisLeftOut", "thisRightOut", "setData", "isInWindow", "NaN", "range", "selectRange", "setApproximateExtent", "minMaxSpan", "minMax", "percentSpan", "valueSpan", "precision", "getPixelPrecision", "Math", "min", "rawExtentInfo", "setDeterminedMinMax", "toFixed", "freeze", "axisProxy", "Infinity", "rawExtentResult", "calculate", "max"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/dataZoom/AxisProxy.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { unionAxisExtentFromData } from '../../coord/axisHelper.js';\nimport { ensureScaleRawExtentInfo } from '../../coord/scaleRawExtentInfo.js';\nimport { getAxisMainType, isCoordSupported } from './helper.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\n/**\n * Operate single axis.\n * One axis can only operated by one axis operator.\n * Different dataZoomModels may be defined to operate the same axis.\n * (i.e. 'inside' data zoom and 'slider' data zoom components)\n * So dataZoomModels share one axisProxy in that case.\n */\nvar AxisProxy = /** @class */function () {\n  function AxisProxy(dimName, axisIndex, dataZoomModel, ecModel) {\n    this._dimName = dimName;\n    this._axisIndex = axisIndex;\n    this.ecModel = ecModel;\n    this._dataZoomModel = dataZoomModel;\n    // /**\n    //  * @readOnly\n    //  * @private\n    //  */\n    // this.hasSeriesStacked;\n  }\n  /**\n   * Whether the axisProxy is hosted by dataZoomModel.\n   */\n  AxisProxy.prototype.hostedBy = function (dataZoomModel) {\n    return this._dataZoomModel === dataZoomModel;\n  };\n  /**\n   * @return Value can only be NaN or finite value.\n   */\n  AxisProxy.prototype.getDataValueWindow = function () {\n    return this._valueWindow.slice();\n  };\n  /**\n   * @return {Array.<number>}\n   */\n  AxisProxy.prototype.getDataPercentWindow = function () {\n    return this._percentWindow.slice();\n  };\n  AxisProxy.prototype.getTargetSeriesModels = function () {\n    var seriesModels = [];\n    this.ecModel.eachSeries(function (seriesModel) {\n      if (isCoordSupported(seriesModel)) {\n        var axisMainType = getAxisMainType(this._dimName);\n        var axisModel = seriesModel.getReferringComponents(axisMainType, SINGLE_REFERRING).models[0];\n        if (axisModel && this._axisIndex === axisModel.componentIndex) {\n          seriesModels.push(seriesModel);\n        }\n      }\n    }, this);\n    return seriesModels;\n  };\n  AxisProxy.prototype.getAxisModel = function () {\n    return this.ecModel.getComponent(this._dimName + 'Axis', this._axisIndex);\n  };\n  AxisProxy.prototype.getMinMaxSpan = function () {\n    return zrUtil.clone(this._minMaxSpan);\n  };\n  /**\n   * Only calculate by given range and this._dataExtent, do not change anything.\n   */\n  AxisProxy.prototype.calculateDataWindow = function (opt) {\n    var dataExtent = this._dataExtent;\n    var axisModel = this.getAxisModel();\n    var scale = axisModel.axis.scale;\n    var rangePropMode = this._dataZoomModel.getRangePropMode();\n    var percentExtent = [0, 100];\n    var percentWindow = [];\n    var valueWindow = [];\n    var hasPropModeValue;\n    each(['start', 'end'], function (prop, idx) {\n      var boundPercent = opt[prop];\n      var boundValue = opt[prop + 'Value'];\n      // Notice: dataZoom is based either on `percentProp` ('start', 'end') or\n      // on `valueProp` ('startValue', 'endValue'). (They are based on the data extent\n      // but not min/max of axis, which will be calculated by data window then).\n      // The former one is suitable for cases that a dataZoom component controls multiple\n      // axes with different unit or extent, and the latter one is suitable for accurate\n      // zoom by pixel (e.g., in dataZoomSelect).\n      // we use `getRangePropMode()` to mark which prop is used. `rangePropMode` is updated\n      // only when setOption or dispatchAction, otherwise it remains its original value.\n      // (Why not only record `percentProp` and always map to `valueProp`? Because\n      // the map `valueProp` -> `percentProp` -> `valueProp` probably not the original\n      // `valueProp`. consider two axes constrolled by one dataZoom. They have different\n      // data extent. All of values that are overflow the `dataExtent` will be calculated\n      // to percent '100%').\n      if (rangePropMode[idx] === 'percent') {\n        boundPercent == null && (boundPercent = percentExtent[idx]);\n        // Use scale.parse to math round for category or time axis.\n        boundValue = scale.parse(numberUtil.linearMap(boundPercent, percentExtent, dataExtent));\n      } else {\n        hasPropModeValue = true;\n        boundValue = boundValue == null ? dataExtent[idx] : scale.parse(boundValue);\n        // Calculating `percent` from `value` may be not accurate, because\n        // This calculation can not be inversed, because all of values that\n        // are overflow the `dataExtent` will be calculated to percent '100%'\n        boundPercent = numberUtil.linearMap(boundValue, dataExtent, percentExtent);\n      }\n      // valueWindow[idx] = round(boundValue);\n      // percentWindow[idx] = round(boundPercent);\n      // fallback to extent start/end when parsed value or percent is invalid\n      valueWindow[idx] = boundValue == null || isNaN(boundValue) ? dataExtent[idx] : boundValue;\n      percentWindow[idx] = boundPercent == null || isNaN(boundPercent) ? percentExtent[idx] : boundPercent;\n    });\n    asc(valueWindow);\n    asc(percentWindow);\n    // The windows from user calling of `dispatchAction` might be out of the extent,\n    // or do not obey the `min/maxSpan`, `min/maxValueSpan`. But we don't restrict window\n    // by `zoomLock` here, because we see `zoomLock` just as a interaction constraint,\n    // where API is able to initialize/modify the window size even though `zoomLock`\n    // specified.\n    var spans = this._minMaxSpan;\n    hasPropModeValue ? restrictSet(valueWindow, percentWindow, dataExtent, percentExtent, false) : restrictSet(percentWindow, valueWindow, percentExtent, dataExtent, true);\n    function restrictSet(fromWindow, toWindow, fromExtent, toExtent, toValue) {\n      var suffix = toValue ? 'Span' : 'ValueSpan';\n      sliderMove(0, fromWindow, fromExtent, 'all', spans['min' + suffix], spans['max' + suffix]);\n      for (var i = 0; i < 2; i++) {\n        toWindow[i] = numberUtil.linearMap(fromWindow[i], fromExtent, toExtent, true);\n        toValue && (toWindow[i] = scale.parse(toWindow[i]));\n      }\n    }\n    return {\n      valueWindow: valueWindow,\n      percentWindow: percentWindow\n    };\n  };\n  /**\n   * Notice: reset should not be called before series.restoreData() is called,\n   * so it is recommended to be called in \"process stage\" but not \"model init\n   * stage\".\n   */\n  AxisProxy.prototype.reset = function (dataZoomModel) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var targetSeries = this.getTargetSeriesModels();\n    // Culculate data window and data extent, and record them.\n    this._dataExtent = calculateDataExtent(this, this._dimName, targetSeries);\n    // `calculateDataWindow` uses min/maxSpan.\n    this._updateMinMaxSpan();\n    var dataWindow = this.calculateDataWindow(dataZoomModel.settledOption);\n    this._valueWindow = dataWindow.valueWindow;\n    this._percentWindow = dataWindow.percentWindow;\n    // Update axis setting then.\n    this._setAxisModel();\n  };\n  AxisProxy.prototype.filterData = function (dataZoomModel, api) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var axisDim = this._dimName;\n    var seriesModels = this.getTargetSeriesModels();\n    var filterMode = dataZoomModel.get('filterMode');\n    var valueWindow = this._valueWindow;\n    if (filterMode === 'none') {\n      return;\n    }\n    // FIXME\n    // Toolbox may has dataZoom injected. And if there are stacked bar chart\n    // with NaN data, NaN will be filtered and stack will be wrong.\n    // So we need to force the mode to be set empty.\n    // In fect, it is not a big deal that do not support filterMode-'filter'\n    // when using toolbox#dataZoom, utill tooltip#dataZoom support \"single axis\n    // selection\" some day, which might need \"adapt to data extent on the\n    // otherAxis\", which is disabled by filterMode-'empty'.\n    // But currently, stack has been fixed to based on value but not index,\n    // so this is not an issue any more.\n    // let otherAxisModel = this.getOtherAxisModel();\n    // if (dataZoomModel.get('$fromToolbox')\n    //     && otherAxisModel\n    //     && otherAxisModel.hasSeriesStacked\n    // ) {\n    //     filterMode = 'empty';\n    // }\n    // TODO\n    // filterMode 'weakFilter' and 'empty' is not optimized for huge data yet.\n    each(seriesModels, function (seriesModel) {\n      var seriesData = seriesModel.getData();\n      var dataDims = seriesData.mapDimensionsAll(axisDim);\n      if (!dataDims.length) {\n        return;\n      }\n      if (filterMode === 'weakFilter') {\n        var store_1 = seriesData.getStore();\n        var dataDimIndices_1 = zrUtil.map(dataDims, function (dim) {\n          return seriesData.getDimensionIndex(dim);\n        }, seriesData);\n        seriesData.filterSelf(function (dataIndex) {\n          var leftOut;\n          var rightOut;\n          var hasValue;\n          for (var i = 0; i < dataDims.length; i++) {\n            var value = store_1.get(dataDimIndices_1[i], dataIndex);\n            var thisHasValue = !isNaN(value);\n            var thisLeftOut = value < valueWindow[0];\n            var thisRightOut = value > valueWindow[1];\n            if (thisHasValue && !thisLeftOut && !thisRightOut) {\n              return true;\n            }\n            thisHasValue && (hasValue = true);\n            thisLeftOut && (leftOut = true);\n            thisRightOut && (rightOut = true);\n          }\n          // If both left out and right out, do not filter.\n          return hasValue && leftOut && rightOut;\n        });\n      } else {\n        each(dataDims, function (dim) {\n          if (filterMode === 'empty') {\n            seriesModel.setData(seriesData = seriesData.map(dim, function (value) {\n              return !isInWindow(value) ? NaN : value;\n            }));\n          } else {\n            var range = {};\n            range[dim] = valueWindow;\n            // console.time('select');\n            seriesData.selectRange(range);\n            // console.timeEnd('select');\n          }\n        });\n      }\n      each(dataDims, function (dim) {\n        seriesData.setApproximateExtent(valueWindow, dim);\n      });\n    });\n    function isInWindow(value) {\n      return value >= valueWindow[0] && value <= valueWindow[1];\n    }\n  };\n  AxisProxy.prototype._updateMinMaxSpan = function () {\n    var minMaxSpan = this._minMaxSpan = {};\n    var dataZoomModel = this._dataZoomModel;\n    var dataExtent = this._dataExtent;\n    each(['min', 'max'], function (minMax) {\n      var percentSpan = dataZoomModel.get(minMax + 'Span');\n      var valueSpan = dataZoomModel.get(minMax + 'ValueSpan');\n      valueSpan != null && (valueSpan = this.getAxisModel().axis.scale.parse(valueSpan));\n      // minValueSpan and maxValueSpan has higher priority than minSpan and maxSpan\n      if (valueSpan != null) {\n        percentSpan = numberUtil.linearMap(dataExtent[0] + valueSpan, dataExtent, [0, 100], true);\n      } else if (percentSpan != null) {\n        valueSpan = numberUtil.linearMap(percentSpan, [0, 100], dataExtent, true) - dataExtent[0];\n      }\n      minMaxSpan[minMax + 'Span'] = percentSpan;\n      minMaxSpan[minMax + 'ValueSpan'] = valueSpan;\n    }, this);\n  };\n  AxisProxy.prototype._setAxisModel = function () {\n    var axisModel = this.getAxisModel();\n    var percentWindow = this._percentWindow;\n    var valueWindow = this._valueWindow;\n    if (!percentWindow) {\n      return;\n    }\n    // [0, 500]: arbitrary value, guess axis extent.\n    var precision = numberUtil.getPixelPrecision(valueWindow, [0, 500]);\n    precision = Math.min(precision, 20);\n    // For value axis, if min/max/scale are not set, we just use the extent obtained\n    // by series data, which may be a little different from the extent calculated by\n    // `axisHelper.getScaleExtent`. But the different just affects the experience a\n    // little when zooming. So it will not be fixed until some users require it strongly.\n    var rawExtentInfo = axisModel.axis.scale.rawExtentInfo;\n    if (percentWindow[0] !== 0) {\n      rawExtentInfo.setDeterminedMinMax('min', +valueWindow[0].toFixed(precision));\n    }\n    if (percentWindow[1] !== 100) {\n      rawExtentInfo.setDeterminedMinMax('max', +valueWindow[1].toFixed(precision));\n    }\n    rawExtentInfo.freeze();\n  };\n  return AxisProxy;\n}();\nfunction calculateDataExtent(axisProxy, axisDim, seriesModels) {\n  var dataExtent = [Infinity, -Infinity];\n  each(seriesModels, function (seriesModel) {\n    unionAxisExtentFromData(dataExtent, seriesModel.getData(), axisDim);\n  });\n  // It is important to get \"consistent\" extent when more then one axes is\n  // controlled by a `dataZoom`, otherwise those axes will not be synchronized\n  // when zooming. But it is difficult to know what is \"consistent\", considering\n  // axes have different type or even different meanings (For example, two\n  // time axes are used to compare data of the same date in different years).\n  // So basically dataZoom just obtains extent by series.data (in category axis\n  // extent can be obtained from axis.data).\n  // Nevertheless, user can set min/max/scale on axes to make extent of axes\n  // consistent.\n  var axisModel = axisProxy.getAxisModel();\n  var rawExtentResult = ensureScaleRawExtentInfo(axisModel.axis.scale, axisModel, dataExtent).calculate();\n  return [rawExtentResult.min, rawExtentResult.max];\n}\nexport default AxisProxy;"], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,aAAa;AAC/D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI;AACtB,IAAIC,GAAG,GAAGR,UAAU,CAACQ,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAACC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACC,QAAQ,GAAGJ,OAAO;IACvB,IAAI,CAACK,UAAU,GAAGJ,SAAS;IAC3B,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,cAAc,GAAGJ,aAAa;IACnC;IACA;IACA;IACA;IACA;EACF;EACA;AACF;AACA;EACEH,SAAS,CAACQ,SAAS,CAACC,QAAQ,GAAG,UAAUN,aAAa,EAAE;IACtD,OAAO,IAAI,CAACI,cAAc,KAAKJ,aAAa;EAC9C,CAAC;EACD;AACF;AACA;EACEH,SAAS,CAACQ,SAAS,CAACE,kBAAkB,GAAG,YAAY;IACnD,OAAO,IAAI,CAACC,YAAY,CAACC,KAAK,CAAC,CAAC;EAClC,CAAC;EACD;AACF;AACA;EACEZ,SAAS,CAACQ,SAAS,CAACK,oBAAoB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACC,cAAc,CAACF,KAAK,CAAC,CAAC;EACpC,CAAC;EACDZ,SAAS,CAACQ,SAAS,CAACO,qBAAqB,GAAG,YAAY;IACtD,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAI,CAACZ,OAAO,CAACa,UAAU,CAAC,UAAUC,WAAW,EAAE;MAC7C,IAAItB,gBAAgB,CAACsB,WAAW,CAAC,EAAE;QACjC,IAAIC,YAAY,GAAGxB,eAAe,CAAC,IAAI,CAACU,QAAQ,CAAC;QACjD,IAAIe,SAAS,GAAGF,WAAW,CAACG,sBAAsB,CAACF,YAAY,EAAEtB,gBAAgB,CAAC,CAACyB,MAAM,CAAC,CAAC,CAAC;QAC5F,IAAIF,SAAS,IAAI,IAAI,CAACd,UAAU,KAAKc,SAAS,CAACG,cAAc,EAAE;UAC7DP,YAAY,CAACQ,IAAI,CAACN,WAAW,CAAC;QAChC;MACF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAOF,YAAY;EACrB,CAAC;EACDhB,SAAS,CAACQ,SAAS,CAACiB,YAAY,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACrB,OAAO,CAACsB,YAAY,CAAC,IAAI,CAACrB,QAAQ,GAAG,MAAM,EAAE,IAAI,CAACC,UAAU,CAAC;EAC3E,CAAC;EACDN,SAAS,CAACQ,SAAS,CAACmB,aAAa,GAAG,YAAY;IAC9C,OAAOrC,MAAM,CAACsC,KAAK,CAAC,IAAI,CAACC,WAAW,CAAC;EACvC,CAAC;EACD;AACF;AACA;EACE7B,SAAS,CAACQ,SAAS,CAACsB,mBAAmB,GAAG,UAAUC,GAAG,EAAE;IACvD,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAIb,SAAS,GAAG,IAAI,CAACK,YAAY,CAAC,CAAC;IACnC,IAAIS,KAAK,GAAGd,SAAS,CAACe,IAAI,CAACD,KAAK;IAChC,IAAIE,aAAa,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,gBAAgB,CAAC,CAAC;IAC1D,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5B,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,gBAAgB;IACpB3C,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,UAAU4C,IAAI,EAAEC,GAAG,EAAE;MAC1C,IAAIC,YAAY,GAAGb,GAAG,CAACW,IAAI,CAAC;MAC5B,IAAIG,UAAU,GAAGd,GAAG,CAACW,IAAI,GAAG,OAAO,CAAC;MACpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIN,aAAa,CAACO,GAAG,CAAC,KAAK,SAAS,EAAE;QACpCC,YAAY,IAAI,IAAI,KAAKA,YAAY,GAAGN,aAAa,CAACK,GAAG,CAAC,CAAC;QAC3D;QACAE,UAAU,GAAGX,KAAK,CAACY,KAAK,CAACvD,UAAU,CAACwD,SAAS,CAACH,YAAY,EAAEN,aAAa,EAAEN,UAAU,CAAC,CAAC;MACzF,CAAC,MAAM;QACLS,gBAAgB,GAAG,IAAI;QACvBI,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGb,UAAU,CAACW,GAAG,CAAC,GAAGT,KAAK,CAACY,KAAK,CAACD,UAAU,CAAC;QAC3E;QACA;QACA;QACAD,YAAY,GAAGrD,UAAU,CAACwD,SAAS,CAACF,UAAU,EAAEb,UAAU,EAAEM,aAAa,CAAC;MAC5E;MACA;MACA;MACA;MACAE,WAAW,CAACG,GAAG,CAAC,GAAGE,UAAU,IAAI,IAAI,IAAIG,KAAK,CAACH,UAAU,CAAC,GAAGb,UAAU,CAACW,GAAG,CAAC,GAAGE,UAAU;MACzFN,aAAa,CAACI,GAAG,CAAC,GAAGC,YAAY,IAAI,IAAI,IAAII,KAAK,CAACJ,YAAY,CAAC,GAAGN,aAAa,CAACK,GAAG,CAAC,GAAGC,YAAY;IACtG,CAAC,CAAC;IACF7C,GAAG,CAACyC,WAAW,CAAC;IAChBzC,GAAG,CAACwC,aAAa,CAAC;IAClB;IACA;IACA;IACA;IACA;IACA,IAAIU,KAAK,GAAG,IAAI,CAACpB,WAAW;IAC5BY,gBAAgB,GAAGS,WAAW,CAACV,WAAW,EAAED,aAAa,EAAEP,UAAU,EAAEM,aAAa,EAAE,KAAK,CAAC,GAAGY,WAAW,CAACX,aAAa,EAAEC,WAAW,EAAEF,aAAa,EAAEN,UAAU,EAAE,IAAI,CAAC;IACvK,SAASkB,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MACxE,IAAIC,MAAM,GAAGD,OAAO,GAAG,MAAM,GAAG,WAAW;MAC3C/D,UAAU,CAAC,CAAC,EAAE2D,UAAU,EAAEE,UAAU,EAAE,KAAK,EAAEJ,KAAK,CAAC,KAAK,GAAGO,MAAM,CAAC,EAAEP,KAAK,CAAC,KAAK,GAAGO,MAAM,CAAC,CAAC;MAC1F,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1BL,QAAQ,CAACK,CAAC,CAAC,GAAGlE,UAAU,CAACwD,SAAS,CAACI,UAAU,CAACM,CAAC,CAAC,EAAEJ,UAAU,EAAEC,QAAQ,EAAE,IAAI,CAAC;QAC7EC,OAAO,KAAKH,QAAQ,CAACK,CAAC,CAAC,GAAGvB,KAAK,CAACY,KAAK,CAACM,QAAQ,CAACK,CAAC,CAAC,CAAC,CAAC;MACrD;IACF;IACA,OAAO;MACLjB,WAAW,EAAEA,WAAW;MACxBD,aAAa,EAAEA;IACjB,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;EACEvC,SAAS,CAACQ,SAAS,CAACkD,KAAK,GAAG,UAAUvD,aAAa,EAAE;IACnD,IAAIA,aAAa,KAAK,IAAI,CAACI,cAAc,EAAE;MACzC;IACF;IACA,IAAIoD,YAAY,GAAG,IAAI,CAAC5C,qBAAqB,CAAC,CAAC;IAC/C;IACA,IAAI,CAACkB,WAAW,GAAG2B,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACvD,QAAQ,EAAEsD,YAAY,CAAC;IACzE;IACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IACxB,IAAIC,UAAU,GAAG,IAAI,CAAChC,mBAAmB,CAAC3B,aAAa,CAAC4D,aAAa,CAAC;IACtE,IAAI,CAACpD,YAAY,GAAGmD,UAAU,CAACtB,WAAW;IAC1C,IAAI,CAAC1B,cAAc,GAAGgD,UAAU,CAACvB,aAAa;IAC9C;IACA,IAAI,CAACyB,aAAa,CAAC,CAAC;EACtB,CAAC;EACDhE,SAAS,CAACQ,SAAS,CAACyD,UAAU,GAAG,UAAU9D,aAAa,EAAE+D,GAAG,EAAE;IAC7D,IAAI/D,aAAa,KAAK,IAAI,CAACI,cAAc,EAAE;MACzC;IACF;IACA,IAAI4D,OAAO,GAAG,IAAI,CAAC9D,QAAQ;IAC3B,IAAIW,YAAY,GAAG,IAAI,CAACD,qBAAqB,CAAC,CAAC;IAC/C,IAAIqD,UAAU,GAAGjE,aAAa,CAACkE,GAAG,CAAC,YAAY,CAAC;IAChD,IAAI7B,WAAW,GAAG,IAAI,CAAC7B,YAAY;IACnC,IAAIyD,UAAU,KAAK,MAAM,EAAE;MACzB;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAtE,IAAI,CAACkB,YAAY,EAAE,UAAUE,WAAW,EAAE;MACxC,IAAIoD,UAAU,GAAGpD,WAAW,CAACqD,OAAO,CAAC,CAAC;MACtC,IAAIC,QAAQ,GAAGF,UAAU,CAACG,gBAAgB,CAACN,OAAO,CAAC;MACnD,IAAI,CAACK,QAAQ,CAACE,MAAM,EAAE;QACpB;MACF;MACA,IAAIN,UAAU,KAAK,YAAY,EAAE;QAC/B,IAAIO,OAAO,GAAGL,UAAU,CAACM,QAAQ,CAAC,CAAC;QACnC,IAAIC,gBAAgB,GAAGvF,MAAM,CAACwF,GAAG,CAACN,QAAQ,EAAE,UAAUO,GAAG,EAAE;UACzD,OAAOT,UAAU,CAACU,iBAAiB,CAACD,GAAG,CAAC;QAC1C,CAAC,EAAET,UAAU,CAAC;QACdA,UAAU,CAACW,UAAU,CAAC,UAAUC,SAAS,EAAE;UACzC,IAAIC,OAAO;UACX,IAAIC,QAAQ;UACZ,IAAIC,QAAQ;UACZ,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,QAAQ,CAACE,MAAM,EAAEjB,CAAC,EAAE,EAAE;YACxC,IAAI6B,KAAK,GAAGX,OAAO,CAACN,GAAG,CAACQ,gBAAgB,CAACpB,CAAC,CAAC,EAAEyB,SAAS,CAAC;YACvD,IAAIK,YAAY,GAAG,CAACvC,KAAK,CAACsC,KAAK,CAAC;YAChC,IAAIE,WAAW,GAAGF,KAAK,GAAG9C,WAAW,CAAC,CAAC,CAAC;YACxC,IAAIiD,YAAY,GAAGH,KAAK,GAAG9C,WAAW,CAAC,CAAC,CAAC;YACzC,IAAI+C,YAAY,IAAI,CAACC,WAAW,IAAI,CAACC,YAAY,EAAE;cACjD,OAAO,IAAI;YACb;YACAF,YAAY,KAAKF,QAAQ,GAAG,IAAI,CAAC;YACjCG,WAAW,KAAKL,OAAO,GAAG,IAAI,CAAC;YAC/BM,YAAY,KAAKL,QAAQ,GAAG,IAAI,CAAC;UACnC;UACA;UACA,OAAOC,QAAQ,IAAIF,OAAO,IAAIC,QAAQ;QACxC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtF,IAAI,CAAC0E,QAAQ,EAAE,UAAUO,GAAG,EAAE;UAC5B,IAAIX,UAAU,KAAK,OAAO,EAAE;YAC1BlD,WAAW,CAACwE,OAAO,CAACpB,UAAU,GAAGA,UAAU,CAACQ,GAAG,CAACC,GAAG,EAAE,UAAUO,KAAK,EAAE;cACpE,OAAO,CAACK,UAAU,CAACL,KAAK,CAAC,GAAGM,GAAG,GAAGN,KAAK;YACzC,CAAC,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAIO,KAAK,GAAG,CAAC,CAAC;YACdA,KAAK,CAACd,GAAG,CAAC,GAAGvC,WAAW;YACxB;YACA8B,UAAU,CAACwB,WAAW,CAACD,KAAK,CAAC;YAC7B;UACF;QACF,CAAC,CAAC;MACJ;MACA/F,IAAI,CAAC0E,QAAQ,EAAE,UAAUO,GAAG,EAAE;QAC5BT,UAAU,CAACyB,oBAAoB,CAACvD,WAAW,EAAEuC,GAAG,CAAC;MACnD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,SAASY,UAAUA,CAACL,KAAK,EAAE;MACzB,OAAOA,KAAK,IAAI9C,WAAW,CAAC,CAAC,CAAC,IAAI8C,KAAK,IAAI9C,WAAW,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC;EACDxC,SAAS,CAACQ,SAAS,CAACqD,iBAAiB,GAAG,YAAY;IAClD,IAAImC,UAAU,GAAG,IAAI,CAACnE,WAAW,GAAG,CAAC,CAAC;IACtC,IAAI1B,aAAa,GAAG,IAAI,CAACI,cAAc;IACvC,IAAIyB,UAAU,GAAG,IAAI,CAACC,WAAW;IACjCnC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,UAAUmG,MAAM,EAAE;MACrC,IAAIC,WAAW,GAAG/F,aAAa,CAACkE,GAAG,CAAC4B,MAAM,GAAG,MAAM,CAAC;MACpD,IAAIE,SAAS,GAAGhG,aAAa,CAACkE,GAAG,CAAC4B,MAAM,GAAG,WAAW,CAAC;MACvDE,SAAS,IAAI,IAAI,KAAKA,SAAS,GAAG,IAAI,CAAC1E,YAAY,CAAC,CAAC,CAACU,IAAI,CAACD,KAAK,CAACY,KAAK,CAACqD,SAAS,CAAC,CAAC;MAClF;MACA,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrBD,WAAW,GAAG3G,UAAU,CAACwD,SAAS,CAACf,UAAU,CAAC,CAAC,CAAC,GAAGmE,SAAS,EAAEnE,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;MAC3F,CAAC,MAAM,IAAIkE,WAAW,IAAI,IAAI,EAAE;QAC9BC,SAAS,GAAG5G,UAAU,CAACwD,SAAS,CAACmD,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAElE,UAAU,EAAE,IAAI,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;MAC3F;MACAgE,UAAU,CAACC,MAAM,GAAG,MAAM,CAAC,GAAGC,WAAW;MACzCF,UAAU,CAACC,MAAM,GAAG,WAAW,CAAC,GAAGE,SAAS;IAC9C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDnG,SAAS,CAACQ,SAAS,CAACwD,aAAa,GAAG,YAAY;IAC9C,IAAI5C,SAAS,GAAG,IAAI,CAACK,YAAY,CAAC,CAAC;IACnC,IAAIc,aAAa,GAAG,IAAI,CAACzB,cAAc;IACvC,IAAI0B,WAAW,GAAG,IAAI,CAAC7B,YAAY;IACnC,IAAI,CAAC4B,aAAa,EAAE;MAClB;IACF;IACA;IACA,IAAI6D,SAAS,GAAG7G,UAAU,CAAC8G,iBAAiB,CAAC7D,WAAW,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACnE4D,SAAS,GAAGE,IAAI,CAACC,GAAG,CAACH,SAAS,EAAE,EAAE,CAAC;IACnC;IACA;IACA;IACA;IACA,IAAII,aAAa,GAAGpF,SAAS,CAACe,IAAI,CAACD,KAAK,CAACsE,aAAa;IACtD,IAAIjE,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MAC1BiE,aAAa,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAACjE,WAAW,CAAC,CAAC,CAAC,CAACkE,OAAO,CAACN,SAAS,CAAC,CAAC;IAC9E;IACA,IAAI7D,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5BiE,aAAa,CAACC,mBAAmB,CAAC,KAAK,EAAE,CAACjE,WAAW,CAAC,CAAC,CAAC,CAACkE,OAAO,CAACN,SAAS,CAAC,CAAC;IAC9E;IACAI,aAAa,CAACG,MAAM,CAAC,CAAC;EACxB,CAAC;EACD,OAAO3G,SAAS;AAClB,CAAC,CAAC,CAAC;AACH,SAAS4D,mBAAmBA,CAACgD,SAAS,EAAEzC,OAAO,EAAEnD,YAAY,EAAE;EAC7D,IAAIgB,UAAU,GAAG,CAAC6E,QAAQ,EAAE,CAACA,QAAQ,CAAC;EACtC/G,IAAI,CAACkB,YAAY,EAAE,UAAUE,WAAW,EAAE;IACxCzB,uBAAuB,CAACuC,UAAU,EAAEd,WAAW,CAACqD,OAAO,CAAC,CAAC,EAAEJ,OAAO,CAAC;EACrE,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI/C,SAAS,GAAGwF,SAAS,CAACnF,YAAY,CAAC,CAAC;EACxC,IAAIqF,eAAe,GAAGpH,wBAAwB,CAAC0B,SAAS,CAACe,IAAI,CAACD,KAAK,EAAEd,SAAS,EAAEY,UAAU,CAAC,CAAC+E,SAAS,CAAC,CAAC;EACvG,OAAO,CAACD,eAAe,CAACP,GAAG,EAAEO,eAAe,CAACE,GAAG,CAAC;AACnD;AACA,eAAehH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}