{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  /**\n   * PENDING - Regarding forcibly converting to string:\n   *  In the early days, the underlying hash map impl used JS plain object and converted the key to\n   *  string; later in https://github.com/ecomfe/zrender/pull/966 it was changed to a JS Map (in supported\n   *  platforms), which does not require string keys. But consider any input that `scale/Ordinal['parse']`\n   *  is involved, a number input represents an `OrdinalNumber` (i.e., an index), and affect the query\n   *  behavior:\n   *    - If forcbily converting to string:\n   *      pros: users can use numeric string (such as, '123') to query the raw data (123), tho it's probably\n   *      still confusing.\n   *      cons: NaN/null/undefined in data will be equals to 'NaN'/'null'/'undefined', if simply using\n   *      `val + ''` to convert them, like currently `getName` does.\n   *    - Otherwise:\n   *      pros: see NaN/null/undefined case above.\n   *      cons: users cannot query the raw data (123) any more.\n   *  There are two inconsistent behaviors in the current impl:\n   *    - Force conversion is applied on the case `xAxis{data: ['aaa', 'bbb', ...]}`,\n   *      but no conversion applied to the case `xAxis{data: [{value: 'aaa'}, ...]}` and\n   *      the case `dataset: {source: [['aaa', 123], ['bbb', 234], ...]}`.\n   *    - behaves differently according to whether JS Map is supported (the polyfill is simply using JS\n   *      plain object) (tho it seems rare platform that do not support it).\n   *  Since there's no sufficient good solution to offset cost of the breaking change, we preserve the\n   *  current behavior, until real issues is reported.\n   */\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n    this._onCollect = opt.onCollect;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    return this._getOrCreateMap().get(category);\n  };\n  /**\n   * @return The ordinal. If not found, return NaN.\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      this._onCollect && this._onCollect(category, index);\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        map.set(category, index);\n        this._onCollect && this._onCollect(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;", "map": {"version": 3, "names": ["createHashMap", "isObject", "map", "isString", "uidBase", "OrdinalMeta", "opt", "categories", "_needCollect", "needCollect", "_deduplication", "deduplication", "uid", "_onCollect", "onCollect", "createByAxisModel", "axisModel", "option", "data", "getName", "dedplication", "prototype", "getOrdinal", "category", "_getOrCreateMap", "get", "parseAndCollect", "index", "length", "set", "NaN", "_map", "obj", "value"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/data/OrdinalMeta.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, isObject, map, isString } from 'zrender/lib/core/util.js';\nvar uidBase = 0;\nvar OrdinalMeta = /** @class */function () {\n  /**\n   * PENDING - Regarding forcibly converting to string:\n   *  In the early days, the underlying hash map impl used JS plain object and converted the key to\n   *  string; later in https://github.com/ecomfe/zrender/pull/966 it was changed to a JS Map (in supported\n   *  platforms), which does not require string keys. But consider any input that `scale/Ordinal['parse']`\n   *  is involved, a number input represents an `OrdinalNumber` (i.e., an index), and affect the query\n   *  behavior:\n   *    - If forcbily converting to string:\n   *      pros: users can use numeric string (such as, '123') to query the raw data (123), tho it's probably\n   *      still confusing.\n   *      cons: NaN/null/undefined in data will be equals to 'NaN'/'null'/'undefined', if simply using\n   *      `val + ''` to convert them, like currently `getName` does.\n   *    - Otherwise:\n   *      pros: see NaN/null/undefined case above.\n   *      cons: users cannot query the raw data (123) any more.\n   *  There are two inconsistent behaviors in the current impl:\n   *    - Force conversion is applied on the case `xAxis{data: ['aaa', 'bbb', ...]}`,\n   *      but no conversion applied to the case `xAxis{data: [{value: 'aaa'}, ...]}` and\n   *      the case `dataset: {source: [['aaa', 123], ['bbb', 234], ...]}`.\n   *    - behaves differently according to whether JS Map is supported (the polyfill is simply using JS\n   *      plain object) (tho it seems rare platform that do not support it).\n   *  Since there's no sufficient good solution to offset cost of the breaking change, we preserve the\n   *  current behavior, until real issues is reported.\n   */\n  function OrdinalMeta(opt) {\n    this.categories = opt.categories || [];\n    this._needCollect = opt.needCollect;\n    this._deduplication = opt.deduplication;\n    this.uid = ++uidBase;\n    this._onCollect = opt.onCollect;\n  }\n  OrdinalMeta.createByAxisModel = function (axisModel) {\n    var option = axisModel.option;\n    var data = option.data;\n    var categories = data && map(data, getName);\n    return new OrdinalMeta({\n      categories: categories,\n      needCollect: !categories,\n      // deduplication is default in axis.\n      deduplication: option.dedplication !== false\n    });\n  };\n  ;\n  OrdinalMeta.prototype.getOrdinal = function (category) {\n    return this._getOrCreateMap().get(category);\n  };\n  /**\n   * @return The ordinal. If not found, return NaN.\n   */\n  OrdinalMeta.prototype.parseAndCollect = function (category) {\n    var index;\n    var needCollect = this._needCollect;\n    // The value of category dim can be the index of the given category set.\n    // This feature is only supported when !needCollect, because we should\n    // consider a common case: a value is 2017, which is a number but is\n    // expected to be tread as a category. This case usually happen in dataset,\n    // where it happent to be no need of the index feature.\n    if (!isString(category) && !needCollect) {\n      return category;\n    }\n    // Optimize for the scenario:\n    // category is ['2012-01-01', '2012-01-02', ...], where the input\n    // data has been ensured not duplicate and is large data.\n    // Notice, if a dataset dimension provide categroies, usually echarts\n    // should remove duplication except user tell echarts dont do that\n    // (set axis.deduplication = false), because echarts do not know whether\n    // the values in the category dimension has duplication (consider the\n    // parallel-aqi example)\n    if (needCollect && !this._deduplication) {\n      index = this.categories.length;\n      this.categories[index] = category;\n      this._onCollect && this._onCollect(category, index);\n      return index;\n    }\n    var map = this._getOrCreateMap();\n    index = map.get(category);\n    if (index == null) {\n      if (needCollect) {\n        index = this.categories.length;\n        this.categories[index] = category;\n        map.set(category, index);\n        this._onCollect && this._onCollect(category, index);\n      } else {\n        index = NaN;\n      }\n    }\n    return index;\n  };\n  // Consider big data, do not create map until needed.\n  OrdinalMeta.prototype._getOrCreateMap = function () {\n    return this._map || (this._map = createHashMap(this.categories));\n  };\n  return OrdinalMeta;\n}();\nfunction getName(obj) {\n  if (isObject(obj) && obj.value != null) {\n    return obj.value;\n  } else {\n    return obj + '';\n  }\n}\nexport default OrdinalMeta;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,0BAA0B;AACjF,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,WAAW,GAAG,aAAa,YAAY;EACzC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,WAAWA,CAACC,GAAG,EAAE;IACxB,IAAI,CAACC,UAAU,GAAGD,GAAG,CAACC,UAAU,IAAI,EAAE;IACtC,IAAI,CAACC,YAAY,GAAGF,GAAG,CAACG,WAAW;IACnC,IAAI,CAACC,cAAc,GAAGJ,GAAG,CAACK,aAAa;IACvC,IAAI,CAACC,GAAG,GAAG,EAAER,OAAO;IACpB,IAAI,CAACS,UAAU,GAAGP,GAAG,CAACQ,SAAS;EACjC;EACAT,WAAW,CAACU,iBAAiB,GAAG,UAAUC,SAAS,EAAE;IACnD,IAAIC,MAAM,GAAGD,SAAS,CAACC,MAAM;IAC7B,IAAIC,IAAI,GAAGD,MAAM,CAACC,IAAI;IACtB,IAAIX,UAAU,GAAGW,IAAI,IAAIhB,GAAG,CAACgB,IAAI,EAAEC,OAAO,CAAC;IAC3C,OAAO,IAAId,WAAW,CAAC;MACrBE,UAAU,EAAEA,UAAU;MACtBE,WAAW,EAAE,CAACF,UAAU;MACxB;MACAI,aAAa,EAAEM,MAAM,CAACG,YAAY,KAAK;IACzC,CAAC,CAAC;EACJ,CAAC;EACD;EACAf,WAAW,CAACgB,SAAS,CAACC,UAAU,GAAG,UAAUC,QAAQ,EAAE;IACrD,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC,CAACC,GAAG,CAACF,QAAQ,CAAC;EAC7C,CAAC;EACD;AACF;AACA;EACElB,WAAW,CAACgB,SAAS,CAACK,eAAe,GAAG,UAAUH,QAAQ,EAAE;IAC1D,IAAII,KAAK;IACT,IAAIlB,WAAW,GAAG,IAAI,CAACD,YAAY;IACnC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACL,QAAQ,CAACoB,QAAQ,CAAC,IAAI,CAACd,WAAW,EAAE;MACvC,OAAOc,QAAQ;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAId,WAAW,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACvCiB,KAAK,GAAG,IAAI,CAACpB,UAAU,CAACqB,MAAM;MAC9B,IAAI,CAACrB,UAAU,CAACoB,KAAK,CAAC,GAAGJ,QAAQ;MACjC,IAAI,CAACV,UAAU,IAAI,IAAI,CAACA,UAAU,CAACU,QAAQ,EAAEI,KAAK,CAAC;MACnD,OAAOA,KAAK;IACd;IACA,IAAIzB,GAAG,GAAG,IAAI,CAACsB,eAAe,CAAC,CAAC;IAChCG,KAAK,GAAGzB,GAAG,CAACuB,GAAG,CAACF,QAAQ,CAAC;IACzB,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIlB,WAAW,EAAE;QACfkB,KAAK,GAAG,IAAI,CAACpB,UAAU,CAACqB,MAAM;QAC9B,IAAI,CAACrB,UAAU,CAACoB,KAAK,CAAC,GAAGJ,QAAQ;QACjCrB,GAAG,CAAC2B,GAAG,CAACN,QAAQ,EAAEI,KAAK,CAAC;QACxB,IAAI,CAACd,UAAU,IAAI,IAAI,CAACA,UAAU,CAACU,QAAQ,EAAEI,KAAK,CAAC;MACrD,CAAC,MAAM;QACLA,KAAK,GAAGG,GAAG;MACb;IACF;IACA,OAAOH,KAAK;EACd,CAAC;EACD;EACAtB,WAAW,CAACgB,SAAS,CAACG,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACO,IAAI,KAAK,IAAI,CAACA,IAAI,GAAG/B,aAAa,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC;EAClE,CAAC;EACD,OAAOF,WAAW;AACpB,CAAC,CAAC,CAAC;AACH,SAASc,OAAOA,CAACa,GAAG,EAAE;EACpB,IAAI/B,QAAQ,CAAC+B,GAAG,CAAC,IAAIA,GAAG,CAACC,KAAK,IAAI,IAAI,EAAE;IACtC,OAAOD,GAAG,CAACC,KAAK;EAClB,CAAC,MAAM;IACL,OAAOD,GAAG,GAAG,EAAE;EACjB;AACF;AACA,eAAe3B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}