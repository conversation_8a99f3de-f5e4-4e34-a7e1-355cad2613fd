{"ast": null, "code": "import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nexport function getWidth(text, font) {\n  return measureWidth(ensureFontMeasureInfo(font), text);\n}\nexport function ensureFontMeasureInfo(font) {\n  if (!_fontMeasureInfoCache) {\n    _fontMeasureInfoCache = new LRU(100);\n  }\n  font = font || DEFAULT_FONT;\n  var measureInfo = _fontMeasureInfoCache.get(font);\n  if (!measureInfo) {\n    measureInfo = {\n      font: font,\n      strWidthCache: new LRU(500),\n      asciiWidthMap: null,\n      asciiWidthMapTried: false,\n      stWideCharWidth: platformApi.measureText('国', font).width,\n      asciiCharWidth: platformApi.measureText('a', font).width\n    };\n    _fontMeasureInfoCache.put(font, measureInfo);\n  }\n  return measureInfo;\n}\nvar _fontMeasureInfoCache;\nfunction tryCreateASCIIWidthMap(font) {\n  if (_getASCIIWidthMapLongCount >= GET_ASCII_WIDTH_LONG_COUNT_MAX) {\n    return;\n  }\n  font = font || DEFAULT_FONT;\n  var asciiWidthMap = [];\n  var start = +new Date();\n  for (var code = 0; code <= 127; code++) {\n    asciiWidthMap[code] = platformApi.measureText(String.fromCharCode(code), font).width;\n  }\n  var cost = +new Date() - start;\n  if (cost > 16) {\n    _getASCIIWidthMapLongCount = GET_ASCII_WIDTH_LONG_COUNT_MAX;\n  } else if (cost > 2) {\n    _getASCIIWidthMapLongCount++;\n  }\n  return asciiWidthMap;\n}\nvar _getASCIIWidthMapLongCount = 0;\nvar GET_ASCII_WIDTH_LONG_COUNT_MAX = 5;\nexport function measureCharWidth(fontMeasureInfo, charCode) {\n  if (!fontMeasureInfo.asciiWidthMapTried) {\n    fontMeasureInfo.asciiWidthMap = tryCreateASCIIWidthMap(fontMeasureInfo.font);\n    fontMeasureInfo.asciiWidthMapTried = true;\n  }\n  return 0 <= charCode && charCode <= 127 ? fontMeasureInfo.asciiWidthMap != null ? fontMeasureInfo.asciiWidthMap[charCode] : fontMeasureInfo.asciiCharWidth : fontMeasureInfo.stWideCharWidth;\n}\nexport function measureWidth(fontMeasureInfo, text) {\n  var strWidthCache = fontMeasureInfo.strWidthCache;\n  var width = strWidthCache.get(text);\n  if (width == null) {\n    width = platformApi.measureText(text, fontMeasureInfo.font).width;\n    strWidthCache.put(text, width);\n  }\n  return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n  var width = measureWidth(ensureFontMeasureInfo(font), text);\n  var height = getLineHeight(font);\n  var x = adjustTextX(0, width, textAlign);\n  var y = adjustTextY(0, height, textBaseline);\n  var rect = new BoundingRect(x, y, width, height);\n  return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n  var textLines = ((text || '') + '').split('\\n');\n  var len = textLines.length;\n  if (len === 1) {\n    return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n  } else {\n    var uniondRect = new BoundingRect(0, 0, 0, 0);\n    for (var i = 0; i < textLines.length; i++) {\n      var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n      i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n    }\n    return uniondRect;\n  }\n}\nexport function adjustTextX(x, width, textAlign, inverse) {\n  if (textAlign === 'right') {\n    !inverse ? x -= width : x += width;\n  } else if (textAlign === 'center') {\n    !inverse ? x -= width / 2 : x += width / 2;\n  }\n  return x;\n}\nexport function adjustTextY(y, height, verticalAlign, inverse) {\n  if (verticalAlign === 'middle') {\n    !inverse ? y -= height / 2 : y += height / 2;\n  } else if (verticalAlign === 'bottom') {\n    !inverse ? y -= height : y += height;\n  }\n  return y;\n}\nexport function getLineHeight(font) {\n  return ensureFontMeasureInfo(font).stWideCharWidth;\n}\nexport function measureText(text, font) {\n  return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n  if (typeof value === 'string') {\n    if (value.lastIndexOf('%') >= 0) {\n      return parseFloat(value) / 100 * maxValue;\n    }\n    return parseFloat(value);\n  }\n  return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n  var textPosition = opts.position || 'inside';\n  var distance = opts.distance != null ? opts.distance : 5;\n  var height = rect.height;\n  var width = rect.width;\n  var halfHeight = height / 2;\n  var x = rect.x;\n  var y = rect.y;\n  var textAlign = 'left';\n  var textVerticalAlign = 'top';\n  if (textPosition instanceof Array) {\n    x += parsePercent(textPosition[0], rect.width);\n    y += parsePercent(textPosition[1], rect.height);\n    textAlign = null;\n    textVerticalAlign = null;\n  } else {\n    switch (textPosition) {\n      case 'left':\n        x -= distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'right':\n        x += distance + width;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'top':\n        x += width / 2;\n        y -= distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'bottom':\n        x += width / 2;\n        y += height + distance;\n        textAlign = 'center';\n        break;\n      case 'inside':\n        x += width / 2;\n        y += halfHeight;\n        textAlign = 'center';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideLeft':\n        x += distance;\n        y += halfHeight;\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideRight':\n        x += width - distance;\n        y += halfHeight;\n        textAlign = 'right';\n        textVerticalAlign = 'middle';\n        break;\n      case 'insideTop':\n        x += width / 2;\n        y += distance;\n        textAlign = 'center';\n        break;\n      case 'insideBottom':\n        x += width / 2;\n        y += height - distance;\n        textAlign = 'center';\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideTopLeft':\n        x += distance;\n        y += distance;\n        break;\n      case 'insideTopRight':\n        x += width - distance;\n        y += distance;\n        textAlign = 'right';\n        break;\n      case 'insideBottomLeft':\n        x += distance;\n        y += height - distance;\n        textVerticalAlign = 'bottom';\n        break;\n      case 'insideBottomRight':\n        x += width - distance;\n        y += height - distance;\n        textAlign = 'right';\n        textVerticalAlign = 'bottom';\n        break;\n    }\n  }\n  out = out || {};\n  out.x = x;\n  out.y = y;\n  out.align = textAlign;\n  out.verticalAlign = textVerticalAlign;\n  return out;\n}", "map": {"version": 3, "names": ["BoundingRect", "LRU", "DEFAULT_FONT", "platformApi", "getWidth", "text", "font", "measureWidth", "ensureFontMeasureInfo", "_fontMeasureInfoCache", "measureInfo", "get", "str<PERSON>id<PERSON><PERSON><PERSON>", "asciiWidthMap", "asciiWidthMapTried", "stWideCharWidth", "measureText", "width", "asciiCharWidth", "put", "tryCreateASCIIWidthMap", "_getASCIIWidthMapLongCount", "GET_ASCII_WIDTH_LONG_COUNT_MAX", "start", "Date", "code", "String", "fromCharCode", "cost", "measureCharWidth", "fontMeasureInfo", "charCode", "innerGetBoundingRect", "textAlign", "textBaseline", "height", "getLineHeight", "x", "adjustTextX", "y", "adjustTextY", "rect", "getBoundingRect", "textLines", "split", "len", "length", "uniondRect", "i", "copy", "union", "inverse", "verticalAlign", "parsePercent", "value", "maxValue", "lastIndexOf", "parseFloat", "calculateTextPosition", "out", "opts", "textPosition", "position", "distance", "halfHeight", "textVerticalAlign", "Array", "align"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/contain/text.js"], "sourcesContent": ["import BoundingRect from '../core/BoundingRect.js';\nimport LRU from '../core/LRU.js';\nimport { DEFAULT_FONT, platformApi } from '../core/platform.js';\nexport function getWidth(text, font) {\n    return measureWidth(ensureFontMeasureInfo(font), text);\n}\nexport function ensureFontMeasureInfo(font) {\n    if (!_fontMeasureInfoCache) {\n        _fontMeasureInfoCache = new LRU(100);\n    }\n    font = font || DEFAULT_FONT;\n    var measureInfo = _fontMeasureInfoCache.get(font);\n    if (!measureInfo) {\n        measureInfo = {\n            font: font,\n            strWidthCache: new LRU(500),\n            asciiWidthMap: null,\n            asciiWidthMapTried: false,\n            stWideCharWidth: platformApi.measureText('国', font).width,\n            asciiCharWidth: platformApi.measureText('a', font).width\n        };\n        _fontMeasureInfoCache.put(font, measureInfo);\n    }\n    return measureInfo;\n}\nvar _fontMeasureInfoCache;\nfunction tryCreateASCIIWidthMap(font) {\n    if (_getASCIIWidthMapLongCount >= GET_ASCII_WIDTH_LONG_COUNT_MAX) {\n        return;\n    }\n    font = font || DEFAULT_FONT;\n    var asciiWidthMap = [];\n    var start = +(new Date());\n    for (var code = 0; code <= 127; code++) {\n        asciiWidthMap[code] = platformApi.measureText(String.fromCharCode(code), font).width;\n    }\n    var cost = +(new Date()) - start;\n    if (cost > 16) {\n        _getASCIIWidthMapLongCount = GET_ASCII_WIDTH_LONG_COUNT_MAX;\n    }\n    else if (cost > 2) {\n        _getASCIIWidthMapLongCount++;\n    }\n    return asciiWidthMap;\n}\nvar _getASCIIWidthMapLongCount = 0;\nvar GET_ASCII_WIDTH_LONG_COUNT_MAX = 5;\nexport function measureCharWidth(fontMeasureInfo, charCode) {\n    if (!fontMeasureInfo.asciiWidthMapTried) {\n        fontMeasureInfo.asciiWidthMap = tryCreateASCIIWidthMap(fontMeasureInfo.font);\n        fontMeasureInfo.asciiWidthMapTried = true;\n    }\n    return (0 <= charCode && charCode <= 127)\n        ? (fontMeasureInfo.asciiWidthMap != null\n            ? fontMeasureInfo.asciiWidthMap[charCode]\n            : fontMeasureInfo.asciiCharWidth)\n        : fontMeasureInfo.stWideCharWidth;\n}\nexport function measureWidth(fontMeasureInfo, text) {\n    var strWidthCache = fontMeasureInfo.strWidthCache;\n    var width = strWidthCache.get(text);\n    if (width == null) {\n        width = platformApi.measureText(text, fontMeasureInfo.font).width;\n        strWidthCache.put(text, width);\n    }\n    return width;\n}\nexport function innerGetBoundingRect(text, font, textAlign, textBaseline) {\n    var width = measureWidth(ensureFontMeasureInfo(font), text);\n    var height = getLineHeight(font);\n    var x = adjustTextX(0, width, textAlign);\n    var y = adjustTextY(0, height, textBaseline);\n    var rect = new BoundingRect(x, y, width, height);\n    return rect;\n}\nexport function getBoundingRect(text, font, textAlign, textBaseline) {\n    var textLines = ((text || '') + '').split('\\n');\n    var len = textLines.length;\n    if (len === 1) {\n        return innerGetBoundingRect(textLines[0], font, textAlign, textBaseline);\n    }\n    else {\n        var uniondRect = new BoundingRect(0, 0, 0, 0);\n        for (var i = 0; i < textLines.length; i++) {\n            var rect = innerGetBoundingRect(textLines[i], font, textAlign, textBaseline);\n            i === 0 ? uniondRect.copy(rect) : uniondRect.union(rect);\n        }\n        return uniondRect;\n    }\n}\nexport function adjustTextX(x, width, textAlign, inverse) {\n    if (textAlign === 'right') {\n        !inverse ? (x -= width) : (x += width);\n    }\n    else if (textAlign === 'center') {\n        !inverse ? (x -= width / 2) : (x += width / 2);\n    }\n    return x;\n}\nexport function adjustTextY(y, height, verticalAlign, inverse) {\n    if (verticalAlign === 'middle') {\n        !inverse ? (y -= height / 2) : (y += height / 2);\n    }\n    else if (verticalAlign === 'bottom') {\n        !inverse ? (y -= height) : (y += height);\n    }\n    return y;\n}\nexport function getLineHeight(font) {\n    return ensureFontMeasureInfo(font).stWideCharWidth;\n}\nexport function measureText(text, font) {\n    return platformApi.measureText(text, font);\n}\nexport function parsePercent(value, maxValue) {\n    if (typeof value === 'string') {\n        if (value.lastIndexOf('%') >= 0) {\n            return parseFloat(value) / 100 * maxValue;\n        }\n        return parseFloat(value);\n    }\n    return value;\n}\nexport function calculateTextPosition(out, opts, rect) {\n    var textPosition = opts.position || 'inside';\n    var distance = opts.distance != null ? opts.distance : 5;\n    var height = rect.height;\n    var width = rect.width;\n    var halfHeight = height / 2;\n    var x = rect.x;\n    var y = rect.y;\n    var textAlign = 'left';\n    var textVerticalAlign = 'top';\n    if (textPosition instanceof Array) {\n        x += parsePercent(textPosition[0], rect.width);\n        y += parsePercent(textPosition[1], rect.height);\n        textAlign = null;\n        textVerticalAlign = null;\n    }\n    else {\n        switch (textPosition) {\n            case 'left':\n                x -= distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'right':\n                x += distance + width;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'top':\n                x += width / 2;\n                y -= distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'bottom':\n                x += width / 2;\n                y += height + distance;\n                textAlign = 'center';\n                break;\n            case 'inside':\n                x += width / 2;\n                y += halfHeight;\n                textAlign = 'center';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideLeft':\n                x += distance;\n                y += halfHeight;\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideRight':\n                x += width - distance;\n                y += halfHeight;\n                textAlign = 'right';\n                textVerticalAlign = 'middle';\n                break;\n            case 'insideTop':\n                x += width / 2;\n                y += distance;\n                textAlign = 'center';\n                break;\n            case 'insideBottom':\n                x += width / 2;\n                y += height - distance;\n                textAlign = 'center';\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideTopLeft':\n                x += distance;\n                y += distance;\n                break;\n            case 'insideTopRight':\n                x += width - distance;\n                y += distance;\n                textAlign = 'right';\n                break;\n            case 'insideBottomLeft':\n                x += distance;\n                y += height - distance;\n                textVerticalAlign = 'bottom';\n                break;\n            case 'insideBottomRight':\n                x += width - distance;\n                y += height - distance;\n                textAlign = 'right';\n                textVerticalAlign = 'bottom';\n                break;\n        }\n    }\n    out = out || {};\n    out.x = x;\n    out.y = y;\n    out.align = textAlign;\n    out.verticalAlign = textVerticalAlign;\n    return out;\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,yBAAyB;AAClD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,YAAY,EAAEC,WAAW,QAAQ,qBAAqB;AAC/D,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACjC,OAAOC,YAAY,CAACC,qBAAqB,CAACF,IAAI,CAAC,EAAED,IAAI,CAAC;AAC1D;AACA,OAAO,SAASG,qBAAqBA,CAACF,IAAI,EAAE;EACxC,IAAI,CAACG,qBAAqB,EAAE;IACxBA,qBAAqB,GAAG,IAAIR,GAAG,CAAC,GAAG,CAAC;EACxC;EACAK,IAAI,GAAGA,IAAI,IAAIJ,YAAY;EAC3B,IAAIQ,WAAW,GAAGD,qBAAqB,CAACE,GAAG,CAACL,IAAI,CAAC;EACjD,IAAI,CAACI,WAAW,EAAE;IACdA,WAAW,GAAG;MACVJ,IAAI,EAAEA,IAAI;MACVM,aAAa,EAAE,IAAIX,GAAG,CAAC,GAAG,CAAC;MAC3BY,aAAa,EAAE,IAAI;MACnBC,kBAAkB,EAAE,KAAK;MACzBC,eAAe,EAAEZ,WAAW,CAACa,WAAW,CAAC,GAAG,EAAEV,IAAI,CAAC,CAACW,KAAK;MACzDC,cAAc,EAAEf,WAAW,CAACa,WAAW,CAAC,GAAG,EAAEV,IAAI,CAAC,CAACW;IACvD,CAAC;IACDR,qBAAqB,CAACU,GAAG,CAACb,IAAI,EAAEI,WAAW,CAAC;EAChD;EACA,OAAOA,WAAW;AACtB;AACA,IAAID,qBAAqB;AACzB,SAASW,sBAAsBA,CAACd,IAAI,EAAE;EAClC,IAAIe,0BAA0B,IAAIC,8BAA8B,EAAE;IAC9D;EACJ;EACAhB,IAAI,GAAGA,IAAI,IAAIJ,YAAY;EAC3B,IAAIW,aAAa,GAAG,EAAE;EACtB,IAAIU,KAAK,GAAG,CAAE,IAAIC,IAAI,CAAC,CAAE;EACzB,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAI,GAAG,EAAEA,IAAI,EAAE,EAAE;IACpCZ,aAAa,CAACY,IAAI,CAAC,GAAGtB,WAAW,CAACa,WAAW,CAACU,MAAM,CAACC,YAAY,CAACF,IAAI,CAAC,EAAEnB,IAAI,CAAC,CAACW,KAAK;EACxF;EACA,IAAIW,IAAI,GAAG,CAAE,IAAIJ,IAAI,CAAC,CAAE,GAAGD,KAAK;EAChC,IAAIK,IAAI,GAAG,EAAE,EAAE;IACXP,0BAA0B,GAAGC,8BAA8B;EAC/D,CAAC,MACI,IAAIM,IAAI,GAAG,CAAC,EAAE;IACfP,0BAA0B,EAAE;EAChC;EACA,OAAOR,aAAa;AACxB;AACA,IAAIQ,0BAA0B,GAAG,CAAC;AAClC,IAAIC,8BAA8B,GAAG,CAAC;AACtC,OAAO,SAASO,gBAAgBA,CAACC,eAAe,EAAEC,QAAQ,EAAE;EACxD,IAAI,CAACD,eAAe,CAAChB,kBAAkB,EAAE;IACrCgB,eAAe,CAACjB,aAAa,GAAGO,sBAAsB,CAACU,eAAe,CAACxB,IAAI,CAAC;IAC5EwB,eAAe,CAAChB,kBAAkB,GAAG,IAAI;EAC7C;EACA,OAAQ,CAAC,IAAIiB,QAAQ,IAAIA,QAAQ,IAAI,GAAG,GACjCD,eAAe,CAACjB,aAAa,IAAI,IAAI,GAClCiB,eAAe,CAACjB,aAAa,CAACkB,QAAQ,CAAC,GACvCD,eAAe,CAACZ,cAAc,GAClCY,eAAe,CAACf,eAAe;AACzC;AACA,OAAO,SAASR,YAAYA,CAACuB,eAAe,EAAEzB,IAAI,EAAE;EAChD,IAAIO,aAAa,GAAGkB,eAAe,CAAClB,aAAa;EACjD,IAAIK,KAAK,GAAGL,aAAa,CAACD,GAAG,CAACN,IAAI,CAAC;EACnC,IAAIY,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGd,WAAW,CAACa,WAAW,CAACX,IAAI,EAAEyB,eAAe,CAACxB,IAAI,CAAC,CAACW,KAAK;IACjEL,aAAa,CAACO,GAAG,CAACd,IAAI,EAAEY,KAAK,CAAC;EAClC;EACA,OAAOA,KAAK;AAChB;AACA,OAAO,SAASe,oBAAoBA,CAAC3B,IAAI,EAAEC,IAAI,EAAE2B,SAAS,EAAEC,YAAY,EAAE;EACtE,IAAIjB,KAAK,GAAGV,YAAY,CAACC,qBAAqB,CAACF,IAAI,CAAC,EAAED,IAAI,CAAC;EAC3D,IAAI8B,MAAM,GAAGC,aAAa,CAAC9B,IAAI,CAAC;EAChC,IAAI+B,CAAC,GAAGC,WAAW,CAAC,CAAC,EAAErB,KAAK,EAAEgB,SAAS,CAAC;EACxC,IAAIM,CAAC,GAAGC,WAAW,CAAC,CAAC,EAAEL,MAAM,EAAED,YAAY,CAAC;EAC5C,IAAIO,IAAI,GAAG,IAAIzC,YAAY,CAACqC,CAAC,EAAEE,CAAC,EAAEtB,KAAK,EAAEkB,MAAM,CAAC;EAChD,OAAOM,IAAI;AACf;AACA,OAAO,SAASC,eAAeA,CAACrC,IAAI,EAAEC,IAAI,EAAE2B,SAAS,EAAEC,YAAY,EAAE;EACjE,IAAIS,SAAS,GAAG,CAAC,CAACtC,IAAI,IAAI,EAAE,IAAI,EAAE,EAAEuC,KAAK,CAAC,IAAI,CAAC;EAC/C,IAAIC,GAAG,GAAGF,SAAS,CAACG,MAAM;EAC1B,IAAID,GAAG,KAAK,CAAC,EAAE;IACX,OAAOb,oBAAoB,CAACW,SAAS,CAAC,CAAC,CAAC,EAAErC,IAAI,EAAE2B,SAAS,EAAEC,YAAY,CAAC;EAC5E,CAAC,MACI;IACD,IAAIa,UAAU,GAAG,IAAI/C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;MACvC,IAAIP,IAAI,GAAGT,oBAAoB,CAACW,SAAS,CAACK,CAAC,CAAC,EAAE1C,IAAI,EAAE2B,SAAS,EAAEC,YAAY,CAAC;MAC5Ec,CAAC,KAAK,CAAC,GAAGD,UAAU,CAACE,IAAI,CAACR,IAAI,CAAC,GAAGM,UAAU,CAACG,KAAK,CAACT,IAAI,CAAC;IAC5D;IACA,OAAOM,UAAU;EACrB;AACJ;AACA,OAAO,SAAST,WAAWA,CAACD,CAAC,EAAEpB,KAAK,EAAEgB,SAAS,EAAEkB,OAAO,EAAE;EACtD,IAAIlB,SAAS,KAAK,OAAO,EAAE;IACvB,CAACkB,OAAO,GAAId,CAAC,IAAIpB,KAAK,GAAKoB,CAAC,IAAIpB,KAAM;EAC1C,CAAC,MACI,IAAIgB,SAAS,KAAK,QAAQ,EAAE;IAC7B,CAACkB,OAAO,GAAId,CAAC,IAAIpB,KAAK,GAAG,CAAC,GAAKoB,CAAC,IAAIpB,KAAK,GAAG,CAAE;EAClD;EACA,OAAOoB,CAAC;AACZ;AACA,OAAO,SAASG,WAAWA,CAACD,CAAC,EAAEJ,MAAM,EAAEiB,aAAa,EAAED,OAAO,EAAE;EAC3D,IAAIC,aAAa,KAAK,QAAQ,EAAE;IAC5B,CAACD,OAAO,GAAIZ,CAAC,IAAIJ,MAAM,GAAG,CAAC,GAAKI,CAAC,IAAIJ,MAAM,GAAG,CAAE;EACpD,CAAC,MACI,IAAIiB,aAAa,KAAK,QAAQ,EAAE;IACjC,CAACD,OAAO,GAAIZ,CAAC,IAAIJ,MAAM,GAAKI,CAAC,IAAIJ,MAAO;EAC5C;EACA,OAAOI,CAAC;AACZ;AACA,OAAO,SAASH,aAAaA,CAAC9B,IAAI,EAAE;EAChC,OAAOE,qBAAqB,CAACF,IAAI,CAAC,CAACS,eAAe;AACtD;AACA,OAAO,SAASC,WAAWA,CAACX,IAAI,EAAEC,IAAI,EAAE;EACpC,OAAOH,WAAW,CAACa,WAAW,CAACX,IAAI,EAAEC,IAAI,CAAC;AAC9C;AACA,OAAO,SAAS+C,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC1C,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAIA,KAAK,CAACE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MAC7B,OAAOC,UAAU,CAACH,KAAK,CAAC,GAAG,GAAG,GAAGC,QAAQ;IAC7C;IACA,OAAOE,UAAU,CAACH,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK;AAChB;AACA,OAAO,SAASI,qBAAqBA,CAACC,GAAG,EAAEC,IAAI,EAAEnB,IAAI,EAAE;EACnD,IAAIoB,YAAY,GAAGD,IAAI,CAACE,QAAQ,IAAI,QAAQ;EAC5C,IAAIC,QAAQ,GAAGH,IAAI,CAACG,QAAQ,IAAI,IAAI,GAAGH,IAAI,CAACG,QAAQ,GAAG,CAAC;EACxD,IAAI5B,MAAM,GAAGM,IAAI,CAACN,MAAM;EACxB,IAAIlB,KAAK,GAAGwB,IAAI,CAACxB,KAAK;EACtB,IAAI+C,UAAU,GAAG7B,MAAM,GAAG,CAAC;EAC3B,IAAIE,CAAC,GAAGI,IAAI,CAACJ,CAAC;EACd,IAAIE,CAAC,GAAGE,IAAI,CAACF,CAAC;EACd,IAAIN,SAAS,GAAG,MAAM;EACtB,IAAIgC,iBAAiB,GAAG,KAAK;EAC7B,IAAIJ,YAAY,YAAYK,KAAK,EAAE;IAC/B7B,CAAC,IAAIgB,YAAY,CAACQ,YAAY,CAAC,CAAC,CAAC,EAAEpB,IAAI,CAACxB,KAAK,CAAC;IAC9CsB,CAAC,IAAIc,YAAY,CAACQ,YAAY,CAAC,CAAC,CAAC,EAAEpB,IAAI,CAACN,MAAM,CAAC;IAC/CF,SAAS,GAAG,IAAI;IAChBgC,iBAAiB,GAAG,IAAI;EAC5B,CAAC,MACI;IACD,QAAQJ,YAAY;MAChB,KAAK,MAAM;QACPxB,CAAC,IAAI0B,QAAQ;QACbxB,CAAC,IAAIyB,UAAU;QACf/B,SAAS,GAAG,OAAO;QACnBgC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,OAAO;QACR5B,CAAC,IAAI0B,QAAQ,GAAG9C,KAAK;QACrBsB,CAAC,IAAIyB,UAAU;QACfC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,KAAK;QACN5B,CAAC,IAAIpB,KAAK,GAAG,CAAC;QACdsB,CAAC,IAAIwB,QAAQ;QACb9B,SAAS,GAAG,QAAQ;QACpBgC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,QAAQ;QACT5B,CAAC,IAAIpB,KAAK,GAAG,CAAC;QACdsB,CAAC,IAAIJ,MAAM,GAAG4B,QAAQ;QACtB9B,SAAS,GAAG,QAAQ;QACpB;MACJ,KAAK,QAAQ;QACTI,CAAC,IAAIpB,KAAK,GAAG,CAAC;QACdsB,CAAC,IAAIyB,UAAU;QACf/B,SAAS,GAAG,QAAQ;QACpBgC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,YAAY;QACb5B,CAAC,IAAI0B,QAAQ;QACbxB,CAAC,IAAIyB,UAAU;QACfC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,aAAa;QACd5B,CAAC,IAAIpB,KAAK,GAAG8C,QAAQ;QACrBxB,CAAC,IAAIyB,UAAU;QACf/B,SAAS,GAAG,OAAO;QACnBgC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,WAAW;QACZ5B,CAAC,IAAIpB,KAAK,GAAG,CAAC;QACdsB,CAAC,IAAIwB,QAAQ;QACb9B,SAAS,GAAG,QAAQ;QACpB;MACJ,KAAK,cAAc;QACfI,CAAC,IAAIpB,KAAK,GAAG,CAAC;QACdsB,CAAC,IAAIJ,MAAM,GAAG4B,QAAQ;QACtB9B,SAAS,GAAG,QAAQ;QACpBgC,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,eAAe;QAChB5B,CAAC,IAAI0B,QAAQ;QACbxB,CAAC,IAAIwB,QAAQ;QACb;MACJ,KAAK,gBAAgB;QACjB1B,CAAC,IAAIpB,KAAK,GAAG8C,QAAQ;QACrBxB,CAAC,IAAIwB,QAAQ;QACb9B,SAAS,GAAG,OAAO;QACnB;MACJ,KAAK,kBAAkB;QACnBI,CAAC,IAAI0B,QAAQ;QACbxB,CAAC,IAAIJ,MAAM,GAAG4B,QAAQ;QACtBE,iBAAiB,GAAG,QAAQ;QAC5B;MACJ,KAAK,mBAAmB;QACpB5B,CAAC,IAAIpB,KAAK,GAAG8C,QAAQ;QACrBxB,CAAC,IAAIJ,MAAM,GAAG4B,QAAQ;QACtB9B,SAAS,GAAG,OAAO;QACnBgC,iBAAiB,GAAG,QAAQ;QAC5B;IACR;EACJ;EACAN,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACfA,GAAG,CAACtB,CAAC,GAAGA,CAAC;EACTsB,GAAG,CAACpB,CAAC,GAAGA,CAAC;EACToB,GAAG,CAACQ,KAAK,GAAGlC,SAAS;EACrB0B,GAAG,CAACP,aAAa,GAAGa,iBAAiB;EACrC,OAAON,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}