{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { retrieve, defaults, extend, each, isObject, isString, isNumber, isFunction, retrieve2, assert, map, retrieve3, filter } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { isNameLocationCenter, shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { hideOverlap, labelIntersect, computeLabelGeometry2, ensureLabelLayoutWithGeometry, labelLayoutApplyTranslation, setLabelLayoutDirty, newLabelLayoutWithGeometry } from '../../label/labelLayoutHelper.js';\nimport { makeInner } from '../../util/model.js';\nimport { getAxisBreakHelper } from './axisBreakHelper.js';\nimport { AXIS_BREAK_EXPAND_ACTION_TYPE } from './axisAction.js';\nimport { getScaleBreakHelper } from '../../scale/break.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport { copyTransform } from 'zrender/lib/core/Transformable.js';\nimport { AxisTickLabelComputingKind, createAxisLabelsComputingContext } from '../../coord/axisTickLabelBuilder.js';\nvar PI = Math.PI;\nvar DEFAULT_CENTER_NAME_MARGIN_LEVELS = [[1, 2, 1, 2], [5, 3, 5, 3], [8, 3, 8, 3]];\nvar DEFAULT_ENDS_NAME_MARGIN_LEVELS = [[0, 1, 0, 1], [0, 3, 0, 3], [0, 3, 0, 3]];\nexport var getLabelInner = makeInner();\nvar getTickInner = makeInner();\n/**\n * A context shared by difference axisBuilder instances.\n * For cross-axes overlap resolving.\n *\n * Lifecycle constraint: should not over a pass of ec main process.\n *  If model is changed, the context must be disposed.\n *\n * @see AxisBuilderLocalContext\n */\nvar AxisBuilderSharedContext = /** @class */function () {\n  function AxisBuilderSharedContext(resolveAxisNameOverlap) {\n    /**\n     * [CAUTION] Do not modify this data structure outside this class.\n     */\n    this.recordMap = {};\n    this.resolveAxisNameOverlap = resolveAxisNameOverlap;\n  }\n  AxisBuilderSharedContext.prototype.ensureRecord = function (axisModel) {\n    var dim = axisModel.axis.dim;\n    var idx = axisModel.componentIndex;\n    var recordMap = this.recordMap;\n    var records = recordMap[dim] || (recordMap[dim] = []);\n    return records[idx] || (records[idx] = {\n      ready: {}\n    });\n  };\n  return AxisBuilderSharedContext;\n}();\nexport { AxisBuilderSharedContext };\n;\n/**\n * [CAUTION]\n *  1. The call of this function must be after axisLabel overlap handlings\n *     (such as `hideOverlap`, `fixMinMaxLabelShow`) and after transform calculating.\n *  2. Can be called multiple times and should be idempotent.\n */\nfunction resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList) {\n  var axis = axisModel.axis;\n  var record = shared.ensureRecord(axisModel);\n  var labelInfoList = [];\n  var stOccupiedRect;\n  var useStOccupiedRect = hasAxisName(cfg.axisName) && isNameLocationCenter(cfg.nameLocation);\n  each(labelLayoutList, function (layout) {\n    var layoutInfo = ensureLabelLayoutWithGeometry(layout);\n    if (!layoutInfo || layoutInfo.label.ignore) {\n      return;\n    }\n    labelInfoList.push(layoutInfo);\n    var transGroup = record.transGroup;\n    if (useStOccupiedRect) {\n      // Transform to \"standard axis\" for creating stOccupiedRect (the label rects union).\n      transGroup.transform ? matrixUtil.invert(_stTransTmp, transGroup.transform) : matrixUtil.identity(_stTransTmp);\n      if (layoutInfo.transform) {\n        matrixUtil.mul(_stTransTmp, _stTransTmp, layoutInfo.transform);\n      }\n      BoundingRect.copy(_stLabelRectTmp, layoutInfo.localRect);\n      _stLabelRectTmp.applyTransform(_stTransTmp);\n      stOccupiedRect ? stOccupiedRect.union(_stLabelRectTmp) : BoundingRect.copy(stOccupiedRect = new BoundingRect(0, 0, 0, 0), _stLabelRectTmp);\n    }\n  });\n  var sortByDim = Math.abs(record.dirVec.x) > 0.1 ? 'x' : 'y';\n  var sortByValue = record.transGroup[sortByDim];\n  labelInfoList.sort(function (info1, info2) {\n    return Math.abs(info1.label[sortByDim] - sortByValue) - Math.abs(info2.label[sortByDim] - sortByValue);\n  });\n  if (useStOccupiedRect && stOccupiedRect) {\n    var extent = axis.getExtent();\n    var axisLineX = Math.min(extent[0], extent[1]);\n    var axisLineWidth = Math.max(extent[0], extent[1]) - axisLineX;\n    // If `nameLocation` is 'middle', enlarge axis labels boundingRect to axisLine to avoid bad\n    //  case like that axis name is placed in the gap between axis labels and axis line.\n    // If only one label exists, the entire band should be occupied for\n    // visual consistency, so extent it to [0, canvas width].\n    stOccupiedRect.union(new BoundingRect(axisLineX, 0, axisLineWidth, 1));\n  }\n  record.stOccupiedRect = stOccupiedRect;\n  record.labelInfoList = labelInfoList;\n}\nvar _stTransTmp = matrixUtil.create();\nvar _stLabelRectTmp = new BoundingRect(0, 0, 0, 0);\n/**\n * The default resolver does not involve other axes within the same coordinate system.\n */\nexport var resolveAxisNameOverlapDefault = function (cfg, ctx, axisModel, nameLayoutInfo, nameMoveDirVec, thisRecord) {\n  if (isNameLocationCenter(cfg.nameLocation)) {\n    var stOccupiedRect = thisRecord.stOccupiedRect;\n    if (stOccupiedRect) {\n      moveIfOverlap(computeLabelGeometry2({}, stOccupiedRect, thisRecord.transGroup.transform), nameLayoutInfo, nameMoveDirVec);\n    }\n  } else {\n    moveIfOverlapByLinearLabels(thisRecord.labelInfoList, thisRecord.dirVec, nameLayoutInfo, nameMoveDirVec);\n  }\n};\n// [NOTICE] not consider ignore.\nfunction moveIfOverlap(basedLayoutInfo, movableLayoutInfo, moveDirVec) {\n  var mtv = new Point();\n  if (labelIntersect(basedLayoutInfo, movableLayoutInfo, mtv, {\n    direction: Math.atan2(moveDirVec.y, moveDirVec.x),\n    bidirectional: false,\n    touchThreshold: 0.05\n  })) {\n    labelLayoutApplyTranslation(movableLayoutInfo, mtv);\n  }\n}\nexport function moveIfOverlapByLinearLabels(baseLayoutInfoList, baseDirVec, movableLayoutInfo, moveDirVec) {\n  // Detect and move from far to close.\n  var sameDir = Point.dot(moveDirVec, baseDirVec) >= 0;\n  for (var idx = 0, len = baseLayoutInfoList.length; idx < len; idx++) {\n    var labelInfo = baseLayoutInfoList[sameDir ? idx : len - 1 - idx];\n    if (!labelInfo.label.ignore) {\n      moveIfOverlap(labelInfo, movableLayoutInfo, moveDirVec);\n    }\n  }\n}\n/**\n * @caution\n * - Ensure it is called after the data processing stage finished.\n * - It might be called before `CahrtView#render`, sush as called at `CoordinateSystem#update`,\n *  thus ensure the result the same whenever it is called.\n *\n * A builder for a straight-line axis.\n *\n * A final axis is translated and rotated from a \"standard axis\".\n * So opt.position and opt.rotation is required.\n *\n * A \"standard axis\" is the axis [0,0]-->[abs(axisExtent[1]-axisExtent[0]),0]\n * for example: [0,0]-->[50,0]\n */\nvar AxisBuilder = /** @class */function () {\n  /**\n   * [CAUTION]: axisModel.axis.extent/scale must be ready to use.\n   */\n  function AxisBuilder(axisModel, api, opt, shared) {\n    this.group = new graphic.Group();\n    this._axisModel = axisModel;\n    this._api = api;\n    this._local = {};\n    this._shared = shared || new AxisBuilderSharedContext(resolveAxisNameOverlapDefault);\n    this._resetCfgDetermined(opt);\n  }\n  /**\n   * Regarding axis label related configurations, only the change of label.x/y is supported; other\n   * changes are not necessary and not performant. To be specific, only `axis.position`\n   * (and consequently `labelOffset`) and `axis.extent` can be changed, and assume everything in\n   * `axisModel` are not changed.\n   * Axis line related configurations can be changed since this method can only be called\n   * before they are created.\n   */\n  AxisBuilder.prototype.updateCfg = function (opt) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = this._shared.ensureRecord(this._axisModel).ready;\n      // After that, changing cfg is not supported; avoid unnecessary complexity.\n      assert(!ready.axisLine && !ready.axisTickLabelDetermine);\n      // Have to be called again if cfg changed.\n      ready.axisName = ready.axisTickLabelEstimate = false;\n    }\n    var raw = this._cfg.raw;\n    raw.position = opt.position;\n    raw.labelOffset = opt.labelOffset;\n    this._resetCfgDetermined(raw);\n  };\n  /**\n   * [CAUTION] For debug usage. Never change it outside!\n   */\n  AxisBuilder.prototype.__getRawCfg = function () {\n    return this._cfg.raw;\n  };\n  AxisBuilder.prototype._resetCfgDetermined = function (raw) {\n    var axisModel = this._axisModel;\n    // FIXME:\n    //  Currently there is no uniformed way to set default values if an option\n    //  is specified null/undefined by user (intentionally or unintentionally),\n    //  e.g. null/undefined is not a illegal value for `nameLocation`.\n    //  Try to use `getDefaultOption` to address it. But radar has no `getDefaultOption`.\n    var axisModelDefaultOption = axisModel.getDefaultOption ? axisModel.getDefaultOption() : {};\n    // Default value\n    var axisName = retrieve2(raw.axisName, axisModel.get('name'));\n    var nameMoveOverlapOption = axisModel.get('nameMoveOverlap');\n    if (nameMoveOverlapOption == null || nameMoveOverlapOption === 'auto') {\n      nameMoveOverlapOption = retrieve2(raw.defaultNameMoveOverlap, true);\n    }\n    var cfg = {\n      raw: raw,\n      position: raw.position,\n      rotation: raw.rotation,\n      nameDirection: retrieve2(raw.nameDirection, 1),\n      tickDirection: retrieve2(raw.tickDirection, 1),\n      labelDirection: retrieve2(raw.labelDirection, 1),\n      labelOffset: retrieve2(raw.labelOffset, 0),\n      silent: retrieve2(raw.silent, true),\n      axisName: axisName,\n      nameLocation: retrieve3(axisModel.get('nameLocation'), axisModelDefaultOption.nameLocation, 'end'),\n      shouldNameMoveOverlap: hasAxisName(axisName) && nameMoveOverlapOption,\n      optionHideOverlap: axisModel.get(['axisLabel', 'hideOverlap']),\n      showMinorTicks: axisModel.get(['minorTick', 'show'])\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      assert(cfg.position != null);\n      assert(cfg.rotation != null);\n    }\n    this._cfg = cfg;\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: cfg.position[0],\n      y: cfg.position[1],\n      rotation: cfg.rotation\n    });\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n    var record = this._shared.ensureRecord(axisModel);\n    record.transGroup = this._transformGroup;\n    record.dirVec = new Point(Math.cos(-cfg.rotation), Math.sin(-cfg.rotation));\n  };\n  AxisBuilder.prototype.build = function (axisPartNameMap, extraParams) {\n    var _this = this;\n    if (!axisPartNameMap) {\n      axisPartNameMap = {\n        axisLine: true,\n        axisTickLabelEstimate: false,\n        axisTickLabelDetermine: true,\n        axisName: true\n      };\n    }\n    each(AXIS_BUILDER_AXIS_PART_NAMES, function (partName) {\n      if (axisPartNameMap[partName]) {\n        builders[partName](_this._cfg, _this._local, _this._shared, _this._axisModel, _this.group, _this._transformGroup, _this._api, extraParams || {});\n      }\n    });\n    return this;\n  };\n  /**\n   * Currently only get text align/verticalAlign by rotation.\n   * NO `position` is involved, otherwise it have to be performed for each `updateAxisLabelChangableProps`.\n   */\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\n// Sorted by dependency order.\nvar AXIS_BUILDER_AXIS_PART_NAMES = ['axisLine', 'axisTickLabelEstimate', 'axisTickLabelDetermine', 'axisName'];\nvar builders = {\n  axisLine: function (cfg, local, shared, axisModel, group, transformGroup, api) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      assert(!ready.axisLine);\n      ready.axisLine = true;\n    }\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto') {\n      shown = true;\n      if (cfg.raw.axisLineAutoShow != null) {\n        shown = !!cfg.raw.axisLineAutoShow;\n      }\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var pathBaseProp = {\n      strokeContainThreshold: cfg.raw.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1,\n      style: lineStyle\n    };\n    if (axisModel.get(['axisLine', 'breakLine']) && axisModel.axis.scale.hasBreaks()) {\n      getAxisBreakHelper().buildAxisBreakLine(axisModel, group, transformGroup, pathBaseProp);\n    } else {\n      var line = new graphic.Line(extend({\n        shape: {\n          x1: pt1[0],\n          y1: pt1[1],\n          x2: pt2[0],\n          y2: pt2[1]\n        }\n      }, pathBaseProp));\n      graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n      line.anid = 'line';\n      group.add(line);\n    }\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: cfg.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: cfg.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(cfg.rotation),\n            y: pt[1] - r * Math.sin(cfg.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  /**\n   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called\n   *  in size measurement. Thus this method should be idempotent, and should be performant.\n   */\n  axisTickLabelEstimate: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      assert(!ready.axisTickLabelDetermine);\n      ready.axisTickLabelEstimate = true;\n    }\n    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);\n    if (needCallLayout) {\n      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, AxisTickLabelComputingKind.estimate);\n    }\n  },\n  /**\n   * Finish axis tick label build.\n   * Can be only called once.\n   */\n  axisTickLabelDetermine: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      ready.axisTickLabelDetermine = true;\n    }\n    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);\n    if (needCallLayout) {\n      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, AxisTickLabelComputingKind.determine);\n    }\n    var ticksEls = buildAxisMajorTicks(cfg, group, transformGroup, axisModel);\n    syncLabelIgnoreToMajorTicks(cfg, local.labelLayoutList, ticksEls);\n    buildAxisMinorTicks(cfg, group, transformGroup, axisModel, cfg.tickDirection);\n  },\n  /**\n   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called\n   *  in size measurement. Thus this method should be idempotent, and should be performant.\n   */\n  axisName: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    var sharedRecord = shared.ensureRecord(axisModel);\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = sharedRecord.ready;\n      assert(ready.axisTickLabelEstimate || ready.axisTickLabelDetermine);\n      ready.axisName = true;\n    }\n    // Remove the existing name result created in estimation phase.\n    if (local.nameEl) {\n      group.remove(local.nameEl);\n      local.nameEl = sharedRecord.nameLayout = sharedRecord.nameLocation = null;\n    }\n    var name = cfg.axisName;\n    if (!hasAxisName(name)) {\n      return;\n    }\n    var nameLocation = cfg.nameLocation;\n    var nameDirection = cfg.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapStartEndSignal = axisModel.axis.inverse ? -1 : 1;\n    var pos = new Point(0, 0);\n    var nameMoveDirVec = new Point(0, 0);\n    if (nameLocation === 'start') {\n      pos.x = extent[0] - gapStartEndSignal * gap;\n      nameMoveDirVec.x = -gapStartEndSignal;\n    } else if (nameLocation === 'end') {\n      pos.x = extent[1] + gapStartEndSignal * gap;\n      nameMoveDirVec.x = gapStartEndSignal;\n    } else {\n      // 'middle' or 'center'\n      pos.x = (extent[0] + extent[1]) / 2;\n      pos.y = cfg.labelOffset + nameDirection * gap;\n      nameMoveDirVec.y = nameDirection;\n    }\n    var mt = matrixUtil.create();\n    nameMoveDirVec.transform(matrixUtil.rotate(mt, mt, cfg.rotation));\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var labelLayout;\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(cfg.rotation, nameRotation != null ? nameRotation : cfg.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(cfg.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = cfg.raw.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(cfg.raw.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var nameMarginLevel = extraParams.nameMarginLevel || 0;\n    var textEl = new graphic.Text({\n      x: pos.x,\n      y: pos.y,\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    local.nameEl = textEl;\n    var nameLayout = sharedRecord.nameLayout = ensureLabelLayoutWithGeometry({\n      label: textEl,\n      priority: textEl.z2,\n      defaultAttr: {\n        ignore: textEl.ignore\n      },\n      marginDefault: isNameLocationCenter(nameLocation)\n      // Make axis name visually far from axis labels.\n      // (but not too aggressive, consider multiple small charts)\n      ? DEFAULT_CENTER_NAME_MARGIN_LEVELS[nameMarginLevel]\n      // top/button margin is set to `0` to inserted the xAxis name into the indention\n      // above the axis labels to save space. (see example below.)\n      : DEFAULT_ENDS_NAME_MARGIN_LEVELS[nameMarginLevel]\n    });\n    sharedRecord.nameLocation = nameLocation;\n    group.add(textEl);\n    textEl.decomposeTransform();\n    if (cfg.shouldNameMoveOverlap && nameLayout) {\n      var record = shared.ensureRecord(axisModel);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(record.labelInfoList);\n      }\n      shared.resolveAxisNameOverlap(cfg, shared, axisModel, nameLayout, nameMoveDirVec, record);\n    }\n  }\n};\nfunction layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, kind) {\n  if (!axisLabelBuildResultExists(local)) {\n    buildAxisLabel(cfg, local, group, kind, axisModel, api);\n  }\n  var labelLayoutList = local.labelLayoutList;\n  updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup);\n  adjustBreakLabels(axisModel, cfg.rotation, labelLayoutList);\n  var optionHideOverlap = cfg.optionHideOverlap;\n  fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap);\n  if (optionHideOverlap) {\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    hideOverlap(\n    // Filter the already ignored labels by the previous overlap resolving methods.\n    filter(labelLayoutList, function (layout) {\n      return layout && !layout.label.ignore;\n    }));\n  }\n  // Always call it even this axis has no name, since it serves in overlapping detection\n  // and grid outerBounds on other axis.\n  resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList);\n}\n;\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\n/**\n * Assume `labelLayoutList` has no `label.ignore: true`.\n * Assume `labelLayoutList` have been sorted by value ascending order.\n */\nfunction fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  // Assert no ignore in labels.\n  function deal(showMinMaxLabel, outmostLabelIdx, innerLabelIdx) {\n    var outmostLabelLayout = ensureLabelLayoutWithGeometry(labelLayoutList[outmostLabelIdx]);\n    var innerLabelLayout = ensureLabelLayoutWithGeometry(labelLayoutList[innerLabelIdx]);\n    if (!outmostLabelLayout || !innerLabelLayout) {\n      return;\n    }\n    if (showMinMaxLabel === false || outmostLabelLayout.suggestIgnore) {\n      ignoreEl(outmostLabelLayout.label);\n      return;\n    }\n    if (innerLabelLayout.suggestIgnore) {\n      ignoreEl(innerLabelLayout.label);\n      return;\n    }\n    // PENDING: Originally we thought `optionHideOverlap === false` means do not hide anything,\n    //  since currently the bounding rect of text might not accurate enough and might slightly bigger,\n    //  which causes false positive. But `optionHideOverlap: null/undfined` is falsy and likely\n    //  be treated as false.\n    // In most fonts the glyph does not reach the boundary of the bounding rect.\n    // This is needed to avoid too aggressive to hide two elements that meet at the edge\n    // due to compact layout by the same bounding rect or OBB.\n    var touchThreshold = 0.1;\n    // This treatment is for backward compatibility. And `!optionHideOverlap` implies that\n    // the user accepts the visual touch between adjacent labels, thus \"hide min/max label\"\n    // should be conservative, since the space might be sufficient in this case.\n    if (!optionHideOverlap) {\n      var marginForce = [0, 0, 0, 0];\n      // Make a copy to apply `ignoreMargin`.\n      outmostLabelLayout = newLabelLayoutWithGeometry({\n        marginForce: marginForce\n      }, outmostLabelLayout);\n      innerLabelLayout = newLabelLayoutWithGeometry({\n        marginForce: marginForce\n      }, innerLabelLayout);\n    }\n    if (labelIntersect(outmostLabelLayout, innerLabelLayout, null, {\n      touchThreshold: touchThreshold\n    })) {\n      if (showMinMaxLabel) {\n        ignoreEl(innerLabelLayout.label);\n      } else {\n        ignoreEl(outmostLabelLayout.label);\n      }\n    }\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  var labelsLen = labelLayoutList.length;\n  deal(showMinLabel, 0, 1);\n  deal(showMaxLabel, labelsLen - 1, labelsLen - 2);\n}\n// PENDING: Is it necessary to display a tick while the corresponding label is ignored?\nfunction syncLabelIgnoreToMajorTicks(cfg, labelLayoutList, tickEls) {\n  if (cfg.showMinorTicks) {\n    // It probably unreaasonable to hide major ticks when show minor ticks.\n    return;\n  }\n  each(labelLayoutList, function (labelLayout) {\n    if (labelLayout && labelLayout.label.ignore) {\n      for (var idx = 0; idx < tickEls.length; idx++) {\n        var tickEl = tickEls[idx];\n        // Assume small array, linear search is fine for performance.\n        // PENDING: measure?\n        var tickInner = getTickInner(tickEl);\n        var labelInner = getLabelInner(labelLayout.label);\n        if (tickInner.tickValue != null && !tickInner.onBand && tickInner.tickValue === labelInner.tickValue) {\n          ignoreEl(tickEl);\n          return;\n        }\n      }\n    }\n  });\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n    var inner = getTickInner(tickEl);\n    inner.onBand = !!ticksCoords[i].onBand;\n    inner.tickValue = ticksCoords[i].tickValue;\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(cfg, group, transformGroup, axisModel) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto') {\n    shown = true;\n    if (cfg.raw.axisTickAutoShow != null) {\n      shown = !!cfg.raw.axisTickAutoShow;\n    }\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return [];\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = cfg.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(cfg, group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!cfg.showMinorTicks || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\n// Return whether need to call `layOutAxisTickLabel` again.\nfunction dealLastTickLabelResultReusable(local, group, extraParams) {\n  if (axisLabelBuildResultExists(local)) {\n    var axisLabelsCreationContext = local.axisLabelsCreationContext;\n    if (process.env.NODE_ENV !== 'production') {\n      assert(local.labelGroup && axisLabelsCreationContext);\n    }\n    var noPxChangeTryDetermine = axisLabelsCreationContext.out.noPxChangeTryDetermine;\n    if (extraParams.noPxChange) {\n      var canDetermine = true;\n      for (var idx = 0; idx < noPxChangeTryDetermine.length; idx++) {\n        canDetermine = canDetermine && noPxChangeTryDetermine[idx]();\n      }\n      if (canDetermine) {\n        return false;\n      }\n    }\n    if (noPxChangeTryDetermine.length) {\n      // Remove the result of `buildAxisLabel`\n      group.remove(local.labelGroup);\n      axisLabelBuildResultSet(local, null, null, null);\n    }\n  }\n  return true;\n}\nfunction buildAxisLabel(cfg, local, group, kind, axisModel, api) {\n  var axis = axisModel.axis;\n  var show = retrieve(cfg.raw.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  var labelGroup = new graphic.Group();\n  group.add(labelGroup);\n  var axisLabelCreationCtx = createAxisLabelsComputingContext(kind);\n  if (!show || axis.scale.isBlank()) {\n    axisLabelBuildResultSet(local, [], labelGroup, axisLabelCreationCtx);\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labels = axis.getViewLabels(axisLabelCreationCtx);\n  // Special label rotate.\n  var labelRotation = (retrieve(cfg.raw.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(cfg.rotation, labelRotation, cfg.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var triggerEvent = axisModel.get('triggerEvent');\n  var z2Min = Infinity;\n  var z2Max = -Infinity;\n  each(labels, function (labelItem, index) {\n    var _a;\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var z2 = 10 + (((_a = labelItem.time) === null || _a === void 0 ? void 0 : _a.level) || 0);\n    z2Min = Math.min(z2Min, z2);\n    z2Max = Math.max(z2Max, z2);\n    var textEl = new graphic.Text({\n      // --- transform props start ---\n      // All of the transform props MUST not be set here, but should be set in\n      // `updateAxisLabelChangableProps`, because they may change in estimation,\n      // and need to calculate based on global coord sys by `decomposeTransform`.\n      x: 0,\n      y: 0,\n      rotation: 0,\n      // --- transform props end ---\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      z2: z2,\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    var inner = getLabelInner(textEl);\n    inner[\"break\"] = labelItem[\"break\"];\n    inner.tickValue = tickValue;\n    inner.layoutRotation = labelLayout.rotation;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (labelItem[\"break\"]) {\n        eventData[\"break\"] = {\n          // type: labelItem.break.type,\n          start: labelItem[\"break\"].parsedBreak.vmin,\n          end: labelItem[\"break\"].parsedBreak.vmax\n        };\n      }\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n      if (labelItem[\"break\"]) {\n        addBreakEventHandler(axisModel, api, textEl, labelItem[\"break\"]);\n      }\n    }\n    labelEls.push(textEl);\n    labelGroup.add(textEl);\n  });\n  var labelLayoutList = map(labelEls, function (label) {\n    return {\n      label: label,\n      priority: getLabelInner(label)[\"break\"] ? label.z2 + (z2Max - z2Min + 1) // Make break labels be highest priority.\n      : label.z2,\n      defaultAttr: {\n        ignore: label.ignore\n      }\n    };\n  });\n  axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelCreationCtx);\n}\n// Indicate that `layOutAxisTickLabel` has been called.\nfunction axisLabelBuildResultExists(local) {\n  return !!local.labelLayoutList;\n}\nfunction axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelsCreationContext) {\n  // Ensure the same lifetime.\n  local.labelLayoutList = labelLayoutList;\n  local.labelGroup = labelGroup;\n  local.axisLabelsCreationContext = axisLabelsCreationContext;\n}\nfunction updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup) {\n  var labelMargin = axisModel.get(['axisLabel', 'margin']);\n  each(labelLayoutList, function (layout, idx) {\n    var geometry = ensureLabelLayoutWithGeometry(layout);\n    if (!geometry) {\n      return;\n    }\n    var labelEl = geometry.label;\n    var inner = getLabelInner(labelEl);\n    // See the comment in `suggestIgnore`.\n    geometry.suggestIgnore = labelEl.ignore;\n    // Currently no `ignore:true` is set in `buildAxisLabel`\n    // But `ignore:true` may be set subsequently for overlap handling, thus reset it here.\n    labelEl.ignore = false;\n    copyTransform(_tmpLayoutEl, _tmpLayoutElReset);\n    _tmpLayoutEl.x = axisModel.axis.dataToCoord(inner.tickValue);\n    _tmpLayoutEl.y = cfg.labelOffset + cfg.labelDirection * labelMargin;\n    _tmpLayoutEl.rotation = inner.layoutRotation;\n    transformGroup.add(_tmpLayoutEl);\n    _tmpLayoutEl.updateTransform();\n    transformGroup.remove(_tmpLayoutEl);\n    _tmpLayoutEl.decomposeTransform();\n    copyTransform(labelEl, _tmpLayoutEl);\n    labelEl.markRedraw();\n    setLabelLayoutDirty(geometry, true);\n    ensureLabelLayoutWithGeometry(geometry);\n  });\n}\nvar _tmpLayoutEl = new graphic.Rect();\nvar _tmpLayoutElReset = new graphic.Rect();\nfunction hasAxisName(axisName) {\n  return !!axisName;\n}\nfunction addBreakEventHandler(axisModel, api, textEl, visualBreak) {\n  textEl.on('click', function (params) {\n    var payload = {\n      type: AXIS_BREAK_EXPAND_ACTION_TYPE,\n      breaks: [{\n        start: visualBreak.parsedBreak.breakOption.start,\n        end: visualBreak.parsedBreak.breakOption.end\n      }]\n    };\n    payload[axisModel.axis.dim + \"AxisIndex\"] = axisModel.componentIndex;\n    api.dispatchAction(payload);\n  });\n}\nfunction adjustBreakLabels(axisModel, axisRotation, labelLayoutList) {\n  var scaleBreakHelper = getScaleBreakHelper();\n  if (!scaleBreakHelper) {\n    return;\n  }\n  var breakLabelIndexPairs = scaleBreakHelper.retrieveAxisBreakPairs(labelLayoutList, function (layoutInfo) {\n    return layoutInfo && getLabelInner(layoutInfo.label)[\"break\"];\n  }, true);\n  var moveOverlap = axisModel.get(['breakLabelLayout', 'moveOverlap'], true);\n  if (moveOverlap === true || moveOverlap === 'auto') {\n    each(breakLabelIndexPairs, function (idxPair) {\n      getAxisBreakHelper().adjustBreakLabelPair(axisModel.axis.inverse, axisRotation, [ensureLabelLayoutWithGeometry(labelLayoutList[idxPair[0]]), ensureLabelLayoutWithGeometry(labelLayoutList[idxPair[1]])]);\n    });\n  }\n}\nexport default AxisBuilder;", "map": {"version": 3, "names": ["retrieve", "defaults", "extend", "each", "isObject", "isString", "isNumber", "isFunction", "retrieve2", "assert", "map", "retrieve3", "filter", "graphic", "getECData", "createTextStyle", "Model", "isRadianAroundZero", "remRadian", "createSymbol", "normalizeSymbolOffset", "matrixUtil", "applyTransform", "v2ApplyTransform", "isNameLocationCenter", "shouldShowAllLabels", "hideOverlap", "labelIntersect", "computeLabelGeometry2", "ensureLabelLayoutWithGeometry", "labelLayoutApplyTranslation", "setLabelLayoutDirty", "newLabelLayoutWithGeometry", "makeInner", "getAxisBreakHelper", "AXIS_BREAK_EXPAND_ACTION_TYPE", "getScaleBreakHelper", "BoundingRect", "Point", "copyTransform", "AxisTickLabelComputingKind", "createAxisLabelsComputingContext", "PI", "Math", "DEFAULT_CENTER_NAME_MARGIN_LEVELS", "DEFAULT_ENDS_NAME_MARGIN_LEVELS", "getLabelInner", "getTickInner", "AxisBuilderSharedContext", "resolveAxisNameOverlap", "recordMap", "prototype", "ensureRecord", "axisModel", "dim", "axis", "idx", "componentIndex", "records", "ready", "resetOverlapRecordToShared", "cfg", "shared", "labelLayoutList", "record", "labelInfoList", "stOccupiedRect", "useStOccupiedRect", "hasAxisName", "axisName", "nameLocation", "layout", "layoutInfo", "label", "ignore", "push", "transGroup", "transform", "invert", "_stTransTmp", "identity", "mul", "copy", "_stLabelRectTmp", "localRect", "union", "sortByDim", "abs", "dirVec", "x", "sortByValue", "sort", "info1", "info2", "extent", "getExtent", "axisLineX", "min", "axisLineWidth", "max", "create", "resolveAxisNameOverlapDefault", "ctx", "nameLayoutInfo", "nameMoveDirVec", "thisRecord", "moveIfOverlap", "moveIfOverlapByLinearLabels", "basedLayoutInfo", "movableLayoutInfo", "moveDirVec", "mtv", "direction", "atan2", "y", "bidirectional", "touchThreshold", "baseLayoutInfoList", "baseDirVec", "sameDir", "dot", "len", "length", "labelInfo", "AxisBuilder", "api", "opt", "group", "Group", "_axisModel", "_api", "_local", "_shared", "_resetCfgDetermined", "updateCfg", "process", "env", "NODE_ENV", "axisLine", "axisTickLabelDetermine", "axisTickLabelEstimate", "raw", "_cfg", "position", "labelOffset", "__getRawCfg", "axisModelDefaultOption", "getDefaultOption", "get", "nameMoveOverlapOption", "defaultNameMoveOverlap", "rotation", "nameDirection", "tickDirection", "labelDirection", "silent", "shouldNameMoveOverlap", "optionHideOverlap", "showMinorTicks", "transformGroup", "updateTransform", "_transformGroup", "cos", "sin", "build", "axisPartNameMap", "extraParams", "_this", "AXIS_BUILDER_AXIS_PART_NAMES", "partName", "builders", "innerTextLayout", "axisRotation", "textRotation", "rotationDiff", "textAlign", "textVerticalAlign", "makeAxisEventDataBase", "eventData", "componentType", "mainType", "isLabelSilent", "tooltipOpt", "show", "local", "shown", "axisLineAutoShow", "matrix", "pt1", "pt2", "inverse", "lineStyle", "lineCap", "getModel", "getLineStyle", "pathBaseProp", "strokeContainThreshold", "z2", "style", "scale", "hasBreaks", "buildAxisBreakLine", "line", "Line", "shape", "x1", "y1", "x2", "y2", "subPixelOptimizeLine", "lineWidth", "anid", "add", "arrows", "arrowSize", "arrowOffset", "symbolWidth_1", "symbolHeight_1", "rotate", "offset", "r", "sqrt", "point", "index", "symbol", "stroke", "pt", "attr", "needCallLayout", "dealLastTickLabelResultReusable", "layOutAxisTickLabel", "estimate", "determine", "ticksEls", "buildAxisMajorTicks", "syncLabelIgnoreToMajorTicks", "buildAxisMinorTicks", "sharedRecord", "nameEl", "remove", "nameLayout", "name", "textStyleModel", "gap", "gapStartEndSignal", "pos", "mt", "nameRotation", "labelLayout", "axisNameAvailableWidth", "endTextLayout", "isFinite", "textFont", "getFont", "truncateOpt", "ellipsis", "max<PERSON><PERSON><PERSON>", "nameTruncateMaxWidth", "nameMarginLevel", "textEl", "Text", "text", "font", "overflow", "width", "fill", "getTextColor", "align", "verticalAlign", "setTooltipConfig", "el", "componentModel", "itemName", "__fullText", "targetType", "priority", "defaultAttr", "<PERSON><PERSON><PERSON><PERSON>", "decomposeTransform", "kind", "axisLabelBuildResultExists", "buildAxisLabel", "updateAxisLabelChangableProps", "adjustBreakLabels", "fixMinMaxLabelShow", "textPosition", "textRotate", "onLeft", "deal", "showMinMaxLabel", "outmostLabelIdx", "innerLabelIdx", "outmostLabelLayout", "innerLabelLayout", "suggestIgnore", "ignoreEl", "marginForce", "showMinLabel", "showMaxLabel", "labelsLen", "tickEls", "tickEl", "tickInner", "labelInner", "tickValue", "onBand", "createTicks", "ticksCoords", "tickTransform", "tickEndCoord", "tickLineStyle", "anidPrefix", "i", "tickCoord", "coord", "autoBatch", "inner", "tickModel", "axisTickAutoShow", "isBlank", "lineStyleModel", "getTicksCoords", "minorTickModel", "minorTicksCoords", "getMinorTicksCoords", "minorTickLineStyle", "minorTicksEls", "k", "axisLabelsCreationContext", "labelGroup", "noPxChangeTryDetermine", "out", "noPxChange", "canDetermine", "axisLabelBuildResultSet", "axisLabelShow", "axisLabelCreationCtx", "labelModel", "labels", "getViewLabels", "labelRotation", "labelRotate", "rawCategoryData", "getCategories", "labelEls", "triggerEvent", "z2Min", "Infinity", "z2Max", "labelItem", "_a", "type", "getRawOrdinalNumber", "formattedLabel", "rawLabel", "itemLabelModel", "rawCategoryItem", "textStyle", "ecModel", "textColor", "getShallow", "alignMin", "alignMax", "verticalAlignMin", "verticalAlignMax", "time", "level", "layoutRotation", "formatterParamsExtra", "isTruncated", "value", "tickIndex", "start", "parsedBreak", "vmin", "end", "vmax", "dataIndex", "addBreakEventHandler", "labelMargin", "geometry", "labelEl", "_tmpLayoutEl", "_tmpLayoutElReset", "dataToCoord", "mark<PERSON><PERSON><PERSON>", "Rect", "visualBreak", "on", "params", "payload", "breaks", "breakOption", "dispatchAction", "scaleBreakHelper", "breakLabelIndexPairs", "retrieveAxisBreakPairs", "moveOverlap", "idxPair", "adjustBreakLabelPair"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/axis/AxisBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { retrieve, defaults, extend, each, isObject, isString, isNumber, isFunction, retrieve2, assert, map, retrieve3, filter } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { isNameLocationCenter, shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { hideOverlap, labelIntersect, computeLabelGeometry2, ensureLabelLayoutWithGeometry, labelLayoutApplyTranslation, setLabelLayoutDirty, newLabelLayoutWithGeometry } from '../../label/labelLayoutHelper.js';\nimport { makeInner } from '../../util/model.js';\nimport { getAxisBreakHelper } from './axisBreakHelper.js';\nimport { AXIS_BREAK_EXPAND_ACTION_TYPE } from './axisAction.js';\nimport { getScaleBreakHelper } from '../../scale/break.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport { copyTransform } from 'zrender/lib/core/Transformable.js';\nimport { AxisTickLabelComputingKind, createAxisLabelsComputingContext } from '../../coord/axisTickLabelBuilder.js';\nvar PI = Math.PI;\nvar DEFAULT_CENTER_NAME_MARGIN_LEVELS = [[1, 2, 1, 2], [5, 3, 5, 3], [8, 3, 8, 3]];\nvar DEFAULT_ENDS_NAME_MARGIN_LEVELS = [[0, 1, 0, 1], [0, 3, 0, 3], [0, 3, 0, 3]];\nexport var getLabelInner = makeInner();\nvar getTickInner = makeInner();\n/**\n * A context shared by difference axisBuilder instances.\n * For cross-axes overlap resolving.\n *\n * Lifecycle constraint: should not over a pass of ec main process.\n *  If model is changed, the context must be disposed.\n *\n * @see AxisBuilderLocalContext\n */\nvar AxisBuilderSharedContext = /** @class */function () {\n  function AxisBuilderSharedContext(resolveAxisNameOverlap) {\n    /**\n     * [CAUTION] Do not modify this data structure outside this class.\n     */\n    this.recordMap = {};\n    this.resolveAxisNameOverlap = resolveAxisNameOverlap;\n  }\n  AxisBuilderSharedContext.prototype.ensureRecord = function (axisModel) {\n    var dim = axisModel.axis.dim;\n    var idx = axisModel.componentIndex;\n    var recordMap = this.recordMap;\n    var records = recordMap[dim] || (recordMap[dim] = []);\n    return records[idx] || (records[idx] = {\n      ready: {}\n    });\n  };\n  return AxisBuilderSharedContext;\n}();\nexport { AxisBuilderSharedContext };\n;\n/**\n * [CAUTION]\n *  1. The call of this function must be after axisLabel overlap handlings\n *     (such as `hideOverlap`, `fixMinMaxLabelShow`) and after transform calculating.\n *  2. Can be called multiple times and should be idempotent.\n */\nfunction resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList) {\n  var axis = axisModel.axis;\n  var record = shared.ensureRecord(axisModel);\n  var labelInfoList = [];\n  var stOccupiedRect;\n  var useStOccupiedRect = hasAxisName(cfg.axisName) && isNameLocationCenter(cfg.nameLocation);\n  each(labelLayoutList, function (layout) {\n    var layoutInfo = ensureLabelLayoutWithGeometry(layout);\n    if (!layoutInfo || layoutInfo.label.ignore) {\n      return;\n    }\n    labelInfoList.push(layoutInfo);\n    var transGroup = record.transGroup;\n    if (useStOccupiedRect) {\n      // Transform to \"standard axis\" for creating stOccupiedRect (the label rects union).\n      transGroup.transform ? matrixUtil.invert(_stTransTmp, transGroup.transform) : matrixUtil.identity(_stTransTmp);\n      if (layoutInfo.transform) {\n        matrixUtil.mul(_stTransTmp, _stTransTmp, layoutInfo.transform);\n      }\n      BoundingRect.copy(_stLabelRectTmp, layoutInfo.localRect);\n      _stLabelRectTmp.applyTransform(_stTransTmp);\n      stOccupiedRect ? stOccupiedRect.union(_stLabelRectTmp) : BoundingRect.copy(stOccupiedRect = new BoundingRect(0, 0, 0, 0), _stLabelRectTmp);\n    }\n  });\n  var sortByDim = Math.abs(record.dirVec.x) > 0.1 ? 'x' : 'y';\n  var sortByValue = record.transGroup[sortByDim];\n  labelInfoList.sort(function (info1, info2) {\n    return Math.abs(info1.label[sortByDim] - sortByValue) - Math.abs(info2.label[sortByDim] - sortByValue);\n  });\n  if (useStOccupiedRect && stOccupiedRect) {\n    var extent = axis.getExtent();\n    var axisLineX = Math.min(extent[0], extent[1]);\n    var axisLineWidth = Math.max(extent[0], extent[1]) - axisLineX;\n    // If `nameLocation` is 'middle', enlarge axis labels boundingRect to axisLine to avoid bad\n    //  case like that axis name is placed in the gap between axis labels and axis line.\n    // If only one label exists, the entire band should be occupied for\n    // visual consistency, so extent it to [0, canvas width].\n    stOccupiedRect.union(new BoundingRect(axisLineX, 0, axisLineWidth, 1));\n  }\n  record.stOccupiedRect = stOccupiedRect;\n  record.labelInfoList = labelInfoList;\n}\nvar _stTransTmp = matrixUtil.create();\nvar _stLabelRectTmp = new BoundingRect(0, 0, 0, 0);\n/**\n * The default resolver does not involve other axes within the same coordinate system.\n */\nexport var resolveAxisNameOverlapDefault = function (cfg, ctx, axisModel, nameLayoutInfo, nameMoveDirVec, thisRecord) {\n  if (isNameLocationCenter(cfg.nameLocation)) {\n    var stOccupiedRect = thisRecord.stOccupiedRect;\n    if (stOccupiedRect) {\n      moveIfOverlap(computeLabelGeometry2({}, stOccupiedRect, thisRecord.transGroup.transform), nameLayoutInfo, nameMoveDirVec);\n    }\n  } else {\n    moveIfOverlapByLinearLabels(thisRecord.labelInfoList, thisRecord.dirVec, nameLayoutInfo, nameMoveDirVec);\n  }\n};\n// [NOTICE] not consider ignore.\nfunction moveIfOverlap(basedLayoutInfo, movableLayoutInfo, moveDirVec) {\n  var mtv = new Point();\n  if (labelIntersect(basedLayoutInfo, movableLayoutInfo, mtv, {\n    direction: Math.atan2(moveDirVec.y, moveDirVec.x),\n    bidirectional: false,\n    touchThreshold: 0.05\n  })) {\n    labelLayoutApplyTranslation(movableLayoutInfo, mtv);\n  }\n}\nexport function moveIfOverlapByLinearLabels(baseLayoutInfoList, baseDirVec, movableLayoutInfo, moveDirVec) {\n  // Detect and move from far to close.\n  var sameDir = Point.dot(moveDirVec, baseDirVec) >= 0;\n  for (var idx = 0, len = baseLayoutInfoList.length; idx < len; idx++) {\n    var labelInfo = baseLayoutInfoList[sameDir ? idx : len - 1 - idx];\n    if (!labelInfo.label.ignore) {\n      moveIfOverlap(labelInfo, movableLayoutInfo, moveDirVec);\n    }\n  }\n}\n/**\n * @caution\n * - Ensure it is called after the data processing stage finished.\n * - It might be called before `CahrtView#render`, sush as called at `CoordinateSystem#update`,\n *  thus ensure the result the same whenever it is called.\n *\n * A builder for a straight-line axis.\n *\n * A final axis is translated and rotated from a \"standard axis\".\n * So opt.position and opt.rotation is required.\n *\n * A \"standard axis\" is the axis [0,0]-->[abs(axisExtent[1]-axisExtent[0]),0]\n * for example: [0,0]-->[50,0]\n */\nvar AxisBuilder = /** @class */function () {\n  /**\n   * [CAUTION]: axisModel.axis.extent/scale must be ready to use.\n   */\n  function AxisBuilder(axisModel, api, opt, shared) {\n    this.group = new graphic.Group();\n    this._axisModel = axisModel;\n    this._api = api;\n    this._local = {};\n    this._shared = shared || new AxisBuilderSharedContext(resolveAxisNameOverlapDefault);\n    this._resetCfgDetermined(opt);\n  }\n  /**\n   * Regarding axis label related configurations, only the change of label.x/y is supported; other\n   * changes are not necessary and not performant. To be specific, only `axis.position`\n   * (and consequently `labelOffset`) and `axis.extent` can be changed, and assume everything in\n   * `axisModel` are not changed.\n   * Axis line related configurations can be changed since this method can only be called\n   * before they are created.\n   */\n  AxisBuilder.prototype.updateCfg = function (opt) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = this._shared.ensureRecord(this._axisModel).ready;\n      // After that, changing cfg is not supported; avoid unnecessary complexity.\n      assert(!ready.axisLine && !ready.axisTickLabelDetermine);\n      // Have to be called again if cfg changed.\n      ready.axisName = ready.axisTickLabelEstimate = false;\n    }\n    var raw = this._cfg.raw;\n    raw.position = opt.position;\n    raw.labelOffset = opt.labelOffset;\n    this._resetCfgDetermined(raw);\n  };\n  /**\n   * [CAUTION] For debug usage. Never change it outside!\n   */\n  AxisBuilder.prototype.__getRawCfg = function () {\n    return this._cfg.raw;\n  };\n  AxisBuilder.prototype._resetCfgDetermined = function (raw) {\n    var axisModel = this._axisModel;\n    // FIXME:\n    //  Currently there is no uniformed way to set default values if an option\n    //  is specified null/undefined by user (intentionally or unintentionally),\n    //  e.g. null/undefined is not a illegal value for `nameLocation`.\n    //  Try to use `getDefaultOption` to address it. But radar has no `getDefaultOption`.\n    var axisModelDefaultOption = axisModel.getDefaultOption ? axisModel.getDefaultOption() : {};\n    // Default value\n    var axisName = retrieve2(raw.axisName, axisModel.get('name'));\n    var nameMoveOverlapOption = axisModel.get('nameMoveOverlap');\n    if (nameMoveOverlapOption == null || nameMoveOverlapOption === 'auto') {\n      nameMoveOverlapOption = retrieve2(raw.defaultNameMoveOverlap, true);\n    }\n    var cfg = {\n      raw: raw,\n      position: raw.position,\n      rotation: raw.rotation,\n      nameDirection: retrieve2(raw.nameDirection, 1),\n      tickDirection: retrieve2(raw.tickDirection, 1),\n      labelDirection: retrieve2(raw.labelDirection, 1),\n      labelOffset: retrieve2(raw.labelOffset, 0),\n      silent: retrieve2(raw.silent, true),\n      axisName: axisName,\n      nameLocation: retrieve3(axisModel.get('nameLocation'), axisModelDefaultOption.nameLocation, 'end'),\n      shouldNameMoveOverlap: hasAxisName(axisName) && nameMoveOverlapOption,\n      optionHideOverlap: axisModel.get(['axisLabel', 'hideOverlap']),\n      showMinorTicks: axisModel.get(['minorTick', 'show'])\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      assert(cfg.position != null);\n      assert(cfg.rotation != null);\n    }\n    this._cfg = cfg;\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: cfg.position[0],\n      y: cfg.position[1],\n      rotation: cfg.rotation\n    });\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n    var record = this._shared.ensureRecord(axisModel);\n    record.transGroup = this._transformGroup;\n    record.dirVec = new Point(Math.cos(-cfg.rotation), Math.sin(-cfg.rotation));\n  };\n  AxisBuilder.prototype.build = function (axisPartNameMap, extraParams) {\n    var _this = this;\n    if (!axisPartNameMap) {\n      axisPartNameMap = {\n        axisLine: true,\n        axisTickLabelEstimate: false,\n        axisTickLabelDetermine: true,\n        axisName: true\n      };\n    }\n    each(AXIS_BUILDER_AXIS_PART_NAMES, function (partName) {\n      if (axisPartNameMap[partName]) {\n        builders[partName](_this._cfg, _this._local, _this._shared, _this._axisModel, _this.group, _this._transformGroup, _this._api, extraParams || {});\n      }\n    });\n    return this;\n  };\n  /**\n   * Currently only get text align/verticalAlign by rotation.\n   * NO `position` is involved, otherwise it have to be performed for each `updateAxisLabelChangableProps`.\n   */\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\n// Sorted by dependency order.\nvar AXIS_BUILDER_AXIS_PART_NAMES = ['axisLine', 'axisTickLabelEstimate', 'axisTickLabelDetermine', 'axisName'];\nvar builders = {\n  axisLine: function (cfg, local, shared, axisModel, group, transformGroup, api) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      assert(!ready.axisLine);\n      ready.axisLine = true;\n    }\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto') {\n      shown = true;\n      if (cfg.raw.axisLineAutoShow != null) {\n        shown = !!cfg.raw.axisLineAutoShow;\n      }\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var pathBaseProp = {\n      strokeContainThreshold: cfg.raw.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1,\n      style: lineStyle\n    };\n    if (axisModel.get(['axisLine', 'breakLine']) && axisModel.axis.scale.hasBreaks()) {\n      getAxisBreakHelper().buildAxisBreakLine(axisModel, group, transformGroup, pathBaseProp);\n    } else {\n      var line = new graphic.Line(extend({\n        shape: {\n          x1: pt1[0],\n          y1: pt1[1],\n          x2: pt2[0],\n          y2: pt2[1]\n        }\n      }, pathBaseProp));\n      graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n      line.anid = 'line';\n      group.add(line);\n    }\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: cfg.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: cfg.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(cfg.rotation),\n            y: pt[1] - r * Math.sin(cfg.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  /**\n   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called\n   *  in size measurement. Thus this method should be idempotent, and should be performant.\n   */\n  axisTickLabelEstimate: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      assert(!ready.axisTickLabelDetermine);\n      ready.axisTickLabelEstimate = true;\n    }\n    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);\n    if (needCallLayout) {\n      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, AxisTickLabelComputingKind.estimate);\n    }\n  },\n  /**\n   * Finish axis tick label build.\n   * Can be only called once.\n   */\n  axisTickLabelDetermine: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = shared.ensureRecord(axisModel).ready;\n      ready.axisTickLabelDetermine = true;\n    }\n    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);\n    if (needCallLayout) {\n      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, AxisTickLabelComputingKind.determine);\n    }\n    var ticksEls = buildAxisMajorTicks(cfg, group, transformGroup, axisModel);\n    syncLabelIgnoreToMajorTicks(cfg, local.labelLayoutList, ticksEls);\n    buildAxisMinorTicks(cfg, group, transformGroup, axisModel, cfg.tickDirection);\n  },\n  /**\n   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called\n   *  in size measurement. Thus this method should be idempotent, and should be performant.\n   */\n  axisName: function (cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {\n    var sharedRecord = shared.ensureRecord(axisModel);\n    if (process.env.NODE_ENV !== 'production') {\n      var ready = sharedRecord.ready;\n      assert(ready.axisTickLabelEstimate || ready.axisTickLabelDetermine);\n      ready.axisName = true;\n    }\n    // Remove the existing name result created in estimation phase.\n    if (local.nameEl) {\n      group.remove(local.nameEl);\n      local.nameEl = sharedRecord.nameLayout = sharedRecord.nameLocation = null;\n    }\n    var name = cfg.axisName;\n    if (!hasAxisName(name)) {\n      return;\n    }\n    var nameLocation = cfg.nameLocation;\n    var nameDirection = cfg.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapStartEndSignal = axisModel.axis.inverse ? -1 : 1;\n    var pos = new Point(0, 0);\n    var nameMoveDirVec = new Point(0, 0);\n    if (nameLocation === 'start') {\n      pos.x = extent[0] - gapStartEndSignal * gap;\n      nameMoveDirVec.x = -gapStartEndSignal;\n    } else if (nameLocation === 'end') {\n      pos.x = extent[1] + gapStartEndSignal * gap;\n      nameMoveDirVec.x = gapStartEndSignal;\n    } else {\n      // 'middle' or 'center'\n      pos.x = (extent[0] + extent[1]) / 2;\n      pos.y = cfg.labelOffset + nameDirection * gap;\n      nameMoveDirVec.y = nameDirection;\n    }\n    var mt = matrixUtil.create();\n    nameMoveDirVec.transform(matrixUtil.rotate(mt, mt, cfg.rotation));\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var labelLayout;\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(cfg.rotation, nameRotation != null ? nameRotation : cfg.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(cfg.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = cfg.raw.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(cfg.raw.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var nameMarginLevel = extraParams.nameMarginLevel || 0;\n    var textEl = new graphic.Text({\n      x: pos.x,\n      y: pos.y,\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    local.nameEl = textEl;\n    var nameLayout = sharedRecord.nameLayout = ensureLabelLayoutWithGeometry({\n      label: textEl,\n      priority: textEl.z2,\n      defaultAttr: {\n        ignore: textEl.ignore\n      },\n      marginDefault: isNameLocationCenter(nameLocation)\n      // Make axis name visually far from axis labels.\n      // (but not too aggressive, consider multiple small charts)\n      ? DEFAULT_CENTER_NAME_MARGIN_LEVELS[nameMarginLevel]\n      // top/button margin is set to `0` to inserted the xAxis name into the indention\n      // above the axis labels to save space. (see example below.)\n      : DEFAULT_ENDS_NAME_MARGIN_LEVELS[nameMarginLevel]\n    });\n    sharedRecord.nameLocation = nameLocation;\n    group.add(textEl);\n    textEl.decomposeTransform();\n    if (cfg.shouldNameMoveOverlap && nameLayout) {\n      var record = shared.ensureRecord(axisModel);\n      if (process.env.NODE_ENV !== 'production') {\n        assert(record.labelInfoList);\n      }\n      shared.resolveAxisNameOverlap(cfg, shared, axisModel, nameLayout, nameMoveDirVec, record);\n    }\n  }\n};\nfunction layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, kind) {\n  if (!axisLabelBuildResultExists(local)) {\n    buildAxisLabel(cfg, local, group, kind, axisModel, api);\n  }\n  var labelLayoutList = local.labelLayoutList;\n  updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup);\n  adjustBreakLabels(axisModel, cfg.rotation, labelLayoutList);\n  var optionHideOverlap = cfg.optionHideOverlap;\n  fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap);\n  if (optionHideOverlap) {\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    hideOverlap(\n    // Filter the already ignored labels by the previous overlap resolving methods.\n    filter(labelLayoutList, function (layout) {\n      return layout && !layout.label.ignore;\n    }));\n  }\n  // Always call it even this axis has no name, since it serves in overlapping detection\n  // and grid outerBounds on other axis.\n  resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList);\n}\n;\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\n/**\n * Assume `labelLayoutList` has no `label.ignore: true`.\n * Assume `labelLayoutList` have been sorted by value ascending order.\n */\nfunction fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  // Assert no ignore in labels.\n  function deal(showMinMaxLabel, outmostLabelIdx, innerLabelIdx) {\n    var outmostLabelLayout = ensureLabelLayoutWithGeometry(labelLayoutList[outmostLabelIdx]);\n    var innerLabelLayout = ensureLabelLayoutWithGeometry(labelLayoutList[innerLabelIdx]);\n    if (!outmostLabelLayout || !innerLabelLayout) {\n      return;\n    }\n    if (showMinMaxLabel === false || outmostLabelLayout.suggestIgnore) {\n      ignoreEl(outmostLabelLayout.label);\n      return;\n    }\n    if (innerLabelLayout.suggestIgnore) {\n      ignoreEl(innerLabelLayout.label);\n      return;\n    }\n    // PENDING: Originally we thought `optionHideOverlap === false` means do not hide anything,\n    //  since currently the bounding rect of text might not accurate enough and might slightly bigger,\n    //  which causes false positive. But `optionHideOverlap: null/undfined` is falsy and likely\n    //  be treated as false.\n    // In most fonts the glyph does not reach the boundary of the bounding rect.\n    // This is needed to avoid too aggressive to hide two elements that meet at the edge\n    // due to compact layout by the same bounding rect or OBB.\n    var touchThreshold = 0.1;\n    // This treatment is for backward compatibility. And `!optionHideOverlap` implies that\n    // the user accepts the visual touch between adjacent labels, thus \"hide min/max label\"\n    // should be conservative, since the space might be sufficient in this case.\n    if (!optionHideOverlap) {\n      var marginForce = [0, 0, 0, 0];\n      // Make a copy to apply `ignoreMargin`.\n      outmostLabelLayout = newLabelLayoutWithGeometry({\n        marginForce: marginForce\n      }, outmostLabelLayout);\n      innerLabelLayout = newLabelLayoutWithGeometry({\n        marginForce: marginForce\n      }, innerLabelLayout);\n    }\n    if (labelIntersect(outmostLabelLayout, innerLabelLayout, null, {\n      touchThreshold: touchThreshold\n    })) {\n      if (showMinMaxLabel) {\n        ignoreEl(innerLabelLayout.label);\n      } else {\n        ignoreEl(outmostLabelLayout.label);\n      }\n    }\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  var labelsLen = labelLayoutList.length;\n  deal(showMinLabel, 0, 1);\n  deal(showMaxLabel, labelsLen - 1, labelsLen - 2);\n}\n// PENDING: Is it necessary to display a tick while the corresponding label is ignored?\nfunction syncLabelIgnoreToMajorTicks(cfg, labelLayoutList, tickEls) {\n  if (cfg.showMinorTicks) {\n    // It probably unreaasonable to hide major ticks when show minor ticks.\n    return;\n  }\n  each(labelLayoutList, function (labelLayout) {\n    if (labelLayout && labelLayout.label.ignore) {\n      for (var idx = 0; idx < tickEls.length; idx++) {\n        var tickEl = tickEls[idx];\n        // Assume small array, linear search is fine for performance.\n        // PENDING: measure?\n        var tickInner = getTickInner(tickEl);\n        var labelInner = getLabelInner(labelLayout.label);\n        if (tickInner.tickValue != null && !tickInner.onBand && tickInner.tickValue === labelInner.tickValue) {\n          ignoreEl(tickEl);\n          return;\n        }\n      }\n    }\n  });\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n    var inner = getTickInner(tickEl);\n    inner.onBand = !!ticksCoords[i].onBand;\n    inner.tickValue = ticksCoords[i].tickValue;\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(cfg, group, transformGroup, axisModel) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto') {\n    shown = true;\n    if (cfg.raw.axisTickAutoShow != null) {\n      shown = !!cfg.raw.axisTickAutoShow;\n    }\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return [];\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = cfg.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(cfg, group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!cfg.showMinorTicks || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\n// Return whether need to call `layOutAxisTickLabel` again.\nfunction dealLastTickLabelResultReusable(local, group, extraParams) {\n  if (axisLabelBuildResultExists(local)) {\n    var axisLabelsCreationContext = local.axisLabelsCreationContext;\n    if (process.env.NODE_ENV !== 'production') {\n      assert(local.labelGroup && axisLabelsCreationContext);\n    }\n    var noPxChangeTryDetermine = axisLabelsCreationContext.out.noPxChangeTryDetermine;\n    if (extraParams.noPxChange) {\n      var canDetermine = true;\n      for (var idx = 0; idx < noPxChangeTryDetermine.length; idx++) {\n        canDetermine = canDetermine && noPxChangeTryDetermine[idx]();\n      }\n      if (canDetermine) {\n        return false;\n      }\n    }\n    if (noPxChangeTryDetermine.length) {\n      // Remove the result of `buildAxisLabel`\n      group.remove(local.labelGroup);\n      axisLabelBuildResultSet(local, null, null, null);\n    }\n  }\n  return true;\n}\nfunction buildAxisLabel(cfg, local, group, kind, axisModel, api) {\n  var axis = axisModel.axis;\n  var show = retrieve(cfg.raw.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  var labelGroup = new graphic.Group();\n  group.add(labelGroup);\n  var axisLabelCreationCtx = createAxisLabelsComputingContext(kind);\n  if (!show || axis.scale.isBlank()) {\n    axisLabelBuildResultSet(local, [], labelGroup, axisLabelCreationCtx);\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labels = axis.getViewLabels(axisLabelCreationCtx);\n  // Special label rotate.\n  var labelRotation = (retrieve(cfg.raw.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(cfg.rotation, labelRotation, cfg.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var triggerEvent = axisModel.get('triggerEvent');\n  var z2Min = Infinity;\n  var z2Max = -Infinity;\n  each(labels, function (labelItem, index) {\n    var _a;\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var z2 = 10 + (((_a = labelItem.time) === null || _a === void 0 ? void 0 : _a.level) || 0);\n    z2Min = Math.min(z2Min, z2);\n    z2Max = Math.max(z2Max, z2);\n    var textEl = new graphic.Text({\n      // --- transform props start ---\n      // All of the transform props MUST not be set here, but should be set in\n      // `updateAxisLabelChangableProps`, because they may change in estimation,\n      // and need to calculate based on global coord sys by `decomposeTransform`.\n      x: 0,\n      y: 0,\n      rotation: 0,\n      // --- transform props end ---\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      z2: z2,\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    var inner = getLabelInner(textEl);\n    inner[\"break\"] = labelItem[\"break\"];\n    inner.tickValue = tickValue;\n    inner.layoutRotation = labelLayout.rotation;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (labelItem[\"break\"]) {\n        eventData[\"break\"] = {\n          // type: labelItem.break.type,\n          start: labelItem[\"break\"].parsedBreak.vmin,\n          end: labelItem[\"break\"].parsedBreak.vmax\n        };\n      }\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n      if (labelItem[\"break\"]) {\n        addBreakEventHandler(axisModel, api, textEl, labelItem[\"break\"]);\n      }\n    }\n    labelEls.push(textEl);\n    labelGroup.add(textEl);\n  });\n  var labelLayoutList = map(labelEls, function (label) {\n    return {\n      label: label,\n      priority: getLabelInner(label)[\"break\"] ? label.z2 + (z2Max - z2Min + 1) // Make break labels be highest priority.\n      : label.z2,\n      defaultAttr: {\n        ignore: label.ignore\n      }\n    };\n  });\n  axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelCreationCtx);\n}\n// Indicate that `layOutAxisTickLabel` has been called.\nfunction axisLabelBuildResultExists(local) {\n  return !!local.labelLayoutList;\n}\nfunction axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelsCreationContext) {\n  // Ensure the same lifetime.\n  local.labelLayoutList = labelLayoutList;\n  local.labelGroup = labelGroup;\n  local.axisLabelsCreationContext = axisLabelsCreationContext;\n}\nfunction updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup) {\n  var labelMargin = axisModel.get(['axisLabel', 'margin']);\n  each(labelLayoutList, function (layout, idx) {\n    var geometry = ensureLabelLayoutWithGeometry(layout);\n    if (!geometry) {\n      return;\n    }\n    var labelEl = geometry.label;\n    var inner = getLabelInner(labelEl);\n    // See the comment in `suggestIgnore`.\n    geometry.suggestIgnore = labelEl.ignore;\n    // Currently no `ignore:true` is set in `buildAxisLabel`\n    // But `ignore:true` may be set subsequently for overlap handling, thus reset it here.\n    labelEl.ignore = false;\n    copyTransform(_tmpLayoutEl, _tmpLayoutElReset);\n    _tmpLayoutEl.x = axisModel.axis.dataToCoord(inner.tickValue);\n    _tmpLayoutEl.y = cfg.labelOffset + cfg.labelDirection * labelMargin;\n    _tmpLayoutEl.rotation = inner.layoutRotation;\n    transformGroup.add(_tmpLayoutEl);\n    _tmpLayoutEl.updateTransform();\n    transformGroup.remove(_tmpLayoutEl);\n    _tmpLayoutEl.decomposeTransform();\n    copyTransform(labelEl, _tmpLayoutEl);\n    labelEl.markRedraw();\n    setLabelLayoutDirty(geometry, true);\n    ensureLabelLayoutWithGeometry(geometry);\n  });\n}\nvar _tmpLayoutEl = new graphic.Rect();\nvar _tmpLayoutElReset = new graphic.Rect();\nfunction hasAxisName(axisName) {\n  return !!axisName;\n}\nfunction addBreakEventHandler(axisModel, api, textEl, visualBreak) {\n  textEl.on('click', function (params) {\n    var payload = {\n      type: AXIS_BREAK_EXPAND_ACTION_TYPE,\n      breaks: [{\n        start: visualBreak.parsedBreak.breakOption.start,\n        end: visualBreak.parsedBreak.breakOption.end\n      }]\n    };\n    payload[axisModel.axis.dim + \"AxisIndex\"] = axisModel.componentIndex;\n    api.dispatchAction(payload);\n  });\n}\nfunction adjustBreakLabels(axisModel, axisRotation, labelLayoutList) {\n  var scaleBreakHelper = getScaleBreakHelper();\n  if (!scaleBreakHelper) {\n    return;\n  }\n  var breakLabelIndexPairs = scaleBreakHelper.retrieveAxisBreakPairs(labelLayoutList, function (layoutInfo) {\n    return layoutInfo && getLabelInner(layoutInfo.label)[\"break\"];\n  }, true);\n  var moveOverlap = axisModel.get(['breakLabelLayout', 'moveOverlap'], true);\n  if (moveOverlap === true || moveOverlap === 'auto') {\n    each(breakLabelIndexPairs, function (idxPair) {\n      getAxisBreakHelper().adjustBreakLabelPair(axisModel.axis.inverse, axisRotation, [ensureLabelLayoutWithGeometry(labelLayoutList[idxPair[0]]), ensureLabelLayoutWithGeometry(labelLayoutList[idxPair[1]])]);\n    });\n  }\n}\nexport default AxisBuilder;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,0BAA0B;AAChK,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,sBAAsB;AACpE,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC1E,OAAO,KAAKC,UAAU,MAAM,4BAA4B;AACxD,SAASC,cAAc,IAAIC,gBAAgB,QAAQ,4BAA4B;AAC/E,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,2BAA2B;AACrF,SAASC,WAAW,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,6BAA6B,EAAEC,2BAA2B,EAAEC,mBAAmB,EAAEC,0BAA0B,QAAQ,kCAAkC;AAClN,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,6BAA6B,QAAQ,iBAAiB;AAC/D,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,0BAA0B,EAAEC,gCAAgC,QAAQ,qCAAqC;AAClH,IAAIC,EAAE,GAAGC,IAAI,CAACD,EAAE;AAChB,IAAIE,iCAAiC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClF,IAAIC,+BAA+B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,OAAO,IAAIC,aAAa,GAAGb,SAAS,CAAC,CAAC;AACtC,IAAIc,YAAY,GAAGd,SAAS,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIe,wBAAwB,GAAG,aAAa,YAAY;EACtD,SAASA,wBAAwBA,CAACC,sBAAsB,EAAE;IACxD;AACJ;AACA;IACI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACD,sBAAsB,GAAGA,sBAAsB;EACtD;EACAD,wBAAwB,CAACG,SAAS,CAACC,YAAY,GAAG,UAAUC,SAAS,EAAE;IACrE,IAAIC,GAAG,GAAGD,SAAS,CAACE,IAAI,CAACD,GAAG;IAC5B,IAAIE,GAAG,GAAGH,SAAS,CAACI,cAAc;IAClC,IAAIP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAIQ,OAAO,GAAGR,SAAS,CAACI,GAAG,CAAC,KAAKJ,SAAS,CAACI,GAAG,CAAC,GAAG,EAAE,CAAC;IACrD,OAAOI,OAAO,CAACF,GAAG,CAAC,KAAKE,OAAO,CAACF,GAAG,CAAC,GAAG;MACrCG,KAAK,EAAE,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EACD,OAAOX,wBAAwB;AACjC,CAAC,CAAC,CAAC;AACH,SAASA,wBAAwB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,0BAA0BA,CAACC,GAAG,EAAEC,MAAM,EAAET,SAAS,EAAEU,eAAe,EAAE;EAC3E,IAAIR,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAIS,MAAM,GAAGF,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC;EAC3C,IAAIY,aAAa,GAAG,EAAE;EACtB,IAAIC,cAAc;EAClB,IAAIC,iBAAiB,GAAGC,WAAW,CAACP,GAAG,CAACQ,QAAQ,CAAC,IAAI7C,oBAAoB,CAACqC,GAAG,CAACS,YAAY,CAAC;EAC3FnE,IAAI,CAAC4D,eAAe,EAAE,UAAUQ,MAAM,EAAE;IACtC,IAAIC,UAAU,GAAG3C,6BAA6B,CAAC0C,MAAM,CAAC;IACtD,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,KAAK,CAACC,MAAM,EAAE;MAC1C;IACF;IACAT,aAAa,CAACU,IAAI,CAACH,UAAU,CAAC;IAC9B,IAAII,UAAU,GAAGZ,MAAM,CAACY,UAAU;IAClC,IAAIT,iBAAiB,EAAE;MACrB;MACAS,UAAU,CAACC,SAAS,GAAGxD,UAAU,CAACyD,MAAM,CAACC,WAAW,EAAEH,UAAU,CAACC,SAAS,CAAC,GAAGxD,UAAU,CAAC2D,QAAQ,CAACD,WAAW,CAAC;MAC9G,IAAIP,UAAU,CAACK,SAAS,EAAE;QACxBxD,UAAU,CAAC4D,GAAG,CAACF,WAAW,EAAEA,WAAW,EAAEP,UAAU,CAACK,SAAS,CAAC;MAChE;MACAxC,YAAY,CAAC6C,IAAI,CAACC,eAAe,EAAEX,UAAU,CAACY,SAAS,CAAC;MACxDD,eAAe,CAAC7D,cAAc,CAACyD,WAAW,CAAC;MAC3Cb,cAAc,GAAGA,cAAc,CAACmB,KAAK,CAACF,eAAe,CAAC,GAAG9C,YAAY,CAAC6C,IAAI,CAAChB,cAAc,GAAG,IAAI7B,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE8C,eAAe,CAAC;IAC5I;EACF,CAAC,CAAC;EACF,IAAIG,SAAS,GAAG3C,IAAI,CAAC4C,GAAG,CAACvB,MAAM,CAACwB,MAAM,CAACC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;EAC3D,IAAIC,WAAW,GAAG1B,MAAM,CAACY,UAAU,CAACU,SAAS,CAAC;EAC9CrB,aAAa,CAAC0B,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACzC,OAAOlD,IAAI,CAAC4C,GAAG,CAACK,KAAK,CAACnB,KAAK,CAACa,SAAS,CAAC,GAAGI,WAAW,CAAC,GAAG/C,IAAI,CAAC4C,GAAG,CAACM,KAAK,CAACpB,KAAK,CAACa,SAAS,CAAC,GAAGI,WAAW,CAAC;EACxG,CAAC,CAAC;EACF,IAAIvB,iBAAiB,IAAID,cAAc,EAAE;IACvC,IAAI4B,MAAM,GAAGvC,IAAI,CAACwC,SAAS,CAAC,CAAC;IAC7B,IAAIC,SAAS,GAAGrD,IAAI,CAACsD,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9C,IAAII,aAAa,GAAGvD,IAAI,CAACwD,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGE,SAAS;IAC9D;IACA;IACA;IACA;IACA9B,cAAc,CAACmB,KAAK,CAAC,IAAIhD,YAAY,CAAC2D,SAAS,EAAE,CAAC,EAAEE,aAAa,EAAE,CAAC,CAAC,CAAC;EACxE;EACAlC,MAAM,CAACE,cAAc,GAAGA,cAAc;EACtCF,MAAM,CAACC,aAAa,GAAGA,aAAa;AACtC;AACA,IAAIc,WAAW,GAAG1D,UAAU,CAAC+E,MAAM,CAAC,CAAC;AACrC,IAAIjB,eAAe,GAAG,IAAI9C,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD;AACA;AACA;AACA,OAAO,IAAIgE,6BAA6B,GAAG,SAAAA,CAAUxC,GAAG,EAAEyC,GAAG,EAAEjD,SAAS,EAAEkD,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAE;EACpH,IAAIjF,oBAAoB,CAACqC,GAAG,CAACS,YAAY,CAAC,EAAE;IAC1C,IAAIJ,cAAc,GAAGuC,UAAU,CAACvC,cAAc;IAC9C,IAAIA,cAAc,EAAE;MAClBwC,aAAa,CAAC9E,qBAAqB,CAAC,CAAC,CAAC,EAAEsC,cAAc,EAAEuC,UAAU,CAAC7B,UAAU,CAACC,SAAS,CAAC,EAAE0B,cAAc,EAAEC,cAAc,CAAC;IAC3H;EACF,CAAC,MAAM;IACLG,2BAA2B,CAACF,UAAU,CAACxC,aAAa,EAAEwC,UAAU,CAACjB,MAAM,EAAEe,cAAc,EAAEC,cAAc,CAAC;EAC1G;AACF,CAAC;AACD;AACA,SAASE,aAAaA,CAACE,eAAe,EAAEC,iBAAiB,EAAEC,UAAU,EAAE;EACrE,IAAIC,GAAG,GAAG,IAAIzE,KAAK,CAAC,CAAC;EACrB,IAAIX,cAAc,CAACiF,eAAe,EAAEC,iBAAiB,EAAEE,GAAG,EAAE;IAC1DC,SAAS,EAAErE,IAAI,CAACsE,KAAK,CAACH,UAAU,CAACI,CAAC,EAAEJ,UAAU,CAACrB,CAAC,CAAC;IACjD0B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC,EAAE;IACFtF,2BAA2B,CAAC+E,iBAAiB,EAAEE,GAAG,CAAC;EACrD;AACF;AACA,OAAO,SAASJ,2BAA2BA,CAACU,kBAAkB,EAAEC,UAAU,EAAET,iBAAiB,EAAEC,UAAU,EAAE;EACzG;EACA,IAAIS,OAAO,GAAGjF,KAAK,CAACkF,GAAG,CAACV,UAAU,EAAEQ,UAAU,CAAC,IAAI,CAAC;EACpD,KAAK,IAAI9D,GAAG,GAAG,CAAC,EAAEiE,GAAG,GAAGJ,kBAAkB,CAACK,MAAM,EAAElE,GAAG,GAAGiE,GAAG,EAAEjE,GAAG,EAAE,EAAE;IACnE,IAAImE,SAAS,GAAGN,kBAAkB,CAACE,OAAO,GAAG/D,GAAG,GAAGiE,GAAG,GAAG,CAAC,GAAGjE,GAAG,CAAC;IACjE,IAAI,CAACmE,SAAS,CAAClD,KAAK,CAACC,MAAM,EAAE;MAC3BgC,aAAa,CAACiB,SAAS,EAAEd,iBAAiB,EAAEC,UAAU,CAAC;IACzD;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIc,WAAW,GAAG,aAAa,YAAY;EACzC;AACF;AACA;EACE,SAASA,WAAWA,CAACvE,SAAS,EAAEwE,GAAG,EAAEC,GAAG,EAAEhE,MAAM,EAAE;IAChD,IAAI,CAACiE,KAAK,GAAG,IAAIlH,OAAO,CAACmH,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,GAAG5E,SAAS;IAC3B,IAAI,CAAC6E,IAAI,GAAGL,GAAG;IACf,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,OAAO,GAAGtE,MAAM,IAAI,IAAId,wBAAwB,CAACqD,6BAA6B,CAAC;IACpF,IAAI,CAACgC,mBAAmB,CAACP,GAAG,CAAC;EAC/B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEF,WAAW,CAACzE,SAAS,CAACmF,SAAS,GAAG,UAAUR,GAAG,EAAE;IAC/C,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9E,KAAK,GAAG,IAAI,CAACyE,OAAO,CAAChF,YAAY,CAAC,IAAI,CAAC6E,UAAU,CAAC,CAACtE,KAAK;MAC5D;MACAlD,MAAM,CAAC,CAACkD,KAAK,CAAC+E,QAAQ,IAAI,CAAC/E,KAAK,CAACgF,sBAAsB,CAAC;MACxD;MACAhF,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACiF,qBAAqB,GAAG,KAAK;IACtD;IACA,IAAIC,GAAG,GAAG,IAAI,CAACC,IAAI,CAACD,GAAG;IACvBA,GAAG,CAACE,QAAQ,GAAGjB,GAAG,CAACiB,QAAQ;IAC3BF,GAAG,CAACG,WAAW,GAAGlB,GAAG,CAACkB,WAAW;IACjC,IAAI,CAACX,mBAAmB,CAACQ,GAAG,CAAC;EAC/B,CAAC;EACD;AACF;AACA;EACEjB,WAAW,CAACzE,SAAS,CAAC8F,WAAW,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACH,IAAI,CAACD,GAAG;EACtB,CAAC;EACDjB,WAAW,CAACzE,SAAS,CAACkF,mBAAmB,GAAG,UAAUQ,GAAG,EAAE;IACzD,IAAIxF,SAAS,GAAG,IAAI,CAAC4E,UAAU;IAC/B;IACA;IACA;IACA;IACA;IACA,IAAIiB,sBAAsB,GAAG7F,SAAS,CAAC8F,gBAAgB,GAAG9F,SAAS,CAAC8F,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3F;IACA,IAAI9E,QAAQ,GAAG7D,SAAS,CAACqI,GAAG,CAACxE,QAAQ,EAAEhB,SAAS,CAAC+F,GAAG,CAAC,MAAM,CAAC,CAAC;IAC7D,IAAIC,qBAAqB,GAAGhG,SAAS,CAAC+F,GAAG,CAAC,iBAAiB,CAAC;IAC5D,IAAIC,qBAAqB,IAAI,IAAI,IAAIA,qBAAqB,KAAK,MAAM,EAAE;MACrEA,qBAAqB,GAAG7I,SAAS,CAACqI,GAAG,CAACS,sBAAsB,EAAE,IAAI,CAAC;IACrE;IACA,IAAIzF,GAAG,GAAG;MACRgF,GAAG,EAAEA,GAAG;MACRE,QAAQ,EAAEF,GAAG,CAACE,QAAQ;MACtBQ,QAAQ,EAAEV,GAAG,CAACU,QAAQ;MACtBC,aAAa,EAAEhJ,SAAS,CAACqI,GAAG,CAACW,aAAa,EAAE,CAAC,CAAC;MAC9CC,aAAa,EAAEjJ,SAAS,CAACqI,GAAG,CAACY,aAAa,EAAE,CAAC,CAAC;MAC9CC,cAAc,EAAElJ,SAAS,CAACqI,GAAG,CAACa,cAAc,EAAE,CAAC,CAAC;MAChDV,WAAW,EAAExI,SAAS,CAACqI,GAAG,CAACG,WAAW,EAAE,CAAC,CAAC;MAC1CW,MAAM,EAAEnJ,SAAS,CAACqI,GAAG,CAACc,MAAM,EAAE,IAAI,CAAC;MACnCtF,QAAQ,EAAEA,QAAQ;MAClBC,YAAY,EAAE3D,SAAS,CAAC0C,SAAS,CAAC+F,GAAG,CAAC,cAAc,CAAC,EAAEF,sBAAsB,CAAC5E,YAAY,EAAE,KAAK,CAAC;MAClGsF,qBAAqB,EAAExF,WAAW,CAACC,QAAQ,CAAC,IAAIgF,qBAAqB;MACrEQ,iBAAiB,EAAExG,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;MAC9DU,cAAc,EAAEzG,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC;IACrD,CAAC;IACD,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzChI,MAAM,CAACoD,GAAG,CAACkF,QAAQ,IAAI,IAAI,CAAC;MAC5BtI,MAAM,CAACoD,GAAG,CAAC0F,QAAQ,IAAI,IAAI,CAAC;IAC9B;IACA,IAAI,CAACT,IAAI,GAAGjF,GAAG;IACf;IACA,IAAIkG,cAAc,GAAG,IAAIlJ,OAAO,CAACmH,KAAK,CAAC;MACrCvC,CAAC,EAAE5B,GAAG,CAACkF,QAAQ,CAAC,CAAC,CAAC;MAClB7B,CAAC,EAAErD,GAAG,CAACkF,QAAQ,CAAC,CAAC,CAAC;MAClBQ,QAAQ,EAAE1F,GAAG,CAAC0F;IAChB,CAAC,CAAC;IACFQ,cAAc,CAACC,eAAe,CAAC,CAAC;IAChC,IAAI,CAACC,eAAe,GAAGF,cAAc;IACrC,IAAI/F,MAAM,GAAG,IAAI,CAACoE,OAAO,CAAChF,YAAY,CAACC,SAAS,CAAC;IACjDW,MAAM,CAACY,UAAU,GAAG,IAAI,CAACqF,eAAe;IACxCjG,MAAM,CAACwB,MAAM,GAAG,IAAIlD,KAAK,CAACK,IAAI,CAACuH,GAAG,CAAC,CAACrG,GAAG,CAAC0F,QAAQ,CAAC,EAAE5G,IAAI,CAACwH,GAAG,CAAC,CAACtG,GAAG,CAAC0F,QAAQ,CAAC,CAAC;EAC7E,CAAC;EACD3B,WAAW,CAACzE,SAAS,CAACiH,KAAK,GAAG,UAAUC,eAAe,EAAEC,WAAW,EAAE;IACpE,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACF,eAAe,EAAE;MACpBA,eAAe,GAAG;QAChB3B,QAAQ,EAAE,IAAI;QACdE,qBAAqB,EAAE,KAAK;QAC5BD,sBAAsB,EAAE,IAAI;QAC5BtE,QAAQ,EAAE;MACZ,CAAC;IACH;IACAlE,IAAI,CAACqK,4BAA4B,EAAE,UAAUC,QAAQ,EAAE;MACrD,IAAIJ,eAAe,CAACI,QAAQ,CAAC,EAAE;QAC7BC,QAAQ,CAACD,QAAQ,CAAC,CAACF,KAAK,CAACzB,IAAI,EAAEyB,KAAK,CAACpC,MAAM,EAAEoC,KAAK,CAACnC,OAAO,EAAEmC,KAAK,CAACtC,UAAU,EAAEsC,KAAK,CAACxC,KAAK,EAAEwC,KAAK,CAACN,eAAe,EAAEM,KAAK,CAACrC,IAAI,EAAEoC,WAAW,IAAI,CAAC,CAAC,CAAC;MAClJ;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE1C,WAAW,CAAC+C,eAAe,GAAG,UAAUC,YAAY,EAAEC,YAAY,EAAE7D,SAAS,EAAE;IAC7E,IAAI8D,YAAY,GAAG5J,SAAS,CAAC2J,YAAY,GAAGD,YAAY,CAAC;IACzD,IAAIG,SAAS;IACb,IAAIC,iBAAiB;IACrB,IAAI/J,kBAAkB,CAAC6J,YAAY,CAAC,EAAE;MACpC;MACAE,iBAAiB,GAAGhE,SAAS,GAAG,CAAC,GAAG,KAAK,GAAG,QAAQ;MACpD+D,SAAS,GAAG,QAAQ;IACtB,CAAC,MAAM,IAAI9J,kBAAkB,CAAC6J,YAAY,GAAGpI,EAAE,CAAC,EAAE;MAChD;MACAsI,iBAAiB,GAAGhE,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,KAAK;MACpD+D,SAAS,GAAG,QAAQ;IACtB,CAAC,MAAM;MACLC,iBAAiB,GAAG,QAAQ;MAC5B,IAAIF,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGpI,EAAE,EAAE;QACzCqI,SAAS,GAAG/D,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;MAC9C,CAAC,MAAM;QACL+D,SAAS,GAAG/D,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAC9C;IACF;IACA,OAAO;MACLuC,QAAQ,EAAEuB,YAAY;MACtBC,SAAS,EAAEA,SAAS;MACpBC,iBAAiB,EAAEA;IACrB,CAAC;EACH,CAAC;EACDpD,WAAW,CAACqD,qBAAqB,GAAG,UAAU5H,SAAS,EAAE;IACvD,IAAI6H,SAAS,GAAG;MACdC,aAAa,EAAE9H,SAAS,CAAC+H,QAAQ;MACjC3H,cAAc,EAAEJ,SAAS,CAACI;IAC5B,CAAC;IACDyH,SAAS,CAAC7H,SAAS,CAAC+H,QAAQ,GAAG,OAAO,CAAC,GAAG/H,SAAS,CAACI,cAAc;IAClE,OAAOyH,SAAS;EAClB,CAAC;EACDtD,WAAW,CAACyD,aAAa,GAAG,UAAUhI,SAAS,EAAE;IAC/C,IAAIiI,UAAU,GAAGjI,SAAS,CAAC+F,GAAG,CAAC,SAAS,CAAC;IACzC,OAAO/F,SAAS,CAAC+F,GAAG,CAAC,QAAQ;IAC7B;IAAA,GACG,EAAE/F,SAAS,CAAC+F,GAAG,CAAC,cAAc,CAAC,IAAIkC,UAAU,IAAIA,UAAU,CAACC,IAAI,CAAC;EACtE,CAAC;EACD,OAAO3D,WAAW;AACpB,CAAC,CAAC,CAAC;AACH;AACA;AACA,IAAI4C,4BAA4B,GAAG,CAAC,UAAU,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,UAAU,CAAC;AAC9G,IAAIE,QAAQ,GAAG;EACbhC,QAAQ,EAAE,SAAAA,CAAU7E,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAE;IAC7E,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9E,KAAK,GAAGG,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC,CAACM,KAAK;MAChDlD,MAAM,CAAC,CAACkD,KAAK,CAAC+E,QAAQ,CAAC;MACvB/E,KAAK,CAAC+E,QAAQ,GAAG,IAAI;IACvB;IACA,IAAI+C,KAAK,GAAGpI,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC/C,IAAIqC,KAAK,KAAK,MAAM,EAAE;MACpBA,KAAK,GAAG,IAAI;MACZ,IAAI5H,GAAG,CAACgF,GAAG,CAAC6C,gBAAgB,IAAI,IAAI,EAAE;QACpCD,KAAK,GAAG,CAAC,CAAC5H,GAAG,CAACgF,GAAG,CAAC6C,gBAAgB;MACpC;IACF;IACA,IAAI,CAACD,KAAK,EAAE;MACV;IACF;IACA,IAAI3F,MAAM,GAAGzC,SAAS,CAACE,IAAI,CAACwC,SAAS,CAAC,CAAC;IACvC,IAAI4F,MAAM,GAAG5B,cAAc,CAAClF,SAAS;IACrC,IAAI+G,GAAG,GAAG,CAAC9F,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,IAAI+F,GAAG,GAAG,CAAC/F,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,IAAIgG,OAAO,GAAGF,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAIF,MAAM,EAAE;MACVpK,gBAAgB,CAACqK,GAAG,EAAEA,GAAG,EAAED,MAAM,CAAC;MAClCpK,gBAAgB,CAACsK,GAAG,EAAEA,GAAG,EAAEF,MAAM,CAAC;IACpC;IACA,IAAII,SAAS,GAAG7L,MAAM,CAAC;MACrB8L,OAAO,EAAE;IACX,CAAC,EAAE3I,SAAS,CAAC4I,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChE,IAAIC,YAAY,GAAG;MACjBC,sBAAsB,EAAEvI,GAAG,CAACgF,GAAG,CAACuD,sBAAsB,IAAI,CAAC;MAC3DzC,MAAM,EAAE,IAAI;MACZ0C,EAAE,EAAE,CAAC;MACLC,KAAK,EAAEP;IACT,CAAC;IACD,IAAI1I,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,IAAI/F,SAAS,CAACE,IAAI,CAACgJ,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE;MAChFtK,kBAAkB,CAAC,CAAC,CAACuK,kBAAkB,CAACpJ,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAEoC,YAAY,CAAC;IACzF,CAAC,MAAM;MACL,IAAIO,IAAI,GAAG,IAAI7L,OAAO,CAAC8L,IAAI,CAACzM,MAAM,CAAC;QACjC0M,KAAK,EAAE;UACLC,EAAE,EAAEjB,GAAG,CAAC,CAAC,CAAC;UACVkB,EAAE,EAAElB,GAAG,CAAC,CAAC,CAAC;UACVmB,EAAE,EAAElB,GAAG,CAAC,CAAC,CAAC;UACVmB,EAAE,EAAEnB,GAAG,CAAC,CAAC;QACX;MACF,CAAC,EAAEM,YAAY,CAAC,CAAC;MACjBtL,OAAO,CAACoM,oBAAoB,CAACP,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACJ,KAAK,CAACY,SAAS,CAAC;MAC9DR,IAAI,CAACS,IAAI,GAAG,MAAM;MAClBpF,KAAK,CAACqF,GAAG,CAACV,IAAI,CAAC;IACjB;IACA,IAAIW,MAAM,GAAGhK,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClD,IAAIiE,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIC,SAAS,GAAGjK,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;MACzD,IAAI/I,QAAQ,CAACgN,MAAM,CAAC,EAAE;QACpB;QACAA,MAAM,GAAG,CAACA,MAAM,EAAEA,MAAM,CAAC;MAC3B;MACA,IAAIhN,QAAQ,CAACiN,SAAS,CAAC,IAAIhN,QAAQ,CAACgN,SAAS,CAAC,EAAE;QAC9C;QACAA,SAAS,GAAG,CAACA,SAAS,EAAEA,SAAS,CAAC;MACpC;MACA,IAAIC,WAAW,GAAGnM,qBAAqB,CAACiC,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,EAAEkE,SAAS,CAAC;MACpG,IAAIE,aAAa,GAAGF,SAAS,CAAC,CAAC,CAAC;MAChC,IAAIG,cAAc,GAAGH,SAAS,CAAC,CAAC,CAAC;MACjCnN,IAAI,CAAC,CAAC;QACJuN,MAAM,EAAE7J,GAAG,CAAC0F,QAAQ,GAAG5G,IAAI,CAACD,EAAE,GAAG,CAAC;QAClCiL,MAAM,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACtBK,CAAC,EAAE;MACL,CAAC,EAAE;QACDF,MAAM,EAAE7J,GAAG,CAAC0F,QAAQ,GAAG5G,IAAI,CAACD,EAAE,GAAG,CAAC;QAClCiL,MAAM,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACtBK,CAAC,EAAEjL,IAAI,CAACkL,IAAI,CAAC,CAACjC,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,KAAKD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,KAAKD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,CAAC,EAAE,UAAUiC,KAAK,EAAEC,KAAK,EAAE;QAC1B,IAAIV,MAAM,CAACU,KAAK,CAAC,KAAK,MAAM,IAAIV,MAAM,CAACU,KAAK,CAAC,IAAI,IAAI,EAAE;UACrD,IAAIC,MAAM,GAAG7M,YAAY,CAACkM,MAAM,CAACU,KAAK,CAAC,EAAE,CAACP,aAAa,GAAG,CAAC,EAAE,CAACC,cAAc,GAAG,CAAC,EAAED,aAAa,EAAEC,cAAc,EAAE1B,SAAS,CAACkC,MAAM,EAAE,IAAI,CAAC;UACxI;UACA,IAAIL,CAAC,GAAGE,KAAK,CAACF,CAAC,GAAGE,KAAK,CAACH,MAAM;UAC9B,IAAIO,EAAE,GAAGpC,OAAO,GAAGD,GAAG,GAAGD,GAAG;UAC5BoC,MAAM,CAACG,IAAI,CAAC;YACV5E,QAAQ,EAAEuE,KAAK,CAACJ,MAAM;YACtBjI,CAAC,EAAEyI,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAGjL,IAAI,CAACuH,GAAG,CAACrG,GAAG,CAAC0F,QAAQ,CAAC;YACrCrC,CAAC,EAAEgH,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAGjL,IAAI,CAACwH,GAAG,CAACtG,GAAG,CAAC0F,QAAQ,CAAC;YACrCI,MAAM,EAAE,IAAI;YACZ0C,EAAE,EAAE;UACN,CAAC,CAAC;UACFtE,KAAK,CAACqF,GAAG,CAACY,MAAM,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;EACEpF,qBAAqB,EAAE,SAAAA,CAAU/E,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAEyC,WAAW,EAAE;IACvG,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9E,KAAK,GAAGG,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC,CAACM,KAAK;MAChDlD,MAAM,CAAC,CAACkD,KAAK,CAACgF,sBAAsB,CAAC;MACrChF,KAAK,CAACiF,qBAAqB,GAAG,IAAI;IACpC;IACA,IAAIwF,cAAc,GAAGC,+BAA+B,CAAC7C,KAAK,EAAEzD,KAAK,EAAEuC,WAAW,CAAC;IAC/E,IAAI8D,cAAc,EAAE;MAClBE,mBAAmB,CAACzK,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAErF,0BAA0B,CAAC+L,QAAQ,CAAC;IACrH;EACF,CAAC;EACD;AACF;AACA;AACA;EACE5F,sBAAsB,EAAE,SAAAA,CAAU9E,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAEyC,WAAW,EAAE;IACxG,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9E,KAAK,GAAGG,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC,CAACM,KAAK;MAChDA,KAAK,CAACgF,sBAAsB,GAAG,IAAI;IACrC;IACA,IAAIyF,cAAc,GAAGC,+BAA+B,CAAC7C,KAAK,EAAEzD,KAAK,EAAEuC,WAAW,CAAC;IAC/E,IAAI8D,cAAc,EAAE;MAClBE,mBAAmB,CAACzK,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAErF,0BAA0B,CAACgM,SAAS,CAAC;IACtH;IACA,IAAIC,QAAQ,GAAGC,mBAAmB,CAAC7K,GAAG,EAAEkE,KAAK,EAAEgC,cAAc,EAAE1G,SAAS,CAAC;IACzEsL,2BAA2B,CAAC9K,GAAG,EAAE2H,KAAK,CAACzH,eAAe,EAAE0K,QAAQ,CAAC;IACjEG,mBAAmB,CAAC/K,GAAG,EAAEkE,KAAK,EAAEgC,cAAc,EAAE1G,SAAS,EAAEQ,GAAG,CAAC4F,aAAa,CAAC;EAC/E,CAAC;EACD;AACF;AACA;AACA;EACEpF,QAAQ,EAAE,SAAAA,CAAUR,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAEyC,WAAW,EAAE;IAC1F,IAAIuE,YAAY,GAAG/K,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC;IACjD,IAAIkF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI9E,KAAK,GAAGkL,YAAY,CAAClL,KAAK;MAC9BlD,MAAM,CAACkD,KAAK,CAACiF,qBAAqB,IAAIjF,KAAK,CAACgF,sBAAsB,CAAC;MACnEhF,KAAK,CAACU,QAAQ,GAAG,IAAI;IACvB;IACA;IACA,IAAImH,KAAK,CAACsD,MAAM,EAAE;MAChB/G,KAAK,CAACgH,MAAM,CAACvD,KAAK,CAACsD,MAAM,CAAC;MAC1BtD,KAAK,CAACsD,MAAM,GAAGD,YAAY,CAACG,UAAU,GAAGH,YAAY,CAACvK,YAAY,GAAG,IAAI;IAC3E;IACA,IAAI2K,IAAI,GAAGpL,GAAG,CAACQ,QAAQ;IACvB,IAAI,CAACD,WAAW,CAAC6K,IAAI,CAAC,EAAE;MACtB;IACF;IACA,IAAI3K,YAAY,GAAGT,GAAG,CAACS,YAAY;IACnC,IAAIkF,aAAa,GAAG3F,GAAG,CAAC2F,aAAa;IACrC,IAAI0F,cAAc,GAAG7L,SAAS,CAAC4I,QAAQ,CAAC,eAAe,CAAC;IACxD,IAAIkD,GAAG,GAAG9L,SAAS,CAAC+F,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;IACvC,IAAItD,MAAM,GAAGzC,SAAS,CAACE,IAAI,CAACwC,SAAS,CAAC,CAAC;IACvC,IAAIqJ,iBAAiB,GAAG/L,SAAS,CAACE,IAAI,CAACuI,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;IACvD,IAAIuD,GAAG,GAAG,IAAI/M,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB,IAAIkE,cAAc,GAAG,IAAIlE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACpC,IAAIgC,YAAY,KAAK,OAAO,EAAE;MAC5B+K,GAAG,CAAC5J,CAAC,GAAGK,MAAM,CAAC,CAAC,CAAC,GAAGsJ,iBAAiB,GAAGD,GAAG;MAC3C3I,cAAc,CAACf,CAAC,GAAG,CAAC2J,iBAAiB;IACvC,CAAC,MAAM,IAAI9K,YAAY,KAAK,KAAK,EAAE;MACjC+K,GAAG,CAAC5J,CAAC,GAAGK,MAAM,CAAC,CAAC,CAAC,GAAGsJ,iBAAiB,GAAGD,GAAG;MAC3C3I,cAAc,CAACf,CAAC,GAAG2J,iBAAiB;IACtC,CAAC,MAAM;MACL;MACAC,GAAG,CAAC5J,CAAC,GAAG,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;MACnCuJ,GAAG,CAACnI,CAAC,GAAGrD,GAAG,CAACmF,WAAW,GAAGQ,aAAa,GAAG2F,GAAG;MAC7C3I,cAAc,CAACU,CAAC,GAAGsC,aAAa;IAClC;IACA,IAAI8F,EAAE,GAAGjO,UAAU,CAAC+E,MAAM,CAAC,CAAC;IAC5BI,cAAc,CAAC3B,SAAS,CAACxD,UAAU,CAACqM,MAAM,CAAC4B,EAAE,EAAEA,EAAE,EAAEzL,GAAG,CAAC0F,QAAQ,CAAC,CAAC;IACjE,IAAIgG,YAAY,GAAGlM,SAAS,CAAC+F,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAImG,YAAY,IAAI,IAAI,EAAE;MACxBA,YAAY,GAAGA,YAAY,GAAG7M,EAAE,GAAG,GAAG,CAAC,CAAC;IAC1C;IACA,IAAI8M,WAAW;IACf,IAAIC,sBAAsB;IAC1B,IAAIjO,oBAAoB,CAAC8C,YAAY,CAAC,EAAE;MACtCkL,WAAW,GAAG5H,WAAW,CAAC+C,eAAe,CAAC9G,GAAG,CAAC0F,QAAQ,EAAEgG,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG1L,GAAG,CAAC0F,QAAQ;MAC1G;MACAC,aAAa,CAAC;IAChB,CAAC,MAAM;MACLgG,WAAW,GAAGE,aAAa,CAAC7L,GAAG,CAAC0F,QAAQ,EAAEjF,YAAY,EAAEiL,YAAY,IAAI,CAAC,EAAEzJ,MAAM,CAAC;MAClF2J,sBAAsB,GAAG5L,GAAG,CAACgF,GAAG,CAAC4G,sBAAsB;MACvD,IAAIA,sBAAsB,IAAI,IAAI,EAAE;QAClCA,sBAAsB,GAAG9M,IAAI,CAAC4C,GAAG,CAACkK,sBAAsB,GAAG9M,IAAI,CAACwH,GAAG,CAACqF,WAAW,CAACjG,QAAQ,CAAC,CAAC;QAC1F,CAACoG,QAAQ,CAACF,sBAAsB,CAAC,KAAKA,sBAAsB,GAAG,IAAI,CAAC;MACtE;IACF;IACA,IAAIG,QAAQ,GAAGV,cAAc,CAACW,OAAO,CAAC,CAAC;IACvC,IAAIC,WAAW,GAAGzM,SAAS,CAAC+F,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D,IAAI2G,QAAQ,GAAGD,WAAW,CAACC,QAAQ;IACnC,IAAIC,QAAQ,GAAGhQ,QAAQ,CAAC6D,GAAG,CAACgF,GAAG,CAACoH,oBAAoB,EAAEH,WAAW,CAACE,QAAQ,EAAEP,sBAAsB,CAAC;IACnG,IAAIS,eAAe,GAAG5F,WAAW,CAAC4F,eAAe,IAAI,CAAC;IACtD,IAAIC,MAAM,GAAG,IAAItP,OAAO,CAACuP,IAAI,CAAC;MAC5B3K,CAAC,EAAE4J,GAAG,CAAC5J,CAAC;MACRyB,CAAC,EAAEmI,GAAG,CAACnI,CAAC;MACRqC,QAAQ,EAAEiG,WAAW,CAACjG,QAAQ;MAC9BI,MAAM,EAAE/B,WAAW,CAACyD,aAAa,CAAChI,SAAS,CAAC;MAC5CiJ,KAAK,EAAEvL,eAAe,CAACmO,cAAc,EAAE;QACrCmB,IAAI,EAAEpB,IAAI;QACVqB,IAAI,EAAEV,QAAQ;QACdW,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAER,QAAQ;QACfD,QAAQ,EAAEA,QAAQ;QAClBU,IAAI,EAAEvB,cAAc,CAACwB,YAAY,CAAC,CAAC,IAAIrN,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QACxFuH,KAAK,EAAEzB,cAAc,CAAC9F,GAAG,CAAC,OAAO,CAAC,IAAIoG,WAAW,CAACzE,SAAS;QAC3D6F,aAAa,EAAE1B,cAAc,CAAC9F,GAAG,CAAC,eAAe,CAAC,IAAIoG,WAAW,CAACxE;MACpE,CAAC,CAAC;MACFqB,EAAE,EAAE;IACN,CAAC,CAAC;IACFxL,OAAO,CAACgQ,gBAAgB,CAAC;MACvBC,EAAE,EAAEX,MAAM;MACVY,cAAc,EAAE1N,SAAS;MACzB2N,QAAQ,EAAE/B;IACZ,CAAC,CAAC;IACFkB,MAAM,CAACc,UAAU,GAAGhC,IAAI;IACxB;IACAkB,MAAM,CAAChD,IAAI,GAAG,MAAM;IACpB,IAAI9J,SAAS,CAAC+F,GAAG,CAAC,cAAc,CAAC,EAAE;MACjC,IAAI8B,SAAS,GAAGtD,WAAW,CAACqD,qBAAqB,CAAC5H,SAAS,CAAC;MAC5D6H,SAAS,CAACgG,UAAU,GAAG,UAAU;MACjChG,SAAS,CAAC+D,IAAI,GAAGA,IAAI;MACrBnO,SAAS,CAACqP,MAAM,CAAC,CAACjF,SAAS,GAAGA,SAAS;IACzC;IACAnB,cAAc,CAACqD,GAAG,CAAC+C,MAAM,CAAC;IAC1BA,MAAM,CAACnG,eAAe,CAAC,CAAC;IACxBwB,KAAK,CAACsD,MAAM,GAAGqB,MAAM;IACrB,IAAInB,UAAU,GAAGH,YAAY,CAACG,UAAU,GAAGnN,6BAA6B,CAAC;MACvE4C,KAAK,EAAE0L,MAAM;MACbgB,QAAQ,EAAEhB,MAAM,CAAC9D,EAAE;MACnB+E,WAAW,EAAE;QACX1M,MAAM,EAAEyL,MAAM,CAACzL;MACjB,CAAC;MACD2M,aAAa,EAAE7P,oBAAoB,CAAC8C,YAAY;MAChD;MACA;MAAA,EACE1B,iCAAiC,CAACsN,eAAe;MACnD;MACA;MAAA,EACErN,+BAA+B,CAACqN,eAAe;IACnD,CAAC,CAAC;IACFrB,YAAY,CAACvK,YAAY,GAAGA,YAAY;IACxCyD,KAAK,CAACqF,GAAG,CAAC+C,MAAM,CAAC;IACjBA,MAAM,CAACmB,kBAAkB,CAAC,CAAC;IAC3B,IAAIzN,GAAG,CAAC+F,qBAAqB,IAAIoF,UAAU,EAAE;MAC3C,IAAIhL,MAAM,GAAGF,MAAM,CAACV,YAAY,CAACC,SAAS,CAAC;MAC3C,IAAIkF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzChI,MAAM,CAACuD,MAAM,CAACC,aAAa,CAAC;MAC9B;MACAH,MAAM,CAACb,sBAAsB,CAACY,GAAG,EAAEC,MAAM,EAAET,SAAS,EAAE2L,UAAU,EAAExI,cAAc,EAAExC,MAAM,CAAC;IAC3F;EACF;AACF,CAAC;AACD,SAASsK,mBAAmBA,CAACzK,GAAG,EAAE2H,KAAK,EAAE1H,MAAM,EAAET,SAAS,EAAE0E,KAAK,EAAEgC,cAAc,EAAElC,GAAG,EAAE0J,IAAI,EAAE;EAC5F,IAAI,CAACC,0BAA0B,CAAChG,KAAK,CAAC,EAAE;IACtCiG,cAAc,CAAC5N,GAAG,EAAE2H,KAAK,EAAEzD,KAAK,EAAEwJ,IAAI,EAAElO,SAAS,EAAEwE,GAAG,CAAC;EACzD;EACA,IAAI9D,eAAe,GAAGyH,KAAK,CAACzH,eAAe;EAC3C2N,6BAA6B,CAAC7N,GAAG,EAAER,SAAS,EAAEU,eAAe,EAAEgG,cAAc,CAAC;EAC9E4H,iBAAiB,CAACtO,SAAS,EAAEQ,GAAG,CAAC0F,QAAQ,EAAExF,eAAe,CAAC;EAC3D,IAAI8F,iBAAiB,GAAGhG,GAAG,CAACgG,iBAAiB;EAC7C+H,kBAAkB,CAACvO,SAAS,EAAEU,eAAe,EAAE8F,iBAAiB,CAAC;EACjE,IAAIA,iBAAiB,EAAE;IACrB;IACA;IACAnI,WAAW;IACX;IACAd,MAAM,CAACmD,eAAe,EAAE,UAAUQ,MAAM,EAAE;MACxC,OAAOA,MAAM,IAAI,CAACA,MAAM,CAACE,KAAK,CAACC,MAAM;IACvC,CAAC,CAAC,CAAC;EACL;EACA;EACA;EACAd,0BAA0B,CAACC,GAAG,EAAEC,MAAM,EAAET,SAAS,EAAEU,eAAe,CAAC;AACrE;AACA;AACA,SAAS2L,aAAaA,CAACnG,QAAQ,EAAEsI,YAAY,EAAEC,UAAU,EAAEhM,MAAM,EAAE;EACjE,IAAIgF,YAAY,GAAG5J,SAAS,CAAC4Q,UAAU,GAAGvI,QAAQ,CAAC;EACnD,IAAIwB,SAAS;EACb,IAAIC,iBAAiB;EACrB,IAAIc,OAAO,GAAGhG,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EACnC,IAAIiM,MAAM,GAAGF,YAAY,KAAK,OAAO,IAAI,CAAC/F,OAAO,IAAI+F,YAAY,KAAK,OAAO,IAAI/F,OAAO;EACxF,IAAI7K,kBAAkB,CAAC6J,YAAY,GAAGpI,EAAE,GAAG,CAAC,CAAC,EAAE;IAC7CsI,iBAAiB,GAAG+G,MAAM,GAAG,QAAQ,GAAG,KAAK;IAC7ChH,SAAS,GAAG,QAAQ;EACtB,CAAC,MAAM,IAAI9J,kBAAkB,CAAC6J,YAAY,GAAGpI,EAAE,GAAG,GAAG,CAAC,EAAE;IACtDsI,iBAAiB,GAAG+G,MAAM,GAAG,KAAK,GAAG,QAAQ;IAC7ChH,SAAS,GAAG,QAAQ;EACtB,CAAC,MAAM;IACLC,iBAAiB,GAAG,QAAQ;IAC5B,IAAIF,YAAY,GAAGpI,EAAE,GAAG,GAAG,IAAIoI,YAAY,GAAGpI,EAAE,GAAG,CAAC,EAAE;MACpDqI,SAAS,GAAGgH,MAAM,GAAG,MAAM,GAAG,OAAO;IACvC,CAAC,MAAM;MACLhH,SAAS,GAAGgH,MAAM,GAAG,OAAO,GAAG,MAAM;IACvC;EACF;EACA,OAAO;IACLxI,QAAQ,EAAEuB,YAAY;IACtBC,SAAS,EAAEA,SAAS;IACpBC,iBAAiB,EAAEA;EACrB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA,SAAS4G,kBAAkBA,CAACvO,SAAS,EAAEU,eAAe,EAAE8F,iBAAiB,EAAE;EACzE,IAAIpI,mBAAmB,CAAC4B,SAAS,CAACE,IAAI,CAAC,EAAE;IACvC;EACF;EACA;EACA;EACA;EACA,SAASyO,IAAIA,CAACC,eAAe,EAAEC,eAAe,EAAEC,aAAa,EAAE;IAC7D,IAAIC,kBAAkB,GAAGvQ,6BAA6B,CAACkC,eAAe,CAACmO,eAAe,CAAC,CAAC;IACxF,IAAIG,gBAAgB,GAAGxQ,6BAA6B,CAACkC,eAAe,CAACoO,aAAa,CAAC,CAAC;IACpF,IAAI,CAACC,kBAAkB,IAAI,CAACC,gBAAgB,EAAE;MAC5C;IACF;IACA,IAAIJ,eAAe,KAAK,KAAK,IAAIG,kBAAkB,CAACE,aAAa,EAAE;MACjEC,QAAQ,CAACH,kBAAkB,CAAC3N,KAAK,CAAC;MAClC;IACF;IACA,IAAI4N,gBAAgB,CAACC,aAAa,EAAE;MAClCC,QAAQ,CAACF,gBAAgB,CAAC5N,KAAK,CAAC;MAChC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI2C,cAAc,GAAG,GAAG;IACxB;IACA;IACA;IACA,IAAI,CAACyC,iBAAiB,EAAE;MACtB,IAAI2I,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9B;MACAJ,kBAAkB,GAAGpQ,0BAA0B,CAAC;QAC9CwQ,WAAW,EAAEA;MACf,CAAC,EAAEJ,kBAAkB,CAAC;MACtBC,gBAAgB,GAAGrQ,0BAA0B,CAAC;QAC5CwQ,WAAW,EAAEA;MACf,CAAC,EAAEH,gBAAgB,CAAC;IACtB;IACA,IAAI1Q,cAAc,CAACyQ,kBAAkB,EAAEC,gBAAgB,EAAE,IAAI,EAAE;MAC7DjL,cAAc,EAAEA;IAClB,CAAC,CAAC,EAAE;MACF,IAAI6K,eAAe,EAAE;QACnBM,QAAQ,CAACF,gBAAgB,CAAC5N,KAAK,CAAC;MAClC,CAAC,MAAM;QACL8N,QAAQ,CAACH,kBAAkB,CAAC3N,KAAK,CAAC;MACpC;IACF;EACF;EACA;EACA;EACA;EACA,IAAIgO,YAAY,GAAGpP,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;EAC/D,IAAIsJ,YAAY,GAAGrP,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;EAC/D,IAAIuJ,SAAS,GAAG5O,eAAe,CAAC2D,MAAM;EACtCsK,IAAI,CAACS,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;EACxBT,IAAI,CAACU,YAAY,EAAEC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG,CAAC,CAAC;AAClD;AACA;AACA,SAAShE,2BAA2BA,CAAC9K,GAAG,EAAEE,eAAe,EAAE6O,OAAO,EAAE;EAClE,IAAI/O,GAAG,CAACiG,cAAc,EAAE;IACtB;IACA;EACF;EACA3J,IAAI,CAAC4D,eAAe,EAAE,UAAUyL,WAAW,EAAE;IAC3C,IAAIA,WAAW,IAAIA,WAAW,CAAC/K,KAAK,CAACC,MAAM,EAAE;MAC3C,KAAK,IAAIlB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGoP,OAAO,CAAClL,MAAM,EAAElE,GAAG,EAAE,EAAE;QAC7C,IAAIqP,MAAM,GAAGD,OAAO,CAACpP,GAAG,CAAC;QACzB;QACA;QACA,IAAIsP,SAAS,GAAG/P,YAAY,CAAC8P,MAAM,CAAC;QACpC,IAAIE,UAAU,GAAGjQ,aAAa,CAAC0M,WAAW,CAAC/K,KAAK,CAAC;QACjD,IAAIqO,SAAS,CAACE,SAAS,IAAI,IAAI,IAAI,CAACF,SAAS,CAACG,MAAM,IAAIH,SAAS,CAACE,SAAS,KAAKD,UAAU,CAACC,SAAS,EAAE;UACpGT,QAAQ,CAACM,MAAM,CAAC;UAChB;QACF;MACF;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAASN,QAAQA,CAACzB,EAAE,EAAE;EACpBA,EAAE,KAAKA,EAAE,CAACpM,MAAM,GAAG,IAAI,CAAC;AAC1B;AACA,SAASwO,WAAWA,CAACC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAE;EACxF,IAAIX,OAAO,GAAG,EAAE;EAChB,IAAIhH,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI2H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACzL,MAAM,EAAE8L,CAAC,EAAE,EAAE;IAC3C,IAAIC,SAAS,GAAGN,WAAW,CAACK,CAAC,CAAC,CAACE,KAAK;IACpC9H,GAAG,CAAC,CAAC,CAAC,GAAG6H,SAAS;IAClB7H,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACVC,GAAG,CAAC,CAAC,CAAC,GAAG4H,SAAS;IAClB5H,GAAG,CAAC,CAAC,CAAC,GAAGwH,YAAY;IACrB,IAAID,aAAa,EAAE;MACjB7R,gBAAgB,CAACqK,GAAG,EAAEA,GAAG,EAAEwH,aAAa,CAAC;MACzC7R,gBAAgB,CAACsK,GAAG,EAAEA,GAAG,EAAEuH,aAAa,CAAC;IAC3C;IACA;IACA,IAAIP,MAAM,GAAG,IAAIhS,OAAO,CAAC8L,IAAI,CAAC;MAC5BC,KAAK,EAAE;QACLC,EAAE,EAAEjB,GAAG,CAAC,CAAC,CAAC;QACVkB,EAAE,EAAElB,GAAG,CAAC,CAAC,CAAC;QACVmB,EAAE,EAAElB,GAAG,CAAC,CAAC,CAAC;QACVmB,EAAE,EAAEnB,GAAG,CAAC,CAAC;MACX,CAAC;MACDS,KAAK,EAAEgH,aAAa;MACpBjH,EAAE,EAAE,CAAC;MACLsH,SAAS,EAAE,IAAI;MACfhK,MAAM,EAAE;IACV,CAAC,CAAC;IACF9I,OAAO,CAACoM,oBAAoB,CAAC4F,MAAM,CAACjG,KAAK,EAAEiG,MAAM,CAACvG,KAAK,CAACY,SAAS,CAAC;IAClE2F,MAAM,CAAC1F,IAAI,GAAGoG,UAAU,GAAG,GAAG,GAAGJ,WAAW,CAACK,CAAC,CAAC,CAACR,SAAS;IACzDJ,OAAO,CAACjO,IAAI,CAACkO,MAAM,CAAC;IACpB,IAAIe,KAAK,GAAG7Q,YAAY,CAAC8P,MAAM,CAAC;IAChCe,KAAK,CAACX,MAAM,GAAG,CAAC,CAACE,WAAW,CAACK,CAAC,CAAC,CAACP,MAAM;IACtCW,KAAK,CAACZ,SAAS,GAAGG,WAAW,CAACK,CAAC,CAAC,CAACR,SAAS;EAC5C;EACA,OAAOJ,OAAO;AAChB;AACA,SAASlE,mBAAmBA,CAAC7K,GAAG,EAAEkE,KAAK,EAAEgC,cAAc,EAAE1G,SAAS,EAAE;EAClE,IAAIE,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAIsQ,SAAS,GAAGxQ,SAAS,CAAC4I,QAAQ,CAAC,UAAU,CAAC;EAC9C,IAAIR,KAAK,GAAGoI,SAAS,CAACzK,GAAG,CAAC,MAAM,CAAC;EACjC,IAAIqC,KAAK,KAAK,MAAM,EAAE;IACpBA,KAAK,GAAG,IAAI;IACZ,IAAI5H,GAAG,CAACgF,GAAG,CAACiL,gBAAgB,IAAI,IAAI,EAAE;MACpCrI,KAAK,GAAG,CAAC,CAAC5H,GAAG,CAACgF,GAAG,CAACiL,gBAAgB;IACpC;EACF;EACA,IAAI,CAACrI,KAAK,IAAIlI,IAAI,CAACgJ,KAAK,CAACwH,OAAO,CAAC,CAAC,EAAE;IAClC,OAAO,EAAE;EACX;EACA,IAAIC,cAAc,GAAGH,SAAS,CAAC5H,QAAQ,CAAC,WAAW,CAAC;EACpD,IAAIoH,YAAY,GAAGxP,GAAG,CAAC4F,aAAa,GAAGoK,SAAS,CAACzK,GAAG,CAAC,QAAQ,CAAC;EAC9D,IAAI+J,WAAW,GAAG5P,IAAI,CAAC0Q,cAAc,CAAC,CAAC;EACvC,IAAIxF,QAAQ,GAAGyE,WAAW,CAACC,WAAW,EAAEpJ,cAAc,CAAClF,SAAS,EAAEwO,YAAY,EAAEpT,QAAQ,CAAC+T,cAAc,CAAC9H,YAAY,CAAC,CAAC,EAAE;IACtH+B,MAAM,EAAE5K,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC1D,CAAC,CAAC,EAAE,OAAO,CAAC;EACZ,KAAK,IAAIoK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,QAAQ,CAAC/G,MAAM,EAAE8L,CAAC,EAAE,EAAE;IACxCzL,KAAK,CAACqF,GAAG,CAACqB,QAAQ,CAAC+E,CAAC,CAAC,CAAC;EACxB;EACA,OAAO/E,QAAQ;AACjB;AACA,SAASG,mBAAmBA,CAAC/K,GAAG,EAAEkE,KAAK,EAAEgC,cAAc,EAAE1G,SAAS,EAAEoG,aAAa,EAAE;EACjF,IAAIlG,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAI2Q,cAAc,GAAG7Q,SAAS,CAAC4I,QAAQ,CAAC,WAAW,CAAC;EACpD,IAAI,CAACpI,GAAG,CAACiG,cAAc,IAAIvG,IAAI,CAACgJ,KAAK,CAACwH,OAAO,CAAC,CAAC,EAAE;IAC/C;EACF;EACA,IAAII,gBAAgB,GAAG5Q,IAAI,CAAC6Q,mBAAmB,CAAC,CAAC;EACjD,IAAI,CAACD,gBAAgB,CAACzM,MAAM,EAAE;IAC5B;EACF;EACA,IAAIsM,cAAc,GAAGE,cAAc,CAACjI,QAAQ,CAAC,WAAW,CAAC;EACzD,IAAIoH,YAAY,GAAG5J,aAAa,GAAGyK,cAAc,CAAC9K,GAAG,CAAC,QAAQ,CAAC;EAC/D,IAAIiL,kBAAkB,GAAGpU,QAAQ,CAAC+T,cAAc,CAAC9H,YAAY,CAAC,CAAC,EAAEjM,QAAQ,CAACoD,SAAS,CAAC4I,QAAQ,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC,CAAC,EAAE;IACvH+B,MAAM,EAAE5K,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC1D,CAAC,CAAC,CAAC;EACH,KAAK,IAAIoK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,gBAAgB,CAACzM,MAAM,EAAE8L,CAAC,EAAE,EAAE;IAChD,IAAIc,aAAa,GAAGpB,WAAW,CAACiB,gBAAgB,CAACX,CAAC,CAAC,EAAEzJ,cAAc,CAAClF,SAAS,EAAEwO,YAAY,EAAEgB,kBAAkB,EAAE,aAAa,GAAGb,CAAC,CAAC;IACnI,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAAC5M,MAAM,EAAE6M,CAAC,EAAE,EAAE;MAC7CxM,KAAK,CAACqF,GAAG,CAACkH,aAAa,CAACC,CAAC,CAAC,CAAC;IAC7B;EACF;AACF;AACA;AACA,SAASlG,+BAA+BA,CAAC7C,KAAK,EAAEzD,KAAK,EAAEuC,WAAW,EAAE;EAClE,IAAIkH,0BAA0B,CAAChG,KAAK,CAAC,EAAE;IACrC,IAAIgJ,yBAAyB,GAAGhJ,KAAK,CAACgJ,yBAAyB;IAC/D,IAAIjM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzChI,MAAM,CAAC+K,KAAK,CAACiJ,UAAU,IAAID,yBAAyB,CAAC;IACvD;IACA,IAAIE,sBAAsB,GAAGF,yBAAyB,CAACG,GAAG,CAACD,sBAAsB;IACjF,IAAIpK,WAAW,CAACsK,UAAU,EAAE;MAC1B,IAAIC,YAAY,GAAG,IAAI;MACvB,KAAK,IAAIrR,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGkR,sBAAsB,CAAChN,MAAM,EAAElE,GAAG,EAAE,EAAE;QAC5DqR,YAAY,GAAGA,YAAY,IAAIH,sBAAsB,CAAClR,GAAG,CAAC,CAAC,CAAC;MAC9D;MACA,IAAIqR,YAAY,EAAE;QAChB,OAAO,KAAK;MACd;IACF;IACA,IAAIH,sBAAsB,CAAChN,MAAM,EAAE;MACjC;MACAK,KAAK,CAACgH,MAAM,CAACvD,KAAK,CAACiJ,UAAU,CAAC;MAC9BK,uBAAuB,CAACtJ,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAClD;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASiG,cAAcA,CAAC5N,GAAG,EAAE2H,KAAK,EAAEzD,KAAK,EAAEwJ,IAAI,EAAElO,SAAS,EAAEwE,GAAG,EAAE;EAC/D,IAAItE,IAAI,GAAGF,SAAS,CAACE,IAAI;EACzB,IAAIgI,IAAI,GAAGvL,QAAQ,CAAC6D,GAAG,CAACgF,GAAG,CAACkM,aAAa,EAAE1R,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EAChF,IAAIqL,UAAU,GAAG,IAAI5T,OAAO,CAACmH,KAAK,CAAC,CAAC;EACpCD,KAAK,CAACqF,GAAG,CAACqH,UAAU,CAAC;EACrB,IAAIO,oBAAoB,GAAGvS,gCAAgC,CAAC8O,IAAI,CAAC;EACjE,IAAI,CAAChG,IAAI,IAAIhI,IAAI,CAACgJ,KAAK,CAACwH,OAAO,CAAC,CAAC,EAAE;IACjCe,uBAAuB,CAACtJ,KAAK,EAAE,EAAE,EAAEiJ,UAAU,EAAEO,oBAAoB,CAAC;IACpE;EACF;EACA,IAAIC,UAAU,GAAG5R,SAAS,CAAC4I,QAAQ,CAAC,WAAW,CAAC;EAChD,IAAIiJ,MAAM,GAAG3R,IAAI,CAAC4R,aAAa,CAACH,oBAAoB,CAAC;EACrD;EACA,IAAII,aAAa,GAAG,CAACpV,QAAQ,CAAC6D,GAAG,CAACgF,GAAG,CAACwM,WAAW,EAAEJ,UAAU,CAAC7L,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI1G,EAAE,GAAG,GAAG;EAC7F,IAAI8M,WAAW,GAAG5H,WAAW,CAAC+C,eAAe,CAAC9G,GAAG,CAAC0F,QAAQ,EAAE6L,aAAa,EAAEvR,GAAG,CAAC6F,cAAc,CAAC;EAC9F,IAAI4L,eAAe,GAAGjS,SAAS,CAACkS,aAAa,IAAIlS,SAAS,CAACkS,aAAa,CAAC,IAAI,CAAC;EAC9E,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,YAAY,GAAGpS,SAAS,CAAC+F,GAAG,CAAC,cAAc,CAAC;EAChD,IAAIsM,KAAK,GAAGC,QAAQ;EACpB,IAAIC,KAAK,GAAG,CAACD,QAAQ;EACrBxV,IAAI,CAAC+U,MAAM,EAAE,UAAUW,SAAS,EAAE9H,KAAK,EAAE;IACvC,IAAI+H,EAAE;IACN,IAAI9C,SAAS,GAAGzP,IAAI,CAACgJ,KAAK,CAACwJ,IAAI,KAAK,SAAS,GAAGxS,IAAI,CAACgJ,KAAK,CAACyJ,mBAAmB,CAACH,SAAS,CAAC7C,SAAS,CAAC,GAAG6C,SAAS,CAAC7C,SAAS;IACzH,IAAIiD,cAAc,GAAGJ,SAAS,CAACI,cAAc;IAC7C,IAAIC,QAAQ,GAAGL,SAAS,CAACK,QAAQ;IACjC,IAAIC,cAAc,GAAGlB,UAAU;IAC/B,IAAIK,eAAe,IAAIA,eAAe,CAACtC,SAAS,CAAC,EAAE;MACjD,IAAIoD,eAAe,GAAGd,eAAe,CAACtC,SAAS,CAAC;MAChD,IAAI5S,QAAQ,CAACgW,eAAe,CAAC,IAAIA,eAAe,CAACC,SAAS,EAAE;QAC1DF,cAAc,GAAG,IAAInV,KAAK,CAACoV,eAAe,CAACC,SAAS,EAAEpB,UAAU,EAAE5R,SAAS,CAACiT,OAAO,CAAC;MACtF;IACF;IACA,IAAIC,SAAS,GAAGJ,cAAc,CAACzF,YAAY,CAAC,CAAC,IAAIrN,SAAS,CAAC+F,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAClG,IAAIuH,KAAK,GAAGwF,cAAc,CAACK,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAIhH,WAAW,CAACzE,SAAS;IAC7E,IAAI0L,QAAQ,GAAGjW,SAAS,CAAC2V,cAAc,CAACK,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE7F,KAAK,CAAC;IACjF,IAAI+F,QAAQ,GAAGlW,SAAS,CAAC2V,cAAc,CAACK,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE7F,KAAK,CAAC;IACjF,IAAIC,aAAa,GAAGuF,cAAc,CAACK,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,IAAIL,cAAc,CAACK,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,IAAIhH,WAAW,CAACxE,iBAAiB;IACpJ,IAAI2L,gBAAgB,GAAGnW,SAAS,CAAC2V,cAAc,CAACK,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE5F,aAAa,CAAC;IACzG,IAAIgG,gBAAgB,GAAGpW,SAAS,CAAC2V,cAAc,CAACK,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAE5F,aAAa,CAAC;IACzG,IAAIvE,EAAE,GAAG,EAAE,IAAI,CAAC,CAACyJ,EAAE,GAAGD,SAAS,CAACgB,IAAI,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,KAAK,KAAK,CAAC,CAAC;IAC1FpB,KAAK,GAAG/S,IAAI,CAACsD,GAAG,CAACyP,KAAK,EAAErJ,EAAE,CAAC;IAC3BuJ,KAAK,GAAGjT,IAAI,CAACwD,GAAG,CAACyP,KAAK,EAAEvJ,EAAE,CAAC;IAC3B,IAAI8D,MAAM,GAAG,IAAItP,OAAO,CAACuP,IAAI,CAAC;MAC5B;MACA;MACA;MACA;MACA3K,CAAC,EAAE,CAAC;MACJyB,CAAC,EAAE,CAAC;MACJqC,QAAQ,EAAE,CAAC;MACX;MACAI,MAAM,EAAE/B,WAAW,CAACyD,aAAa,CAAChI,SAAS,CAAC;MAC5CgJ,EAAE,EAAEA,EAAE;MACNC,KAAK,EAAEvL,eAAe,CAACoV,cAAc,EAAE;QACrC9F,IAAI,EAAE4F,cAAc;QACpBtF,KAAK,EAAE5C,KAAK,KAAK,CAAC,GAAG0I,QAAQ,GAAG1I,KAAK,KAAKmH,MAAM,CAACxN,MAAM,GAAG,CAAC,GAAGgP,QAAQ,GAAG/F,KAAK;QAC9EC,aAAa,EAAE7C,KAAK,KAAK,CAAC,GAAG4I,gBAAgB,GAAG5I,KAAK,KAAKmH,MAAM,CAACxN,MAAM,GAAG,CAAC,GAAGkP,gBAAgB,GAAGhG,aAAa;QAC9GH,IAAI,EAAElQ,UAAU,CAACgW,SAAS,CAAC,GAAGA,SAAS;QACvC;QACA;QACA;QACA;QACA;QACA;QACA;QACAhT,IAAI,CAACwS,IAAI,KAAK,UAAU,GAAGG,QAAQ,GAAG3S,IAAI,CAACwS,IAAI,KAAK,OAAO,GAAG/C,SAAS,GAAG,EAAE,GAAGA,SAAS,EAAEjF,KAAK,CAAC,GAAGwI;MACrG,CAAC;IACH,CAAC,CAAC;IACFpG,MAAM,CAAChD,IAAI,GAAG,QAAQ,GAAG6F,SAAS;IAClC,IAAIY,KAAK,GAAG9Q,aAAa,CAACqN,MAAM,CAAC;IACjCyD,KAAK,CAAC,OAAO,CAAC,GAAGiC,SAAS,CAAC,OAAO,CAAC;IACnCjC,KAAK,CAACZ,SAAS,GAAGA,SAAS;IAC3BY,KAAK,CAACmD,cAAc,GAAGvH,WAAW,CAACjG,QAAQ;IAC3C1I,OAAO,CAACgQ,gBAAgB,CAAC;MACvBC,EAAE,EAAEX,MAAM;MACVY,cAAc,EAAE1N,SAAS;MACzB2N,QAAQ,EAAEiF,cAAc;MACxBe,oBAAoB,EAAE;QACpBC,WAAW,EAAE,SAAAA,CAAA,EAAY;UACvB,OAAO9G,MAAM,CAAC8G,WAAW;QAC3B,CAAC;QACDC,KAAK,EAAEhB,QAAQ;QACfiB,SAAS,EAAEpJ;MACb;IACF,CAAC,CAAC;IACF;IACA,IAAI0H,YAAY,EAAE;MAChB,IAAIvK,SAAS,GAAGtD,WAAW,CAACqD,qBAAqB,CAAC5H,SAAS,CAAC;MAC5D6H,SAAS,CAACgG,UAAU,GAAG,WAAW;MAClChG,SAAS,CAACgM,KAAK,GAAGhB,QAAQ;MAC1BhL,SAAS,CAACiM,SAAS,GAAGpJ,KAAK;MAC3B,IAAI8H,SAAS,CAAC,OAAO,CAAC,EAAE;QACtB3K,SAAS,CAAC,OAAO,CAAC,GAAG;UACnB;UACAkM,KAAK,EAAEvB,SAAS,CAAC,OAAO,CAAC,CAACwB,WAAW,CAACC,IAAI;UAC1CC,GAAG,EAAE1B,SAAS,CAAC,OAAO,CAAC,CAACwB,WAAW,CAACG;QACtC,CAAC;MACH;MACA,IAAIjU,IAAI,CAACwS,IAAI,KAAK,UAAU,EAAE;QAC5B7K,SAAS,CAACuM,SAAS,GAAGzE,SAAS;MACjC;MACAlS,SAAS,CAACqP,MAAM,CAAC,CAACjF,SAAS,GAAGA,SAAS;MACvC,IAAI2K,SAAS,CAAC,OAAO,CAAC,EAAE;QACtB6B,oBAAoB,CAACrU,SAAS,EAAEwE,GAAG,EAAEsI,MAAM,EAAE0F,SAAS,CAAC,OAAO,CAAC,CAAC;MAClE;IACF;IACAL,QAAQ,CAAC7Q,IAAI,CAACwL,MAAM,CAAC;IACrBsE,UAAU,CAACrH,GAAG,CAAC+C,MAAM,CAAC;EACxB,CAAC,CAAC;EACF,IAAIpM,eAAe,GAAGrD,GAAG,CAAC8U,QAAQ,EAAE,UAAU/Q,KAAK,EAAE;IACnD,OAAO;MACLA,KAAK,EAAEA,KAAK;MACZ0M,QAAQ,EAAErO,aAAa,CAAC2B,KAAK,CAAC,CAAC,OAAO,CAAC,GAAGA,KAAK,CAAC4H,EAAE,IAAIuJ,KAAK,GAAGF,KAAK,GAAG,CAAC,CAAC,CAAC;MAAA,EACvEjR,KAAK,CAAC4H,EAAE;MACV+E,WAAW,EAAE;QACX1M,MAAM,EAAED,KAAK,CAACC;MAChB;IACF,CAAC;EACH,CAAC,CAAC;EACFoQ,uBAAuB,CAACtJ,KAAK,EAAEzH,eAAe,EAAE0Q,UAAU,EAAEO,oBAAoB,CAAC;AACnF;AACA;AACA,SAASxD,0BAA0BA,CAAChG,KAAK,EAAE;EACzC,OAAO,CAAC,CAACA,KAAK,CAACzH,eAAe;AAChC;AACA,SAAS+Q,uBAAuBA,CAACtJ,KAAK,EAAEzH,eAAe,EAAE0Q,UAAU,EAAED,yBAAyB,EAAE;EAC9F;EACAhJ,KAAK,CAACzH,eAAe,GAAGA,eAAe;EACvCyH,KAAK,CAACiJ,UAAU,GAAGA,UAAU;EAC7BjJ,KAAK,CAACgJ,yBAAyB,GAAGA,yBAAyB;AAC7D;AACA,SAAS9C,6BAA6BA,CAAC7N,GAAG,EAAER,SAAS,EAAEU,eAAe,EAAEgG,cAAc,EAAE;EACtF,IAAI4N,WAAW,GAAGtU,SAAS,CAAC+F,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;EACxDjJ,IAAI,CAAC4D,eAAe,EAAE,UAAUQ,MAAM,EAAEf,GAAG,EAAE;IAC3C,IAAIoU,QAAQ,GAAG/V,6BAA6B,CAAC0C,MAAM,CAAC;IACpD,IAAI,CAACqT,QAAQ,EAAE;MACb;IACF;IACA,IAAIC,OAAO,GAAGD,QAAQ,CAACnT,KAAK;IAC5B,IAAImP,KAAK,GAAG9Q,aAAa,CAAC+U,OAAO,CAAC;IAClC;IACAD,QAAQ,CAACtF,aAAa,GAAGuF,OAAO,CAACnT,MAAM;IACvC;IACA;IACAmT,OAAO,CAACnT,MAAM,GAAG,KAAK;IACtBnC,aAAa,CAACuV,YAAY,EAAEC,iBAAiB,CAAC;IAC9CD,YAAY,CAACrS,CAAC,GAAGpC,SAAS,CAACE,IAAI,CAACyU,WAAW,CAACpE,KAAK,CAACZ,SAAS,CAAC;IAC5D8E,YAAY,CAAC5Q,CAAC,GAAGrD,GAAG,CAACmF,WAAW,GAAGnF,GAAG,CAAC6F,cAAc,GAAGiO,WAAW;IACnEG,YAAY,CAACvO,QAAQ,GAAGqK,KAAK,CAACmD,cAAc;IAC5ChN,cAAc,CAACqD,GAAG,CAAC0K,YAAY,CAAC;IAChCA,YAAY,CAAC9N,eAAe,CAAC,CAAC;IAC9BD,cAAc,CAACgF,MAAM,CAAC+I,YAAY,CAAC;IACnCA,YAAY,CAACxG,kBAAkB,CAAC,CAAC;IACjC/O,aAAa,CAACsV,OAAO,EAAEC,YAAY,CAAC;IACpCD,OAAO,CAACI,UAAU,CAAC,CAAC;IACpBlW,mBAAmB,CAAC6V,QAAQ,EAAE,IAAI,CAAC;IACnC/V,6BAA6B,CAAC+V,QAAQ,CAAC;EACzC,CAAC,CAAC;AACJ;AACA,IAAIE,YAAY,GAAG,IAAIjX,OAAO,CAACqX,IAAI,CAAC,CAAC;AACrC,IAAIH,iBAAiB,GAAG,IAAIlX,OAAO,CAACqX,IAAI,CAAC,CAAC;AAC1C,SAAS9T,WAAWA,CAACC,QAAQ,EAAE;EAC7B,OAAO,CAAC,CAACA,QAAQ;AACnB;AACA,SAASqT,oBAAoBA,CAACrU,SAAS,EAAEwE,GAAG,EAAEsI,MAAM,EAAEgI,WAAW,EAAE;EACjEhI,MAAM,CAACiI,EAAE,CAAC,OAAO,EAAE,UAAUC,MAAM,EAAE;IACnC,IAAIC,OAAO,GAAG;MACZvC,IAAI,EAAE5T,6BAA6B;MACnCoW,MAAM,EAAE,CAAC;QACPnB,KAAK,EAAEe,WAAW,CAACd,WAAW,CAACmB,WAAW,CAACpB,KAAK;QAChDG,GAAG,EAAEY,WAAW,CAACd,WAAW,CAACmB,WAAW,CAACjB;MAC3C,CAAC;IACH,CAAC;IACDe,OAAO,CAACjV,SAAS,CAACE,IAAI,CAACD,GAAG,GAAG,WAAW,CAAC,GAAGD,SAAS,CAACI,cAAc;IACpEoE,GAAG,CAAC4Q,cAAc,CAACH,OAAO,CAAC;EAC7B,CAAC,CAAC;AACJ;AACA,SAAS3G,iBAAiBA,CAACtO,SAAS,EAAEuH,YAAY,EAAE7G,eAAe,EAAE;EACnE,IAAI2U,gBAAgB,GAAGtW,mBAAmB,CAAC,CAAC;EAC5C,IAAI,CAACsW,gBAAgB,EAAE;IACrB;EACF;EACA,IAAIC,oBAAoB,GAAGD,gBAAgB,CAACE,sBAAsB,CAAC7U,eAAe,EAAE,UAAUS,UAAU,EAAE;IACxG,OAAOA,UAAU,IAAI1B,aAAa,CAAC0B,UAAU,CAACC,KAAK,CAAC,CAAC,OAAO,CAAC;EAC/D,CAAC,EAAE,IAAI,CAAC;EACR,IAAIoU,WAAW,GAAGxV,SAAS,CAAC+F,GAAG,CAAC,CAAC,kBAAkB,EAAE,aAAa,CAAC,EAAE,IAAI,CAAC;EAC1E,IAAIyP,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,MAAM,EAAE;IAClD1Y,IAAI,CAACwY,oBAAoB,EAAE,UAAUG,OAAO,EAAE;MAC5C5W,kBAAkB,CAAC,CAAC,CAAC6W,oBAAoB,CAAC1V,SAAS,CAACE,IAAI,CAACuI,OAAO,EAAElB,YAAY,EAAE,CAAC/I,6BAA6B,CAACkC,eAAe,CAAC+U,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjX,6BAA6B,CAACkC,eAAe,CAAC+U,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3M,CAAC,CAAC;EACJ;AACF;AACA,eAAelR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}