{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nimport { getIntervalPrecision, logTransform } from './helper.js';\nimport { getScaleBreakHelper } from './break.js';\nvar fixRound = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    return _this;\n  }\n  /**\n   * @param Whether expand the ticks to niced extent.\n   */\n  LogScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var extent = this._extent.slice();\n    var originalExtent = this._originalScale.getExtent();\n    var ticks = _super.prototype.getTicks.call(this, opt);\n    var base = this.base;\n    var originalBreaks = this._originalScale._innerGetBreaks();\n    var scaleBreakHelper = getScaleBreakHelper();\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var roundingCriterion = null;\n      var powVal = mathPow(base, val);\n      // Fix #4158\n      if (val === extent[0] && this._fixMin) {\n        roundingCriterion = originalExtent[0];\n      } else if (val === extent[1] && this._fixMax) {\n        roundingCriterion = originalExtent[1];\n      }\n      var vBreak;\n      if (scaleBreakHelper) {\n        var transformed = scaleBreakHelper.getTicksLogTransformBreak(tick, base, originalBreaks, fixRoundingError);\n        vBreak = transformed.vBreak;\n        if (roundingCriterion == null) {\n          roundingCriterion = transformed.brkRoundingCriterion;\n        }\n      }\n      if (roundingCriterion != null) {\n        powVal = fixRoundingError(powVal, roundingCriterion);\n      }\n      return {\n        value: powVal,\n        \"break\": vBreak\n      };\n    }, this);\n  };\n  LogScale.prototype._getNonTransBreaks = function () {\n    return this._originalScale._innerGetBreaks();\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    this._originalScale.setExtent(start, end);\n    var loggedExtent = logTransform(this.base, [start, end]);\n    _super.prototype.setExtent.call(this, loggedExtent[0], loggedExtent[1]);\n  };\n  /**\n   * @return {number} end\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = _super.prototype.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalExtent = this._originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    this._originalScale.unionExtentFromData(data, dim);\n    var loggedOther = logTransform(this.base, data.getApproximateExtent(dim), true);\n    this._innerUnionExtent(loggedOther);\n  };\n  /**\n   * Update interval and extent of intervals for nice ticks\n   * @param approxTickNum default 10 Given approx tick number\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent.slice();\n    var span = this._getExtentSpanWithBreaks();\n    if (!isFinite(span) || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [fixRound(mathCeil(extent[0] / interval) * interval), fixRound(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._intervalPrecision = getIntervalPrecision(interval);\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    _super.prototype.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return _super.prototype.contain.call(this, val);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return _super.prototype.normalize.call(this, val);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = _super.prototype.scale.call(this, val);\n    return mathPow(this.base, val);\n  };\n  LogScale.prototype.setBreaksFromOption = function (breakOptionList) {\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (!scaleBreakHelper) {\n      return;\n    }\n    var _a = scaleBreakHelper.logarithmicParseBreaksFromOption(breakOptionList, this.base, zrUtil.bind(this.parse, this)),\n      parsedOriginal = _a.parsedOriginal,\n      parsedLogged = _a.parsedLogged;\n    this._originalScale._innerSetBreak(parsedOriginal);\n    this._innerSetBreak(parsedLogged);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(IntervalScale);\nfunction fixRoundingError(val, originalVal) {\n  return fixRound(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Scale", "numberUtil", "IntervalScale", "getIntervalPrecision", "logTransform", "getScaleBreakHelper", "fixRound", "round", "mathFloor", "Math", "floor", "math<PERSON>eil", "ceil", "mathPow", "pow", "mathLog", "log", "LogScale", "_super", "_this", "apply", "arguments", "type", "base", "_originalScale", "prototype", "getTicks", "opt", "extent", "_extent", "slice", "originalExtent", "getExtent", "ticks", "call", "originalBreaks", "_innerGetBreaks", "scaleBreakHelper", "map", "tick", "val", "value", "roundingCriterion", "powVal", "_fixMin", "_fixMax", "vBreak", "transformed", "getTicksLogTransformBreak", "fixRoundingError", "brkRoundingCriterion", "_getNonTransBreaks", "setExtent", "start", "end", "loggedExtent", "unionExtentFromData", "data", "dim", "loggedOther", "getApproximateExtent", "_innerUnionExtent", "calcNiceTicks", "approxTickNum", "span", "_getExtentSpanWithBreaks", "isFinite", "interval", "quantity", "err", "isNaN", "abs", "niceExtent", "_interval", "_intervalPrecision", "_niceExtent", "calcNiceExtent", "fixMin", "fixMax", "contain", "normalize", "scale", "setBreaksFromOption", "breakOptionList", "_a", "logarithmicParseBreaksFromOption", "bind", "parse", "parsedOriginal", "parsedLogged", "_innerSetBreak", "originalVal", "getPrecision", "registerClass"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/scale/Log.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nimport { getIntervalPrecision, logTransform } from './helper.js';\nimport { getScaleBreakHelper } from './break.js';\nvar fixRound = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    return _this;\n  }\n  /**\n   * @param Whether expand the ticks to niced extent.\n   */\n  LogScale.prototype.getTicks = function (opt) {\n    opt = opt || {};\n    var extent = this._extent.slice();\n    var originalExtent = this._originalScale.getExtent();\n    var ticks = _super.prototype.getTicks.call(this, opt);\n    var base = this.base;\n    var originalBreaks = this._originalScale._innerGetBreaks();\n    var scaleBreakHelper = getScaleBreakHelper();\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var roundingCriterion = null;\n      var powVal = mathPow(base, val);\n      // Fix #4158\n      if (val === extent[0] && this._fixMin) {\n        roundingCriterion = originalExtent[0];\n      } else if (val === extent[1] && this._fixMax) {\n        roundingCriterion = originalExtent[1];\n      }\n      var vBreak;\n      if (scaleBreakHelper) {\n        var transformed = scaleBreakHelper.getTicksLogTransformBreak(tick, base, originalBreaks, fixRoundingError);\n        vBreak = transformed.vBreak;\n        if (roundingCriterion == null) {\n          roundingCriterion = transformed.brkRoundingCriterion;\n        }\n      }\n      if (roundingCriterion != null) {\n        powVal = fixRoundingError(powVal, roundingCriterion);\n      }\n      return {\n        value: powVal,\n        \"break\": vBreak\n      };\n    }, this);\n  };\n  LogScale.prototype._getNonTransBreaks = function () {\n    return this._originalScale._innerGetBreaks();\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    this._originalScale.setExtent(start, end);\n    var loggedExtent = logTransform(this.base, [start, end]);\n    _super.prototype.setExtent.call(this, loggedExtent[0], loggedExtent[1]);\n  };\n  /**\n   * @return {number} end\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = _super.prototype.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalExtent = this._originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    this._originalScale.unionExtentFromData(data, dim);\n    var loggedOther = logTransform(this.base, data.getApproximateExtent(dim), true);\n    this._innerUnionExtent(loggedOther);\n  };\n  /**\n   * Update interval and extent of intervals for nice ticks\n   * @param approxTickNum default 10 Given approx tick number\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent.slice();\n    var span = this._getExtentSpanWithBreaks();\n    if (!isFinite(span) || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [fixRound(mathCeil(extent[0] / interval) * interval), fixRound(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._intervalPrecision = getIntervalPrecision(interval);\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    _super.prototype.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return _super.prototype.contain.call(this, val);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return _super.prototype.normalize.call(this, val);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = _super.prototype.scale.call(this, val);\n    return mathPow(this.base, val);\n  };\n  LogScale.prototype.setBreaksFromOption = function (breakOptionList) {\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (!scaleBreakHelper) {\n      return;\n    }\n    var _a = scaleBreakHelper.logarithmicParseBreaksFromOption(breakOptionList, this.base, zrUtil.bind(this.parse, this)),\n      parsedOriginal = _a.parsedOriginal,\n      parsedLogged = _a.parsedLogged;\n    this._originalScale._innerSetBreak(parsedOriginal);\n    this._innerSetBreak(parsedLogged);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(IntervalScale);\nfunction fixRoundingError(val, originalVal) {\n  return fixRound(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C;AACA,OAAOC,aAAa,MAAM,eAAe;AACzC,SAASC,oBAAoB,EAAEC,YAAY,QAAQ,aAAa;AAChE,SAASC,mBAAmB,QAAQ,YAAY;AAChD,IAAIC,QAAQ,GAAGL,UAAU,CAACM,KAAK;AAC/B,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK;AAC1B,IAAIC,QAAQ,GAAGF,IAAI,CAACG,IAAI;AACxB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;AACtB,IAAIC,OAAO,GAAGN,IAAI,CAACO,GAAG;AACtB,IAAIC,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CpB,SAAS,CAACmB,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,KAAK;IAClBH,KAAK,CAACI,IAAI,GAAG,EAAE;IACfJ,KAAK,CAACK,cAAc,GAAG,IAAItB,aAAa,CAAC,CAAC;IAC1C,OAAOiB,KAAK;EACd;EACA;AACF;AACA;EACEF,QAAQ,CAACQ,SAAS,CAACC,QAAQ,GAAG,UAAUC,GAAG,EAAE;IAC3CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC,IAAIC,cAAc,GAAG,IAAI,CAACP,cAAc,CAACQ,SAAS,CAAC,CAAC;IACpD,IAAIC,KAAK,GAAGf,MAAM,CAACO,SAAS,CAACC,QAAQ,CAACQ,IAAI,CAAC,IAAI,EAAEP,GAAG,CAAC;IACrD,IAAIJ,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIY,cAAc,GAAG,IAAI,CAACX,cAAc,CAACY,eAAe,CAAC,CAAC;IAC1D,IAAIC,gBAAgB,GAAGhC,mBAAmB,CAAC,CAAC;IAC5C,OAAON,MAAM,CAACuC,GAAG,CAACL,KAAK,EAAE,UAAUM,IAAI,EAAE;MACvC,IAAIC,GAAG,GAAGD,IAAI,CAACE,KAAK;MACpB,IAAIC,iBAAiB,GAAG,IAAI;MAC5B,IAAIC,MAAM,GAAG9B,OAAO,CAACU,IAAI,EAAEiB,GAAG,CAAC;MAC/B;MACA,IAAIA,GAAG,KAAKZ,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAACgB,OAAO,EAAE;QACrCF,iBAAiB,GAAGX,cAAc,CAAC,CAAC,CAAC;MACvC,CAAC,MAAM,IAAIS,GAAG,KAAKZ,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAACiB,OAAO,EAAE;QAC5CH,iBAAiB,GAAGX,cAAc,CAAC,CAAC,CAAC;MACvC;MACA,IAAIe,MAAM;MACV,IAAIT,gBAAgB,EAAE;QACpB,IAAIU,WAAW,GAAGV,gBAAgB,CAACW,yBAAyB,CAACT,IAAI,EAAEhB,IAAI,EAAEY,cAAc,EAAEc,gBAAgB,CAAC;QAC1GH,MAAM,GAAGC,WAAW,CAACD,MAAM;QAC3B,IAAIJ,iBAAiB,IAAI,IAAI,EAAE;UAC7BA,iBAAiB,GAAGK,WAAW,CAACG,oBAAoB;QACtD;MACF;MACA,IAAIR,iBAAiB,IAAI,IAAI,EAAE;QAC7BC,MAAM,GAAGM,gBAAgB,CAACN,MAAM,EAAED,iBAAiB,CAAC;MACtD;MACA,OAAO;QACLD,KAAK,EAAEE,MAAM;QACb,OAAO,EAAEG;MACX,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD7B,QAAQ,CAACQ,SAAS,CAAC0B,kBAAkB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAC3B,cAAc,CAACY,eAAe,CAAC,CAAC;EAC9C,CAAC;EACDnB,QAAQ,CAACQ,SAAS,CAAC2B,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACnD,IAAI,CAAC9B,cAAc,CAAC4B,SAAS,CAACC,KAAK,EAAEC,GAAG,CAAC;IACzC,IAAIC,YAAY,GAAGnD,YAAY,CAAC,IAAI,CAACmB,IAAI,EAAE,CAAC8B,KAAK,EAAEC,GAAG,CAAC,CAAC;IACxDpC,MAAM,CAACO,SAAS,CAAC2B,SAAS,CAAClB,IAAI,CAAC,IAAI,EAAEqB,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC;EACD;AACF;AACA;EACEtC,QAAQ,CAACQ,SAAS,CAACO,SAAS,GAAG,YAAY;IACzC,IAAIT,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIK,MAAM,GAAGV,MAAM,CAACO,SAAS,CAACO,SAAS,CAACE,IAAI,CAAC,IAAI,CAAC;IAClDN,MAAM,CAAC,CAAC,CAAC,GAAGf,OAAO,CAACU,IAAI,EAAEK,MAAM,CAAC,CAAC,CAAC,CAAC;IACpCA,MAAM,CAAC,CAAC,CAAC,GAAGf,OAAO,CAACU,IAAI,EAAEK,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC;IACA,IAAIG,cAAc,GAAG,IAAI,CAACP,cAAc,CAACQ,SAAS,CAAC,CAAC;IACpD,IAAI,CAACY,OAAO,KAAKhB,MAAM,CAAC,CAAC,CAAC,GAAGqB,gBAAgB,CAACrB,MAAM,CAAC,CAAC,CAAC,EAAEG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACc,OAAO,KAAKjB,MAAM,CAAC,CAAC,CAAC,GAAGqB,gBAAgB,CAACrB,MAAM,CAAC,CAAC,CAAC,EAAEG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,OAAOH,MAAM;EACf,CAAC;EACDX,QAAQ,CAACQ,SAAS,CAAC+B,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAC5D,IAAI,CAAClC,cAAc,CAACgC,mBAAmB,CAACC,IAAI,EAAEC,GAAG,CAAC;IAClD,IAAIC,WAAW,GAAGvD,YAAY,CAAC,IAAI,CAACmB,IAAI,EAAEkC,IAAI,CAACG,oBAAoB,CAACF,GAAG,CAAC,EAAE,IAAI,CAAC;IAC/E,IAAI,CAACG,iBAAiB,CAACF,WAAW,CAAC;EACrC,CAAC;EACD;AACF;AACA;AACA;EACE1C,QAAQ,CAACQ,SAAS,CAACqC,aAAa,GAAG,UAAUC,aAAa,EAAE;IAC1DA,aAAa,GAAGA,aAAa,IAAI,EAAE;IACnC,IAAInC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC,IAAIkC,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC1C,IAAI,CAACC,QAAQ,CAACF,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,EAAE;MAChC;IACF;IACA,IAAIG,QAAQ,GAAGlE,UAAU,CAACmE,QAAQ,CAACJ,IAAI,CAAC;IACxC,IAAIK,GAAG,GAAGN,aAAa,GAAGC,IAAI,GAAGG,QAAQ;IACzC;IACA,IAAIE,GAAG,IAAI,GAAG,EAAE;MACdF,QAAQ,IAAI,EAAE;IAChB;IACA;IACA,OAAO,CAACG,KAAK,CAACH,QAAQ,CAAC,IAAI1D,IAAI,CAAC8D,GAAG,CAACJ,QAAQ,CAAC,GAAG,CAAC,IAAI1D,IAAI,CAAC8D,GAAG,CAACJ,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC3EA,QAAQ,IAAI,EAAE;IAChB;IACA,IAAIK,UAAU,GAAG,CAAClE,QAAQ,CAACK,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,GAAGuC,QAAQ,CAAC,GAAGA,QAAQ,CAAC,EAAE7D,QAAQ,CAACE,SAAS,CAACoB,MAAM,CAAC,CAAC,CAAC,GAAGuC,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC;IAC5H,IAAI,CAACM,SAAS,GAAGN,QAAQ;IACzB,IAAI,CAACO,kBAAkB,GAAGvE,oBAAoB,CAACgE,QAAQ,CAAC;IACxD,IAAI,CAACQ,WAAW,GAAGH,UAAU;EAC/B,CAAC;EACDvD,QAAQ,CAACQ,SAAS,CAACmD,cAAc,GAAG,UAAUjD,GAAG,EAAE;IACjDT,MAAM,CAACO,SAAS,CAACmD,cAAc,CAAC1C,IAAI,CAAC,IAAI,EAAEP,GAAG,CAAC;IAC/C,IAAI,CAACiB,OAAO,GAAGjB,GAAG,CAACkD,MAAM;IACzB,IAAI,CAAChC,OAAO,GAAGlB,GAAG,CAACmD,MAAM;EAC3B,CAAC;EACD7D,QAAQ,CAACQ,SAAS,CAACsD,OAAO,GAAG,UAAUvC,GAAG,EAAE;IAC1CA,GAAG,GAAGzB,OAAO,CAACyB,GAAG,CAAC,GAAGzB,OAAO,CAAC,IAAI,CAACQ,IAAI,CAAC;IACvC,OAAOL,MAAM,CAACO,SAAS,CAACsD,OAAO,CAAC7C,IAAI,CAAC,IAAI,EAAEM,GAAG,CAAC;EACjD,CAAC;EACDvB,QAAQ,CAACQ,SAAS,CAACuD,SAAS,GAAG,UAAUxC,GAAG,EAAE;IAC5CA,GAAG,GAAGzB,OAAO,CAACyB,GAAG,CAAC,GAAGzB,OAAO,CAAC,IAAI,CAACQ,IAAI,CAAC;IACvC,OAAOL,MAAM,CAACO,SAAS,CAACuD,SAAS,CAAC9C,IAAI,CAAC,IAAI,EAAEM,GAAG,CAAC;EACnD,CAAC;EACDvB,QAAQ,CAACQ,SAAS,CAACwD,KAAK,GAAG,UAAUzC,GAAG,EAAE;IACxCA,GAAG,GAAGtB,MAAM,CAACO,SAAS,CAACwD,KAAK,CAAC/C,IAAI,CAAC,IAAI,EAAEM,GAAG,CAAC;IAC5C,OAAO3B,OAAO,CAAC,IAAI,CAACU,IAAI,EAAEiB,GAAG,CAAC;EAChC,CAAC;EACDvB,QAAQ,CAACQ,SAAS,CAACyD,mBAAmB,GAAG,UAAUC,eAAe,EAAE;IAClE,IAAI9C,gBAAgB,GAAGhC,mBAAmB,CAAC,CAAC;IAC5C,IAAI,CAACgC,gBAAgB,EAAE;MACrB;IACF;IACA,IAAI+C,EAAE,GAAG/C,gBAAgB,CAACgD,gCAAgC,CAACF,eAAe,EAAE,IAAI,CAAC5D,IAAI,EAAExB,MAAM,CAACuF,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC,CAAC;MACnHC,cAAc,GAAGJ,EAAE,CAACI,cAAc;MAClCC,YAAY,GAAGL,EAAE,CAACK,YAAY;IAChC,IAAI,CAACjE,cAAc,CAACkE,cAAc,CAACF,cAAc,CAAC;IAClD,IAAI,CAACE,cAAc,CAACD,YAAY,CAAC;EACnC,CAAC;EACDxE,QAAQ,CAACK,IAAI,GAAG,KAAK;EACrB,OAAOL,QAAQ;AACjB,CAAC,CAACf,aAAa,CAAC;AAChB,SAAS+C,gBAAgBA,CAACT,GAAG,EAAEmD,WAAW,EAAE;EAC1C,OAAOrF,QAAQ,CAACkC,GAAG,EAAEvC,UAAU,CAAC2F,YAAY,CAACD,WAAW,CAAC,CAAC;AAC5D;AACA3F,KAAK,CAAC6F,aAAa,CAAC5E,QAAQ,CAAC;AAC7B,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}