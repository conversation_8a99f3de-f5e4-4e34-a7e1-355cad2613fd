{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Link lists and struct (graph or tree)\n */\nimport { curry, each, assert, extend, map, keys } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nfunction linkSeriesData(opt) {\n  var mainData = opt.mainData;\n  var datas = opt.datas;\n  if (!datas) {\n    datas = {\n      main: mainData\n    };\n    opt.datasAttr = {\n      main: 'data'\n    };\n  }\n  opt.datas = opt.mainData = null;\n  linkAll(mainData, datas, opt);\n  // Porxy data original methods.\n  each(datas, function (data) {\n    each(mainData.TRANSFERABLE_METHODS, function (methodName) {\n      data.wrapMethod(methodName, curry(transferInjection, opt));\n    });\n  });\n  // Beyond transfer, additional features should be added to `cloneShallow`.\n  mainData.wrapMethod('cloneShallow', curry(cloneShallowInjection, opt));\n  // Only mainData trigger change, because struct.update may trigger\n  // another changable methods, which may bring about dead lock.\n  each(mainData.CHANGABLE_METHODS, function (methodName) {\n    mainData.wrapMethod(methodName, curry(changeInjection, opt));\n  });\n  // Make sure datas contains mainData.\n  assert(datas[mainData.dataType] === mainData);\n}\nfunction transferInjection(opt, res) {\n  if (isMainData(this)) {\n    // Transfer datas to new main data.\n    var datas = extend({}, inner(this).datas);\n    datas[this.dataType] = res;\n    linkAll(res, datas, opt);\n  } else {\n    // Modify the reference in main data to point newData.\n    linkSingle(res, this.dataType, inner(this).mainData, opt);\n  }\n  return res;\n}\nfunction changeInjection(opt, res) {\n  opt.struct && opt.struct.update();\n  return res;\n}\nfunction cloneShallowInjection(opt, res) {\n  // cloneShallow, which brings about some fragilities, may be inappropriate\n  // to be exposed as an API. So for implementation simplicity we can make\n  // the restriction that cloneShallow of not-mainData should not be invoked\n  // outside, but only be invoked here.\n  each(inner(res).datas, function (data, dataType) {\n    data !== res && linkSingle(data.cloneShallow(), dataType, res, opt);\n  });\n  return res;\n}\n/**\n * Supplement method to List.\n *\n * @public\n * @param [dataType] If not specified, return mainData.\n */\nfunction getLinkedData(dataType) {\n  var mainData = inner(this).mainData;\n  return dataType == null || mainData == null ? mainData : inner(mainData).datas[dataType];\n}\n/**\n * Get list of all linked data\n */\nfunction getLinkedDataAll() {\n  var mainData = inner(this).mainData;\n  return mainData == null ? [{\n    data: mainData\n  }] : map(keys(inner(mainData).datas), function (type) {\n    return {\n      type: type,\n      data: inner(mainData).datas[type]\n    };\n  });\n}\nfunction isMainData(data) {\n  return inner(data).mainData === data;\n}\nfunction linkAll(mainData, datas, opt) {\n  inner(mainData).datas = {};\n  each(datas, function (data, dataType) {\n    linkSingle(data, dataType, mainData, opt);\n  });\n}\nfunction linkSingle(data, dataType, mainData, opt) {\n  inner(mainData).datas[dataType] = data;\n  inner(data).mainData = mainData;\n  data.dataType = dataType;\n  if (opt.struct) {\n    data[opt.structAttr] = opt.struct;\n    opt.struct[opt.datasAttr[dataType]] = data;\n  }\n  // Supplement method.\n  data.getLinkedData = getLinkedData;\n  data.getLinkedDataAll = getLinkedDataAll;\n}\nexport default linkSeriesData;", "map": {"version": 3, "names": ["curry", "each", "assert", "extend", "map", "keys", "makeInner", "inner", "linkSeriesData", "opt", "mainData", "datas", "main", "datasAttr", "linkAll", "data", "TRANSFERABLE_METHODS", "methodName", "wrapMethod", "transferInjection", "cloneShallowInjection", "CHANGABLE_METHODS", "changeInjection", "dataType", "res", "isMainData", "linkSingle", "struct", "update", "cloneShallow", "getLinkedData", "getLinkedDataAll", "type", "structAttr"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/data/helper/linkSeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Link lists and struct (graph or tree)\n */\nimport { curry, each, assert, extend, map, keys } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nfunction linkSeriesData(opt) {\n  var mainData = opt.mainData;\n  var datas = opt.datas;\n  if (!datas) {\n    datas = {\n      main: mainData\n    };\n    opt.datasAttr = {\n      main: 'data'\n    };\n  }\n  opt.datas = opt.mainData = null;\n  linkAll(mainData, datas, opt);\n  // Porxy data original methods.\n  each(datas, function (data) {\n    each(mainData.TRANSFERABLE_METHODS, function (methodName) {\n      data.wrapMethod(methodName, curry(transferInjection, opt));\n    });\n  });\n  // Beyond transfer, additional features should be added to `cloneShallow`.\n  mainData.wrapMethod('cloneShallow', curry(cloneShallowInjection, opt));\n  // Only mainData trigger change, because struct.update may trigger\n  // another changable methods, which may bring about dead lock.\n  each(mainData.CHANGABLE_METHODS, function (methodName) {\n    mainData.wrapMethod(methodName, curry(changeInjection, opt));\n  });\n  // Make sure datas contains mainData.\n  assert(datas[mainData.dataType] === mainData);\n}\nfunction transferInjection(opt, res) {\n  if (isMainData(this)) {\n    // Transfer datas to new main data.\n    var datas = extend({}, inner(this).datas);\n    datas[this.dataType] = res;\n    linkAll(res, datas, opt);\n  } else {\n    // Modify the reference in main data to point newData.\n    linkSingle(res, this.dataType, inner(this).mainData, opt);\n  }\n  return res;\n}\nfunction changeInjection(opt, res) {\n  opt.struct && opt.struct.update();\n  return res;\n}\nfunction cloneShallowInjection(opt, res) {\n  // cloneShallow, which brings about some fragilities, may be inappropriate\n  // to be exposed as an API. So for implementation simplicity we can make\n  // the restriction that cloneShallow of not-mainData should not be invoked\n  // outside, but only be invoked here.\n  each(inner(res).datas, function (data, dataType) {\n    data !== res && linkSingle(data.cloneShallow(), dataType, res, opt);\n  });\n  return res;\n}\n/**\n * Supplement method to List.\n *\n * @public\n * @param [dataType] If not specified, return mainData.\n */\nfunction getLinkedData(dataType) {\n  var mainData = inner(this).mainData;\n  return dataType == null || mainData == null ? mainData : inner(mainData).datas[dataType];\n}\n/**\n * Get list of all linked data\n */\nfunction getLinkedDataAll() {\n  var mainData = inner(this).mainData;\n  return mainData == null ? [{\n    data: mainData\n  }] : map(keys(inner(mainData).datas), function (type) {\n    return {\n      type: type,\n      data: inner(mainData).datas[type]\n    };\n  });\n}\nfunction isMainData(data) {\n  return inner(data).mainData === data;\n}\nfunction linkAll(mainData, datas, opt) {\n  inner(mainData).datas = {};\n  each(datas, function (data, dataType) {\n    linkSingle(data, dataType, mainData, opt);\n  });\n}\nfunction linkSingle(data, dataType, mainData, opt) {\n  inner(mainData).datas[dataType] = data;\n  inner(data).mainData = mainData;\n  data.dataType = dataType;\n  if (opt.struct) {\n    data[opt.structAttr] = opt.struct;\n    opt.struct[opt.datasAttr[dataType]] = data;\n  }\n  // Supplement method.\n  data.getLinkedData = getLinkedData;\n  data.getLinkedDataAll = getLinkedDataAll;\n}\nexport default linkSeriesData;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,QAAQ,0BAA0B;AACjF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,SAASE,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ;EAC3B,IAAIC,KAAK,GAAGF,GAAG,CAACE,KAAK;EACrB,IAAI,CAACA,KAAK,EAAE;IACVA,KAAK,GAAG;MACNC,IAAI,EAAEF;IACR,CAAC;IACDD,GAAG,CAACI,SAAS,GAAG;MACdD,IAAI,EAAE;IACR,CAAC;EACH;EACAH,GAAG,CAACE,KAAK,GAAGF,GAAG,CAACC,QAAQ,GAAG,IAAI;EAC/BI,OAAO,CAACJ,QAAQ,EAAEC,KAAK,EAAEF,GAAG,CAAC;EAC7B;EACAR,IAAI,CAACU,KAAK,EAAE,UAAUI,IAAI,EAAE;IAC1Bd,IAAI,CAACS,QAAQ,CAACM,oBAAoB,EAAE,UAAUC,UAAU,EAAE;MACxDF,IAAI,CAACG,UAAU,CAACD,UAAU,EAAEjB,KAAK,CAACmB,iBAAiB,EAAEV,GAAG,CAAC,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;EACAC,QAAQ,CAACQ,UAAU,CAAC,cAAc,EAAElB,KAAK,CAACoB,qBAAqB,EAAEX,GAAG,CAAC,CAAC;EACtE;EACA;EACAR,IAAI,CAACS,QAAQ,CAACW,iBAAiB,EAAE,UAAUJ,UAAU,EAAE;IACrDP,QAAQ,CAACQ,UAAU,CAACD,UAAU,EAAEjB,KAAK,CAACsB,eAAe,EAAEb,GAAG,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF;EACAP,MAAM,CAACS,KAAK,CAACD,QAAQ,CAACa,QAAQ,CAAC,KAAKb,QAAQ,CAAC;AAC/C;AACA,SAASS,iBAAiBA,CAACV,GAAG,EAAEe,GAAG,EAAE;EACnC,IAAIC,UAAU,CAAC,IAAI,CAAC,EAAE;IACpB;IACA,IAAId,KAAK,GAAGR,MAAM,CAAC,CAAC,CAAC,EAAEI,KAAK,CAAC,IAAI,CAAC,CAACI,KAAK,CAAC;IACzCA,KAAK,CAAC,IAAI,CAACY,QAAQ,CAAC,GAAGC,GAAG;IAC1BV,OAAO,CAACU,GAAG,EAAEb,KAAK,EAAEF,GAAG,CAAC;EAC1B,CAAC,MAAM;IACL;IACAiB,UAAU,CAACF,GAAG,EAAE,IAAI,CAACD,QAAQ,EAAEhB,KAAK,CAAC,IAAI,CAAC,CAACG,QAAQ,EAAED,GAAG,CAAC;EAC3D;EACA,OAAOe,GAAG;AACZ;AACA,SAASF,eAAeA,CAACb,GAAG,EAAEe,GAAG,EAAE;EACjCf,GAAG,CAACkB,MAAM,IAAIlB,GAAG,CAACkB,MAAM,CAACC,MAAM,CAAC,CAAC;EACjC,OAAOJ,GAAG;AACZ;AACA,SAASJ,qBAAqBA,CAACX,GAAG,EAAEe,GAAG,EAAE;EACvC;EACA;EACA;EACA;EACAvB,IAAI,CAACM,KAAK,CAACiB,GAAG,CAAC,CAACb,KAAK,EAAE,UAAUI,IAAI,EAAEQ,QAAQ,EAAE;IAC/CR,IAAI,KAAKS,GAAG,IAAIE,UAAU,CAACX,IAAI,CAACc,YAAY,CAAC,CAAC,EAAEN,QAAQ,EAAEC,GAAG,EAAEf,GAAG,CAAC;EACrE,CAAC,CAAC;EACF,OAAOe,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAACP,QAAQ,EAAE;EAC/B,IAAIb,QAAQ,GAAGH,KAAK,CAAC,IAAI,CAAC,CAACG,QAAQ;EACnC,OAAOa,QAAQ,IAAI,IAAI,IAAIb,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAAC,CAACC,KAAK,CAACY,QAAQ,CAAC;AAC1F;AACA;AACA;AACA;AACA,SAASQ,gBAAgBA,CAAA,EAAG;EAC1B,IAAIrB,QAAQ,GAAGH,KAAK,CAAC,IAAI,CAAC,CAACG,QAAQ;EACnC,OAAOA,QAAQ,IAAI,IAAI,GAAG,CAAC;IACzBK,IAAI,EAAEL;EACR,CAAC,CAAC,GAAGN,GAAG,CAACC,IAAI,CAACE,KAAK,CAACG,QAAQ,CAAC,CAACC,KAAK,CAAC,EAAE,UAAUqB,IAAI,EAAE;IACpD,OAAO;MACLA,IAAI,EAAEA,IAAI;MACVjB,IAAI,EAAER,KAAK,CAACG,QAAQ,CAAC,CAACC,KAAK,CAACqB,IAAI;IAClC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASP,UAAUA,CAACV,IAAI,EAAE;EACxB,OAAOR,KAAK,CAACQ,IAAI,CAAC,CAACL,QAAQ,KAAKK,IAAI;AACtC;AACA,SAASD,OAAOA,CAACJ,QAAQ,EAAEC,KAAK,EAAEF,GAAG,EAAE;EACrCF,KAAK,CAACG,QAAQ,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC;EAC1BV,IAAI,CAACU,KAAK,EAAE,UAAUI,IAAI,EAAEQ,QAAQ,EAAE;IACpCG,UAAU,CAACX,IAAI,EAAEQ,QAAQ,EAAEb,QAAQ,EAAED,GAAG,CAAC;EAC3C,CAAC,CAAC;AACJ;AACA,SAASiB,UAAUA,CAACX,IAAI,EAAEQ,QAAQ,EAAEb,QAAQ,EAAED,GAAG,EAAE;EACjDF,KAAK,CAACG,QAAQ,CAAC,CAACC,KAAK,CAACY,QAAQ,CAAC,GAAGR,IAAI;EACtCR,KAAK,CAACQ,IAAI,CAAC,CAACL,QAAQ,GAAGA,QAAQ;EAC/BK,IAAI,CAACQ,QAAQ,GAAGA,QAAQ;EACxB,IAAId,GAAG,CAACkB,MAAM,EAAE;IACdZ,IAAI,CAACN,GAAG,CAACwB,UAAU,CAAC,GAAGxB,GAAG,CAACkB,MAAM;IACjClB,GAAG,CAACkB,MAAM,CAAClB,GAAG,CAACI,SAAS,CAACU,QAAQ,CAAC,CAAC,GAAGR,IAAI;EAC5C;EACA;EACAA,IAAI,CAACe,aAAa,GAAGA,aAAa;EAClCf,IAAI,CAACgB,gBAAgB,GAAGA,gBAAgB;AAC1C;AACA,eAAevB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}