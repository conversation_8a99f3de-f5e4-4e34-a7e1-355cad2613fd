{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport tokens from '../visual/tokens.js';\nvar defaultOption = {\n  show: true,\n  // zlevel: 0,\n  z: 0,\n  // Inverse the axis.\n  inverse: false,\n  // Axis name displayed.\n  name: '',\n  // 'start' | 'middle' | 'end'\n  nameLocation: 'end',\n  // By degree. By default auto rotate by nameLocation.\n  nameRotate: null,\n  nameTruncate: {\n    maxWidth: null,\n    ellipsis: '...',\n    placeholder: '.'\n  },\n  // Use global text style by default.\n  nameTextStyle: {\n    // textMargin: never, // The default value will be specified based on `nameLocation`.\n  },\n  // The gap between axisName and axisLine.\n  nameGap: 15,\n  // Default `false` to support tooltip.\n  silent: false,\n  // Default `false` to avoid legacy user event listener fail.\n  triggerEvent: false,\n  tooltip: {\n    show: false\n  },\n  axisPointer: {},\n  axisLine: {\n    show: true,\n    onZero: true,\n    onZeroAxisIndex: null,\n    lineStyle: {\n      color: tokens.color.axisLine,\n      width: 1,\n      type: 'solid'\n    },\n    // The arrow at both ends the the axis.\n    symbol: ['none', 'none'],\n    symbolSize: [10, 15],\n    breakLine: true\n  },\n  axisTick: {\n    show: true,\n    // Whether axisTick is inside the grid or outside the grid.\n    inside: false,\n    // The length of axisTick.\n    length: 5,\n    lineStyle: {\n      width: 1\n    }\n  },\n  axisLabel: {\n    show: true,\n    // Whether axisLabel is inside the grid or outside the grid.\n    inside: false,\n    rotate: 0,\n    // true | false | null/undefined (auto)\n    showMinLabel: null,\n    // true | false | null/undefined (auto)\n    showMaxLabel: null,\n    margin: 8,\n    // formatter: null,\n    fontSize: 12,\n    color: tokens.color.axisLabel,\n    // In scenarios like axis labels, when labels text's progression direction matches the label\n    // layout direction (e.g., when all letters are in a single line), extra start/end margin is\n    // needed to prevent the text from appearing visually joined. In the other case, when lables\n    // are stacked (e.g., having rotation or horizontal labels on yAxis), the layout needs to be\n    // compact, so NO extra top/bottom margin should be applied.\n    textMargin: [0, 3]\n  },\n  splitLine: {\n    show: true,\n    showMinLine: true,\n    showMaxLine: true,\n    lineStyle: {\n      color: tokens.color.axisSplitLine,\n      width: 1,\n      type: 'solid'\n    }\n  },\n  splitArea: {\n    show: false,\n    areaStyle: {\n      color: [tokens.color.backgroundTint, tokens.color.backgroundTransparent]\n    }\n  },\n  breakArea: {\n    show: true,\n    itemStyle: {\n      color: tokens.color.neutral00,\n      // Break border color should be darker than the splitLine\n      // because it has opacity and should be more prominent\n      borderColor: tokens.color.border,\n      borderWidth: 1,\n      borderType: [3, 3],\n      opacity: 0.6\n    },\n    zigzagAmplitude: 4,\n    zigzagMinSpan: 4,\n    zigzagMaxSpan: 20,\n    zigzagZ: 100,\n    expandOnClick: true\n  },\n  breakLabelLayout: {\n    moveOverlap: 'auto'\n  }\n};\nvar categoryAxis = zrUtil.merge({\n  // The gap at both ends of the axis. For categoryAxis, boolean.\n  boundaryGap: true,\n  // Set false to faster category collection.\n  deduplication: null,\n  jitter: 0,\n  jitterOverlap: true,\n  jitterMargin: 2,\n  // splitArea: {\n  // show: false\n  // },\n  splitLine: {\n    show: false\n  },\n  axisTick: {\n    // If tick is align with label when boundaryGap is true\n    alignWithLabel: false,\n    interval: 'auto',\n    show: 'auto'\n  },\n  axisLabel: {\n    interval: 'auto'\n  }\n}, defaultOption);\nvar valueAxis = zrUtil.merge({\n  boundaryGap: [0, 0],\n  axisLine: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  axisTick: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  // TODO\n  // min/max: [30, datamin, 60] or [20, datamin] or [datamin, 60]\n  splitNumber: 5,\n  minorTick: {\n    // Minor tick, not available for cateogry axis.\n    show: false,\n    // Split number of minor ticks. The value should be in range of (0, 100)\n    splitNumber: 5,\n    // Length of minor tick\n    length: 3,\n    // Line style\n    lineStyle: {\n      // Default to be same with axisTick\n    }\n  },\n  minorSplitLine: {\n    show: false,\n    lineStyle: {\n      color: tokens.color.axisMinorSplitLine,\n      width: 1\n    }\n  }\n}, defaultOption);\nvar timeAxis = zrUtil.merge({\n  splitNumber: 6,\n  axisLabel: {\n    // To eliminate labels that are not nice\n    showMinLabel: false,\n    showMaxLabel: false,\n    rich: {\n      primary: {\n        fontWeight: 'bold'\n      }\n    }\n  },\n  splitLine: {\n    show: false\n  }\n}, valueAxis);\nvar logAxis = zrUtil.defaults({\n  logBase: 10\n}, valueAxis);\nexport default {\n  category: categoryAxis,\n  value: valueAxis,\n  time: timeAxis,\n  log: logAxis\n};", "map": {"version": 3, "names": ["zrUtil", "tokens", "defaultOption", "show", "z", "inverse", "name", "nameLocation", "nameRotate", "nameTruncate", "max<PERSON><PERSON><PERSON>", "ellipsis", "placeholder", "nameTextStyle", "nameGap", "silent", "triggerEvent", "tooltip", "axisPointer", "axisLine", "onZero", "onZeroAxisIndex", "lineStyle", "color", "width", "type", "symbol", "symbolSize", "breakLine", "axisTick", "inside", "length", "axisLabel", "rotate", "showMinLabel", "showMaxLabel", "margin", "fontSize", "textMargin", "splitLine", "showMinLine", "showMaxLine", "axisSplitLine", "splitArea", "areaStyle", "backgroundTint", "background<PERSON><PERSON><PERSON><PERSON>nt", "breakArea", "itemStyle", "neutral00", "borderColor", "border", "borderWidth", "borderType", "opacity", "zigzagAmplitude", "zigzagMinSpan", "zigzagMaxSpan", "zigzagZ", "expandOnClick", "breakLabelLayout", "moveOverlap", "categoryAxis", "merge", "boundaryGap", "deduplication", "jitter", "jitterOverlap", "jitter<PERSON>argin", "alignWithLabel", "interval", "valueAxis", "splitNumber", "minor<PERSON><PERSON>", "minorSplitLine", "axisMinorSplitLine", "timeAxis", "rich", "primary", "fontWeight", "logAxis", "defaults", "logBase", "category", "value", "time", "log"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/axisDefault.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport tokens from '../visual/tokens.js';\nvar defaultOption = {\n  show: true,\n  // zlevel: 0,\n  z: 0,\n  // Inverse the axis.\n  inverse: false,\n  // Axis name displayed.\n  name: '',\n  // 'start' | 'middle' | 'end'\n  nameLocation: 'end',\n  // By degree. By default auto rotate by nameLocation.\n  nameRotate: null,\n  nameTruncate: {\n    maxWidth: null,\n    ellipsis: '...',\n    placeholder: '.'\n  },\n  // Use global text style by default.\n  nameTextStyle: {\n    // textMargin: never, // The default value will be specified based on `nameLocation`.\n  },\n  // The gap between axisName and axisLine.\n  nameGap: 15,\n  // Default `false` to support tooltip.\n  silent: false,\n  // Default `false` to avoid legacy user event listener fail.\n  triggerEvent: false,\n  tooltip: {\n    show: false\n  },\n  axisPointer: {},\n  axisLine: {\n    show: true,\n    onZero: true,\n    onZeroAxisIndex: null,\n    lineStyle: {\n      color: tokens.color.axisLine,\n      width: 1,\n      type: 'solid'\n    },\n    // The arrow at both ends the the axis.\n    symbol: ['none', 'none'],\n    symbolSize: [10, 15],\n    breakLine: true\n  },\n  axisTick: {\n    show: true,\n    // Whether axisTick is inside the grid or outside the grid.\n    inside: false,\n    // The length of axisTick.\n    length: 5,\n    lineStyle: {\n      width: 1\n    }\n  },\n  axisLabel: {\n    show: true,\n    // Whether axisLabel is inside the grid or outside the grid.\n    inside: false,\n    rotate: 0,\n    // true | false | null/undefined (auto)\n    showMinLabel: null,\n    // true | false | null/undefined (auto)\n    showMaxLabel: null,\n    margin: 8,\n    // formatter: null,\n    fontSize: 12,\n    color: tokens.color.axisLabel,\n    // In scenarios like axis labels, when labels text's progression direction matches the label\n    // layout direction (e.g., when all letters are in a single line), extra start/end margin is\n    // needed to prevent the text from appearing visually joined. In the other case, when lables\n    // are stacked (e.g., having rotation or horizontal labels on yAxis), the layout needs to be\n    // compact, so NO extra top/bottom margin should be applied.\n    textMargin: [0, 3]\n  },\n  splitLine: {\n    show: true,\n    showMinLine: true,\n    showMaxLine: true,\n    lineStyle: {\n      color: tokens.color.axisSplitLine,\n      width: 1,\n      type: 'solid'\n    }\n  },\n  splitArea: {\n    show: false,\n    areaStyle: {\n      color: [tokens.color.backgroundTint, tokens.color.backgroundTransparent]\n    }\n  },\n  breakArea: {\n    show: true,\n    itemStyle: {\n      color: tokens.color.neutral00,\n      // Break border color should be darker than the splitLine\n      // because it has opacity and should be more prominent\n      borderColor: tokens.color.border,\n      borderWidth: 1,\n      borderType: [3, 3],\n      opacity: 0.6\n    },\n    zigzagAmplitude: 4,\n    zigzagMinSpan: 4,\n    zigzagMaxSpan: 20,\n    zigzagZ: 100,\n    expandOnClick: true\n  },\n  breakLabelLayout: {\n    moveOverlap: 'auto'\n  }\n};\nvar categoryAxis = zrUtil.merge({\n  // The gap at both ends of the axis. For categoryAxis, boolean.\n  boundaryGap: true,\n  // Set false to faster category collection.\n  deduplication: null,\n  jitter: 0,\n  jitterOverlap: true,\n  jitterMargin: 2,\n  // splitArea: {\n  // show: false\n  // },\n  splitLine: {\n    show: false\n  },\n  axisTick: {\n    // If tick is align with label when boundaryGap is true\n    alignWithLabel: false,\n    interval: 'auto',\n    show: 'auto'\n  },\n  axisLabel: {\n    interval: 'auto'\n  }\n}, defaultOption);\nvar valueAxis = zrUtil.merge({\n  boundaryGap: [0, 0],\n  axisLine: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  axisTick: {\n    // Not shown when other axis is categoryAxis in cartesian\n    show: 'auto'\n  },\n  // TODO\n  // min/max: [30, datamin, 60] or [20, datamin] or [datamin, 60]\n  splitNumber: 5,\n  minorTick: {\n    // Minor tick, not available for cateogry axis.\n    show: false,\n    // Split number of minor ticks. The value should be in range of (0, 100)\n    splitNumber: 5,\n    // Length of minor tick\n    length: 3,\n    // Line style\n    lineStyle: {\n      // Default to be same with axisTick\n    }\n  },\n  minorSplitLine: {\n    show: false,\n    lineStyle: {\n      color: tokens.color.axisMinorSplitLine,\n      width: 1\n    }\n  }\n}, defaultOption);\nvar timeAxis = zrUtil.merge({\n  splitNumber: 6,\n  axisLabel: {\n    // To eliminate labels that are not nice\n    showMinLabel: false,\n    showMaxLabel: false,\n    rich: {\n      primary: {\n        fontWeight: 'bold'\n      }\n    }\n  },\n  splitLine: {\n    show: false\n  }\n}, valueAxis);\nvar logAxis = zrUtil.defaults({\n  logBase: 10\n}, valueAxis);\nexport default {\n  category: categoryAxis,\n  value: valueAxis,\n  time: timeAxis,\n  log: logAxis\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,IAAI;EACV;EACAC,CAAC,EAAE,CAAC;EACJ;EACAC,OAAO,EAAE,KAAK;EACd;EACAC,IAAI,EAAE,EAAE;EACR;EACAC,YAAY,EAAE,KAAK;EACnB;EACAC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE;IACZC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,KAAK;IACfC,WAAW,EAAE;EACf,CAAC;EACD;EACAC,aAAa,EAAE;IACb;EAAA,CACD;EACD;EACAC,OAAO,EAAE,EAAE;EACX;EACAC,MAAM,EAAE,KAAK;EACb;EACAC,YAAY,EAAE,KAAK;EACnBC,OAAO,EAAE;IACPd,IAAI,EAAE;EACR,CAAC;EACDe,WAAW,EAAE,CAAC,CAAC;EACfC,QAAQ,EAAE;IACRhB,IAAI,EAAE,IAAI;IACViB,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,IAAI;IACrBC,SAAS,EAAE;MACTC,KAAK,EAAEtB,MAAM,CAACsB,KAAK,CAACJ,QAAQ;MAC5BK,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR,CAAC;IACD;IACAC,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IACxBC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;IACpBC,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE;IACR1B,IAAI,EAAE,IAAI;IACV;IACA2B,MAAM,EAAE,KAAK;IACb;IACAC,MAAM,EAAE,CAAC;IACTT,SAAS,EAAE;MACTE,KAAK,EAAE;IACT;EACF,CAAC;EACDQ,SAAS,EAAE;IACT7B,IAAI,EAAE,IAAI;IACV;IACA2B,MAAM,EAAE,KAAK;IACbG,MAAM,EAAE,CAAC;IACT;IACAC,YAAY,EAAE,IAAI;IAClB;IACAC,YAAY,EAAE,IAAI;IAClBC,MAAM,EAAE,CAAC;IACT;IACAC,QAAQ,EAAE,EAAE;IACZd,KAAK,EAAEtB,MAAM,CAACsB,KAAK,CAACS,SAAS;IAC7B;IACA;IACA;IACA;IACA;IACAM,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;EACnB,CAAC;EACDC,SAAS,EAAE;IACTpC,IAAI,EAAE,IAAI;IACVqC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBnB,SAAS,EAAE;MACTC,KAAK,EAAEtB,MAAM,CAACsB,KAAK,CAACmB,aAAa;MACjClB,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;IACR;EACF,CAAC;EACDkB,SAAS,EAAE;IACTxC,IAAI,EAAE,KAAK;IACXyC,SAAS,EAAE;MACTrB,KAAK,EAAE,CAACtB,MAAM,CAACsB,KAAK,CAACsB,cAAc,EAAE5C,MAAM,CAACsB,KAAK,CAACuB,qBAAqB;IACzE;EACF,CAAC;EACDC,SAAS,EAAE;IACT5C,IAAI,EAAE,IAAI;IACV6C,SAAS,EAAE;MACTzB,KAAK,EAAEtB,MAAM,CAACsB,KAAK,CAAC0B,SAAS;MAC7B;MACA;MACAC,WAAW,EAAEjD,MAAM,CAACsB,KAAK,CAAC4B,MAAM;MAChCC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,OAAO,EAAE;IACX,CAAC;IACDC,eAAe,EAAE,CAAC;IAClBC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,GAAG;IACZC,aAAa,EAAE;EACjB,CAAC;EACDC,gBAAgB,EAAE;IAChBC,WAAW,EAAE;EACf;AACF,CAAC;AACD,IAAIC,YAAY,GAAG9D,MAAM,CAAC+D,KAAK,CAAC;EAC9B;EACAC,WAAW,EAAE,IAAI;EACjB;EACAC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,CAAC;EACTC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,CAAC;EACf;EACA;EACA;EACA7B,SAAS,EAAE;IACTpC,IAAI,EAAE;EACR,CAAC;EACD0B,QAAQ,EAAE;IACR;IACAwC,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE,MAAM;IAChBnE,IAAI,EAAE;EACR,CAAC;EACD6B,SAAS,EAAE;IACTsC,QAAQ,EAAE;EACZ;AACF,CAAC,EAAEpE,aAAa,CAAC;AACjB,IAAIqE,SAAS,GAAGvE,MAAM,CAAC+D,KAAK,CAAC;EAC3BC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACnB7C,QAAQ,EAAE;IACR;IACAhB,IAAI,EAAE;EACR,CAAC;EACD0B,QAAQ,EAAE;IACR;IACA1B,IAAI,EAAE;EACR,CAAC;EACD;EACA;EACAqE,WAAW,EAAE,CAAC;EACdC,SAAS,EAAE;IACT;IACAtE,IAAI,EAAE,KAAK;IACX;IACAqE,WAAW,EAAE,CAAC;IACd;IACAzC,MAAM,EAAE,CAAC;IACT;IACAT,SAAS,EAAE;MACT;IAAA;EAEJ,CAAC;EACDoD,cAAc,EAAE;IACdvE,IAAI,EAAE,KAAK;IACXmB,SAAS,EAAE;MACTC,KAAK,EAAEtB,MAAM,CAACsB,KAAK,CAACoD,kBAAkB;MACtCnD,KAAK,EAAE;IACT;EACF;AACF,CAAC,EAAEtB,aAAa,CAAC;AACjB,IAAI0E,QAAQ,GAAG5E,MAAM,CAAC+D,KAAK,CAAC;EAC1BS,WAAW,EAAE,CAAC;EACdxC,SAAS,EAAE;IACT;IACAE,YAAY,EAAE,KAAK;IACnBC,YAAY,EAAE,KAAK;IACnB0C,IAAI,EAAE;MACJC,OAAO,EAAE;QACPC,UAAU,EAAE;MACd;IACF;EACF,CAAC;EACDxC,SAAS,EAAE;IACTpC,IAAI,EAAE;EACR;AACF,CAAC,EAAEoE,SAAS,CAAC;AACb,IAAIS,OAAO,GAAGhF,MAAM,CAACiF,QAAQ,CAAC;EAC5BC,OAAO,EAAE;AACX,CAAC,EAAEX,SAAS,CAAC;AACb,eAAe;EACbY,QAAQ,EAAErB,YAAY;EACtBsB,KAAK,EAAEb,SAAS;EAChBc,IAAI,EAAET,QAAQ;EACdU,GAAG,EAAEN;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}