{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as clazzUtil from '../util/clazz.js';\nimport { ScaleCalculator } from './helper.js';\nimport { bind } from 'zrender/lib/core/util.js';\nimport { getScaleBreakHelper } from './break.js';\nvar Scale = /** @class */function () {\n  function Scale(setting) {\n    this._calculator = new ScaleCalculator();\n    this._setting = setting || {};\n    this._extent = [Infinity, -Infinity];\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (scaleBreakHelper) {\n      this._brkCtx = scaleBreakHelper.createScaleBreakContext();\n      this._brkCtx.update(this._extent);\n    }\n  }\n  Scale.prototype.getSetting = function (name) {\n    return this._setting[name];\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerUnionExtent = function (other) {\n    var extent = this._extent;\n    // Considered that number could be NaN and should not write into the extent.\n    this._innerSetExtent(other[0] < extent[0] ? other[0] : extent[0], other[1] > extent[1] ? other[1] : extent[1]);\n  };\n  /**\n   * Set extent from data\n   */\n  Scale.prototype.unionExtentFromData = function (data, dim) {\n    this._innerUnionExtent(data.getApproximateExtent(dim));\n  };\n  /**\n   * Get a new slice of extent.\n   * Extent is always in increase order.\n   */\n  Scale.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  Scale.prototype.setExtent = function (start, end) {\n    this._innerSetExtent(start, end);\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerSetExtent = function (start, end) {\n    var thisExtent = this._extent;\n    if (!isNaN(start)) {\n      thisExtent[0] = start;\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = end;\n    }\n    this._brkCtx && this._brkCtx.update(thisExtent);\n  };\n  /**\n   * Prerequisite: Scale#parse is ready.\n   */\n  Scale.prototype.setBreaksFromOption = function (breakOptionList) {\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (scaleBreakHelper) {\n      this._innerSetBreak(scaleBreakHelper.parseAxisBreakOption(breakOptionList, bind(this.parse, this)));\n    }\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerSetBreak = function (parsed) {\n    if (this._brkCtx) {\n      this._brkCtx.setBreaks(parsed);\n      this._calculator.updateMethods(this._brkCtx);\n      this._brkCtx.update(this._extent);\n    }\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerGetBreaks = function () {\n    return this._brkCtx ? this._brkCtx.breaks : [];\n  };\n  /**\n   * Do not expose the internal `_breaks` unless necessary.\n   */\n  Scale.prototype.hasBreaks = function () {\n    return this._brkCtx ? this._brkCtx.hasBreaks() : false;\n  };\n  Scale.prototype._getExtentSpanWithBreaks = function () {\n    return this._brkCtx && this._brkCtx.hasBreaks() ? this._brkCtx.getExtentSpan() : this._extent[1] - this._extent[0];\n  };\n  /**\n   * If value is in extent range\n   */\n  Scale.prototype.isInExtentRange = function (value) {\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  /**\n   * When axis extent depends on data and no data exists,\n   * axis ticks should not be drawn, which is named 'blank'.\n   */\n  Scale.prototype.isBlank = function () {\n    return this._isBlank;\n  };\n  /**\n   * When axis extent depends on data and no data exists,\n   * axis ticks should not be drawn, which is named 'blank'.\n   */\n  Scale.prototype.setBlank = function (isBlank) {\n    this._isBlank = isBlank;\n  };\n  return Scale;\n}();\nclazzUtil.enableClassManagement(Scale);\nexport default Scale;", "map": {"version": 3, "names": ["clazzUtil", "ScaleCalculator", "bind", "getScaleBreakHelper", "Scale", "setting", "_calculator", "_setting", "_extent", "Infinity", "scaleBreakHelper", "_brkCtx", "createScaleBreakContext", "update", "prototype", "getSetting", "name", "_innerUnionExtent", "other", "extent", "_innerSetExtent", "unionExtentFromData", "data", "dim", "getApproximateExtent", "getExtent", "slice", "setExtent", "start", "end", "thisExtent", "isNaN", "setBreaksFromOption", "breakOptionList", "_innerSetBreak", "parseAxisBreakOption", "parse", "parsed", "setBreaks", "updateMethods", "_innerGetBreaks", "breaks", "hasBreaks", "_getExtentSpanWithBreaks", "getExtentSpan", "isInExtentRange", "value", "isBlank", "_isBlank", "setBlank", "enableClassManagement"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/scale/Scale.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as clazzUtil from '../util/clazz.js';\nimport { ScaleCalculator } from './helper.js';\nimport { bind } from 'zrender/lib/core/util.js';\nimport { getScaleBreakHelper } from './break.js';\nvar Scale = /** @class */function () {\n  function Scale(setting) {\n    this._calculator = new ScaleCalculator();\n    this._setting = setting || {};\n    this._extent = [Infinity, -Infinity];\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (scaleBreakHelper) {\n      this._brkCtx = scaleBreakHelper.createScaleBreakContext();\n      this._brkCtx.update(this._extent);\n    }\n  }\n  Scale.prototype.getSetting = function (name) {\n    return this._setting[name];\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerUnionExtent = function (other) {\n    var extent = this._extent;\n    // Considered that number could be NaN and should not write into the extent.\n    this._innerSetExtent(other[0] < extent[0] ? other[0] : extent[0], other[1] > extent[1] ? other[1] : extent[1]);\n  };\n  /**\n   * Set extent from data\n   */\n  Scale.prototype.unionExtentFromData = function (data, dim) {\n    this._innerUnionExtent(data.getApproximateExtent(dim));\n  };\n  /**\n   * Get a new slice of extent.\n   * Extent is always in increase order.\n   */\n  Scale.prototype.getExtent = function () {\n    return this._extent.slice();\n  };\n  Scale.prototype.setExtent = function (start, end) {\n    this._innerSetExtent(start, end);\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerSetExtent = function (start, end) {\n    var thisExtent = this._extent;\n    if (!isNaN(start)) {\n      thisExtent[0] = start;\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = end;\n    }\n    this._brkCtx && this._brkCtx.update(thisExtent);\n  };\n  /**\n   * Prerequisite: Scale#parse is ready.\n   */\n  Scale.prototype.setBreaksFromOption = function (breakOptionList) {\n    var scaleBreakHelper = getScaleBreakHelper();\n    if (scaleBreakHelper) {\n      this._innerSetBreak(scaleBreakHelper.parseAxisBreakOption(breakOptionList, bind(this.parse, this)));\n    }\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerSetBreak = function (parsed) {\n    if (this._brkCtx) {\n      this._brkCtx.setBreaks(parsed);\n      this._calculator.updateMethods(this._brkCtx);\n      this._brkCtx.update(this._extent);\n    }\n  };\n  /**\n   * [CAVEAT]: It should not be overridden!\n   */\n  Scale.prototype._innerGetBreaks = function () {\n    return this._brkCtx ? this._brkCtx.breaks : [];\n  };\n  /**\n   * Do not expose the internal `_breaks` unless necessary.\n   */\n  Scale.prototype.hasBreaks = function () {\n    return this._brkCtx ? this._brkCtx.hasBreaks() : false;\n  };\n  Scale.prototype._getExtentSpanWithBreaks = function () {\n    return this._brkCtx && this._brkCtx.hasBreaks() ? this._brkCtx.getExtentSpan() : this._extent[1] - this._extent[0];\n  };\n  /**\n   * If value is in extent range\n   */\n  Scale.prototype.isInExtentRange = function (value) {\n    return this._extent[0] <= value && this._extent[1] >= value;\n  };\n  /**\n   * When axis extent depends on data and no data exists,\n   * axis ticks should not be drawn, which is named 'blank'.\n   */\n  Scale.prototype.isBlank = function () {\n    return this._isBlank;\n  };\n  /**\n   * When axis extent depends on data and no data exists,\n   * axis ticks should not be drawn, which is named 'blank'.\n   */\n  Scale.prototype.setBlank = function (isBlank) {\n    this._isBlank = isBlank;\n  };\n  return Scale;\n}();\nclazzUtil.enableClassManagement(Scale);\nexport default Scale;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,SAAS,MAAM,kBAAkB;AAC7C,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,mBAAmB,QAAQ,YAAY;AAChD,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,OAAO,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,IAAIL,eAAe,CAAC,CAAC;IACxC,IAAI,CAACM,QAAQ,GAAGF,OAAO,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACG,OAAO,GAAG,CAACC,QAAQ,EAAE,CAACA,QAAQ,CAAC;IACpC,IAAIC,gBAAgB,GAAGP,mBAAmB,CAAC,CAAC;IAC5C,IAAIO,gBAAgB,EAAE;MACpB,IAAI,CAACC,OAAO,GAAGD,gBAAgB,CAACE,uBAAuB,CAAC,CAAC;MACzD,IAAI,CAACD,OAAO,CAACE,MAAM,CAAC,IAAI,CAACL,OAAO,CAAC;IACnC;EACF;EACAJ,KAAK,CAACU,SAAS,CAACC,UAAU,GAAG,UAAUC,IAAI,EAAE;IAC3C,OAAO,IAAI,CAACT,QAAQ,CAACS,IAAI,CAAC;EAC5B,CAAC;EACD;AACF;AACA;EACEZ,KAAK,CAACU,SAAS,CAACG,iBAAiB,GAAG,UAAUC,KAAK,EAAE;IACnD,IAAIC,MAAM,GAAG,IAAI,CAACX,OAAO;IACzB;IACA,IAAI,CAACY,eAAe,CAACF,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;EAChH,CAAC;EACD;AACF;AACA;EACEf,KAAK,CAACU,SAAS,CAACO,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACzD,IAAI,CAACN,iBAAiB,CAACK,IAAI,CAACE,oBAAoB,CAACD,GAAG,CAAC,CAAC;EACxD,CAAC;EACD;AACF;AACA;AACA;EACEnB,KAAK,CAACU,SAAS,CAACW,SAAS,GAAG,YAAY;IACtC,OAAO,IAAI,CAACjB,OAAO,CAACkB,KAAK,CAAC,CAAC;EAC7B,CAAC;EACDtB,KAAK,CAACU,SAAS,CAACa,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAChD,IAAI,CAACT,eAAe,CAACQ,KAAK,EAAEC,GAAG,CAAC;EAClC,CAAC;EACD;AACF;AACA;EACEzB,KAAK,CAACU,SAAS,CAACM,eAAe,GAAG,UAAUQ,KAAK,EAAEC,GAAG,EAAE;IACtD,IAAIC,UAAU,GAAG,IAAI,CAACtB,OAAO;IAC7B,IAAI,CAACuB,KAAK,CAACH,KAAK,CAAC,EAAE;MACjBE,UAAU,CAAC,CAAC,CAAC,GAAGF,KAAK;IACvB;IACA,IAAI,CAACG,KAAK,CAACF,GAAG,CAAC,EAAE;MACfC,UAAU,CAAC,CAAC,CAAC,GAAGD,GAAG;IACrB;IACA,IAAI,CAAClB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACE,MAAM,CAACiB,UAAU,CAAC;EACjD,CAAC;EACD;AACF;AACA;EACE1B,KAAK,CAACU,SAAS,CAACkB,mBAAmB,GAAG,UAAUC,eAAe,EAAE;IAC/D,IAAIvB,gBAAgB,GAAGP,mBAAmB,CAAC,CAAC;IAC5C,IAAIO,gBAAgB,EAAE;MACpB,IAAI,CAACwB,cAAc,CAACxB,gBAAgB,CAACyB,oBAAoB,CAACF,eAAe,EAAE/B,IAAI,CAAC,IAAI,CAACkC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IACrG;EACF,CAAC;EACD;AACF;AACA;EACEhC,KAAK,CAACU,SAAS,CAACoB,cAAc,GAAG,UAAUG,MAAM,EAAE;IACjD,IAAI,IAAI,CAAC1B,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAC2B,SAAS,CAACD,MAAM,CAAC;MAC9B,IAAI,CAAC/B,WAAW,CAACiC,aAAa,CAAC,IAAI,CAAC5B,OAAO,CAAC;MAC5C,IAAI,CAACA,OAAO,CAACE,MAAM,CAAC,IAAI,CAACL,OAAO,CAAC;IACnC;EACF,CAAC;EACD;AACF;AACA;EACEJ,KAAK,CAACU,SAAS,CAAC0B,eAAe,GAAG,YAAY;IAC5C,OAAO,IAAI,CAAC7B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC8B,MAAM,GAAG,EAAE;EAChD,CAAC;EACD;AACF;AACA;EACErC,KAAK,CAACU,SAAS,CAAC4B,SAAS,GAAG,YAAY;IACtC,OAAO,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC+B,SAAS,CAAC,CAAC,GAAG,KAAK;EACxD,CAAC;EACDtC,KAAK,CAACU,SAAS,CAAC6B,wBAAwB,GAAG,YAAY;IACrD,OAAO,IAAI,CAAChC,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+B,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC/B,OAAO,CAACiC,aAAa,CAAC,CAAC,GAAG,IAAI,CAACpC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC,CAAC;EACpH,CAAC;EACD;AACF;AACA;EACEJ,KAAK,CAACU,SAAS,CAAC+B,eAAe,GAAG,UAAUC,KAAK,EAAE;IACjD,OAAO,IAAI,CAACtC,OAAO,CAAC,CAAC,CAAC,IAAIsC,KAAK,IAAI,IAAI,CAACtC,OAAO,CAAC,CAAC,CAAC,IAAIsC,KAAK;EAC7D,CAAC;EACD;AACF;AACA;AACA;EACE1C,KAAK,CAACU,SAAS,CAACiC,OAAO,GAAG,YAAY;IACpC,OAAO,IAAI,CAACC,QAAQ;EACtB,CAAC;EACD;AACF;AACA;AACA;EACE5C,KAAK,CAACU,SAAS,CAACmC,QAAQ,GAAG,UAAUF,OAAO,EAAE;IAC5C,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACzB,CAAC;EACD,OAAO3C,KAAK;AACd,CAAC,CAAC,CAAC;AACHJ,SAAS,CAACkD,qBAAqB,CAAC9C,KAAK,CAAC;AACtC,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}