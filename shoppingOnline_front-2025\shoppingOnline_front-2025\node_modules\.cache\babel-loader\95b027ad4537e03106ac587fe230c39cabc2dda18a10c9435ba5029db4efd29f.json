{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Single coordinates system.\n */\nimport SingleAxis from './SingleAxis.js';\nimport * as axisHelper from '../axisHelper.js';\nimport { createBoxLayoutReference, getLayoutRect } from '../../util/layout.js';\nimport { each } from 'zrender/lib/core/util.js';\nexport var singleDimensions = ['single'];\n/**\n * Create a single coordinates system.\n */\nvar Single = /** @class */function () {\n  function Single(axisModel, ecModel, api) {\n    this.type = 'single';\n    this.dimension = 'single';\n    /**\n     * Add it just for draw tooltip.\n     */\n    this.dimensions = singleDimensions;\n    this.axisPointerEnabled = true;\n    this.model = axisModel;\n    this._init(axisModel, ecModel, api);\n  }\n  /**\n   * Initialize single coordinate system.\n   */\n  Single.prototype._init = function (axisModel, ecModel, api) {\n    var dim = this.dimension;\n    var axis = new SingleAxis(dim, axisHelper.createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisModel.get('position'));\n    var isCategory = axis.type === 'category';\n    axis.onBand = isCategory && axisModel.get('boundaryGap');\n    axis.inverse = axisModel.get('inverse');\n    axis.orient = axisModel.get('orient');\n    axisModel.axis = axis;\n    axis.model = axisModel;\n    axis.coordinateSystem = this;\n    this._axis = axis;\n  };\n  /**\n   * Update axis scale after data processed\n   */\n  Single.prototype.update = function (ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.coordinateSystem === this) {\n        var data_1 = seriesModel.getData();\n        each(data_1.mapDimensionsAll(this.dimension), function (dim) {\n          this._axis.scale.unionExtentFromData(data_1, dim);\n        }, this);\n        axisHelper.niceScaleExtent(this._axis.scale, this._axis.model);\n      }\n    }, this);\n  };\n  /**\n   * Resize the single coordinate system.\n   */\n  Single.prototype.resize = function (axisModel, api) {\n    var refContainer = createBoxLayoutReference(axisModel, api).refContainer;\n    this._rect = getLayoutRect(axisModel.getBoxLayoutParams(), refContainer);\n    this._adjustAxis();\n  };\n  Single.prototype.getRect = function () {\n    return this._rect;\n  };\n  Single.prototype._adjustAxis = function () {\n    var rect = this._rect;\n    var axis = this._axis;\n    var isHorizontal = axis.isHorizontal();\n    var extent = isHorizontal ? [0, rect.width] : [0, rect.height];\n    var idx = axis.inverse ? 1 : 0;\n    axis.setExtent(extent[idx], extent[1 - idx]);\n    this._updateAxisTransform(axis, isHorizontal ? rect.x : rect.y);\n  };\n  Single.prototype._updateAxisTransform = function (axis, coordBase) {\n    var axisExtent = axis.getExtent();\n    var extentSum = axisExtent[0] + axisExtent[1];\n    var isHorizontal = axis.isHorizontal();\n    axis.toGlobalCoord = isHorizontal ? function (coord) {\n      return coord + coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n    axis.toLocalCoord = isHorizontal ? function (coord) {\n      return coord - coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n  };\n  /**\n   * Get axis.\n   */\n  Single.prototype.getAxis = function () {\n    return this._axis;\n  };\n  /**\n   * Get axis, add it just for draw tooltip.\n   */\n  Single.prototype.getBaseAxis = function () {\n    return this._axis;\n  };\n  Single.prototype.getAxes = function () {\n    return [this._axis];\n  };\n  Single.prototype.getTooltipAxes = function () {\n    return {\n      baseAxes: [this.getAxis()],\n      // Empty otherAxes\n      otherAxes: []\n    };\n  };\n  /**\n   * If contain point.\n   */\n  Single.prototype.containPoint = function (point) {\n    var rect = this.getRect();\n    var axis = this.getAxis();\n    var orient = axis.orient;\n    if (orient === 'horizontal') {\n      return axis.contain(axis.toLocalCoord(point[0])) && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n    } else {\n      return axis.contain(axis.toLocalCoord(point[1])) && point[0] >= rect.y && point[0] <= rect.y + rect.height;\n    }\n  };\n  Single.prototype.pointToData = function (point, reserved, out) {\n    out = out || [];\n    var axis = this.getAxis();\n    out[0] = axis.coordToData(axis.toLocalCoord(point[axis.orient === 'horizontal' ? 0 : 1]));\n    return out;\n  };\n  /**\n   * Convert the series data to concrete point.\n   * Can be [val] | val\n   */\n  Single.prototype.dataToPoint = function (val, reserved, out) {\n    var axis = this.getAxis();\n    var rect = this.getRect();\n    out = out || [];\n    var idx = axis.orient === 'horizontal' ? 0 : 1;\n    if (val instanceof Array) {\n      val = val[0];\n    }\n    out[idx] = axis.toGlobalCoord(axis.dataToCoord(+val));\n    out[1 - idx] = idx === 0 ? rect.y + rect.height / 2 : rect.x + rect.width / 2;\n    return out;\n  };\n  Single.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.dataToPoint(value) : null;\n  };\n  Single.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.pointToData(pixel) : null;\n  };\n  return Single;\n}();\nfunction getCoordSys(finder) {\n  var seriesModel = finder.seriesModel;\n  var singleModel = finder.singleAxisModel;\n  return singleModel && singleModel.coordinateSystem || seriesModel && seriesModel.coordinateSystem;\n}\nexport default Single;", "map": {"version": 3, "names": ["SingleAxis", "axisHelper", "createBoxLayoutReference", "getLayoutRect", "each", "singleDimensions", "Single", "axisModel", "ecModel", "api", "type", "dimension", "dimensions", "axisPointerEnabled", "model", "_init", "prototype", "dim", "axis", "createScaleByModel", "get", "isCategory", "onBand", "inverse", "orient", "coordinateSystem", "_axis", "update", "eachSeries", "seriesModel", "data_1", "getData", "mapDimensionsAll", "scale", "unionExtentFromData", "niceScaleExtent", "resize", "ref<PERSON><PERSON><PERSON>", "_rect", "getBoxLayoutParams", "_adjustAxis", "getRect", "rect", "isHorizontal", "extent", "width", "height", "idx", "setExtent", "_updateAxisTransform", "x", "y", "coordBase", "axisExtent", "getExtent", "extentSum", "toGlobalCoord", "coord", "toLocalCoord", "getAxis", "getBaseAxis", "getAxes", "getTooltipAxes", "baseAxes", "otherAxes", "containPoint", "point", "contain", "pointToData", "reserved", "out", "coordToData", "dataToPoint", "val", "Array", "dataToCoord", "convertToPixel", "finder", "value", "coordSys", "getCoordSys", "convertFromPixel", "pixel", "singleModel", "singleAxisModel"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/single/Single.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n/**\n * Single coordinates system.\n */\nimport SingleAxis from './SingleAxis.js';\nimport * as axisHelper from '../axisHelper.js';\nimport { createBoxLayoutReference, getLayoutRect } from '../../util/layout.js';\nimport { each } from 'zrender/lib/core/util.js';\nexport var singleDimensions = ['single'];\n/**\n * Create a single coordinates system.\n */\nvar Single = /** @class */function () {\n  function Single(axisModel, ecModel, api) {\n    this.type = 'single';\n    this.dimension = 'single';\n    /**\n     * Add it just for draw tooltip.\n     */\n    this.dimensions = singleDimensions;\n    this.axisPointerEnabled = true;\n    this.model = axisModel;\n    this._init(axisModel, ecModel, api);\n  }\n  /**\n   * Initialize single coordinate system.\n   */\n  Single.prototype._init = function (axisModel, ecModel, api) {\n    var dim = this.dimension;\n    var axis = new SingleAxis(dim, axisHelper.createScaleByModel(axisModel), [0, 0], axisModel.get('type'), axisModel.get('position'));\n    var isCategory = axis.type === 'category';\n    axis.onBand = isCategory && axisModel.get('boundaryGap');\n    axis.inverse = axisModel.get('inverse');\n    axis.orient = axisModel.get('orient');\n    axisModel.axis = axis;\n    axis.model = axisModel;\n    axis.coordinateSystem = this;\n    this._axis = axis;\n  };\n  /**\n   * Update axis scale after data processed\n   */\n  Single.prototype.update = function (ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      if (seriesModel.coordinateSystem === this) {\n        var data_1 = seriesModel.getData();\n        each(data_1.mapDimensionsAll(this.dimension), function (dim) {\n          this._axis.scale.unionExtentFromData(data_1, dim);\n        }, this);\n        axisHelper.niceScaleExtent(this._axis.scale, this._axis.model);\n      }\n    }, this);\n  };\n  /**\n   * Resize the single coordinate system.\n   */\n  Single.prototype.resize = function (axisModel, api) {\n    var refContainer = createBoxLayoutReference(axisModel, api).refContainer;\n    this._rect = getLayoutRect(axisModel.getBoxLayoutParams(), refContainer);\n    this._adjustAxis();\n  };\n  Single.prototype.getRect = function () {\n    return this._rect;\n  };\n  Single.prototype._adjustAxis = function () {\n    var rect = this._rect;\n    var axis = this._axis;\n    var isHorizontal = axis.isHorizontal();\n    var extent = isHorizontal ? [0, rect.width] : [0, rect.height];\n    var idx = axis.inverse ? 1 : 0;\n    axis.setExtent(extent[idx], extent[1 - idx]);\n    this._updateAxisTransform(axis, isHorizontal ? rect.x : rect.y);\n  };\n  Single.prototype._updateAxisTransform = function (axis, coordBase) {\n    var axisExtent = axis.getExtent();\n    var extentSum = axisExtent[0] + axisExtent[1];\n    var isHorizontal = axis.isHorizontal();\n    axis.toGlobalCoord = isHorizontal ? function (coord) {\n      return coord + coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n    axis.toLocalCoord = isHorizontal ? function (coord) {\n      return coord - coordBase;\n    } : function (coord) {\n      return extentSum - coord + coordBase;\n    };\n  };\n  /**\n   * Get axis.\n   */\n  Single.prototype.getAxis = function () {\n    return this._axis;\n  };\n  /**\n   * Get axis, add it just for draw tooltip.\n   */\n  Single.prototype.getBaseAxis = function () {\n    return this._axis;\n  };\n  Single.prototype.getAxes = function () {\n    return [this._axis];\n  };\n  Single.prototype.getTooltipAxes = function () {\n    return {\n      baseAxes: [this.getAxis()],\n      // Empty otherAxes\n      otherAxes: []\n    };\n  };\n  /**\n   * If contain point.\n   */\n  Single.prototype.containPoint = function (point) {\n    var rect = this.getRect();\n    var axis = this.getAxis();\n    var orient = axis.orient;\n    if (orient === 'horizontal') {\n      return axis.contain(axis.toLocalCoord(point[0])) && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n    } else {\n      return axis.contain(axis.toLocalCoord(point[1])) && point[0] >= rect.y && point[0] <= rect.y + rect.height;\n    }\n  };\n  Single.prototype.pointToData = function (point, reserved, out) {\n    out = out || [];\n    var axis = this.getAxis();\n    out[0] = axis.coordToData(axis.toLocalCoord(point[axis.orient === 'horizontal' ? 0 : 1]));\n    return out;\n  };\n  /**\n   * Convert the series data to concrete point.\n   * Can be [val] | val\n   */\n  Single.prototype.dataToPoint = function (val, reserved, out) {\n    var axis = this.getAxis();\n    var rect = this.getRect();\n    out = out || [];\n    var idx = axis.orient === 'horizontal' ? 0 : 1;\n    if (val instanceof Array) {\n      val = val[0];\n    }\n    out[idx] = axis.toGlobalCoord(axis.dataToCoord(+val));\n    out[1 - idx] = idx === 0 ? rect.y + rect.height / 2 : rect.x + rect.width / 2;\n    return out;\n  };\n  Single.prototype.convertToPixel = function (ecModel, finder, value) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.dataToPoint(value) : null;\n  };\n  Single.prototype.convertFromPixel = function (ecModel, finder, pixel) {\n    var coordSys = getCoordSys(finder);\n    return coordSys === this ? this.pointToData(pixel) : null;\n  };\n  return Single;\n}();\nfunction getCoordSys(finder) {\n  var seriesModel = finder.seriesModel;\n  var singleModel = finder.singleAxisModel;\n  return singleModel && singleModel.coordinateSystem || seriesModel && seriesModel.coordinateSystem;\n}\nexport default Single;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAC9C,SAASC,wBAAwB,EAAEC,aAAa,QAAQ,sBAAsB;AAC9E,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAO,IAAIC,gBAAgB,GAAG,CAAC,QAAQ,CAAC;AACxC;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACvC,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,SAAS,GAAG,QAAQ;IACzB;AACJ;AACA;IACI,IAAI,CAACC,UAAU,GAAGP,gBAAgB;IAClC,IAAI,CAACQ,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,KAAK,CAACR,SAAS,EAAEC,OAAO,EAAEC,GAAG,CAAC;EACrC;EACA;AACF;AACA;EACEH,MAAM,CAACU,SAAS,CAACD,KAAK,GAAG,UAAUR,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC1D,IAAIQ,GAAG,GAAG,IAAI,CAACN,SAAS;IACxB,IAAIO,IAAI,GAAG,IAAIlB,UAAU,CAACiB,GAAG,EAAEhB,UAAU,CAACkB,kBAAkB,CAACZ,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,SAAS,CAACa,GAAG,CAAC,MAAM,CAAC,EAAEb,SAAS,CAACa,GAAG,CAAC,UAAU,CAAC,CAAC;IAClI,IAAIC,UAAU,GAAGH,IAAI,CAACR,IAAI,KAAK,UAAU;IACzCQ,IAAI,CAACI,MAAM,GAAGD,UAAU,IAAId,SAAS,CAACa,GAAG,CAAC,aAAa,CAAC;IACxDF,IAAI,CAACK,OAAO,GAAGhB,SAAS,CAACa,GAAG,CAAC,SAAS,CAAC;IACvCF,IAAI,CAACM,MAAM,GAAGjB,SAAS,CAACa,GAAG,CAAC,QAAQ,CAAC;IACrCb,SAAS,CAACW,IAAI,GAAGA,IAAI;IACrBA,IAAI,CAACJ,KAAK,GAAGP,SAAS;IACtBW,IAAI,CAACO,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,KAAK,GAAGR,IAAI;EACnB,CAAC;EACD;AACF;AACA;EACEZ,MAAM,CAACU,SAAS,CAACW,MAAM,GAAG,UAAUnB,OAAO,EAAEC,GAAG,EAAE;IAChDD,OAAO,CAACoB,UAAU,CAAC,UAAUC,WAAW,EAAE;MACxC,IAAIA,WAAW,CAACJ,gBAAgB,KAAK,IAAI,EAAE;QACzC,IAAIK,MAAM,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;QAClC3B,IAAI,CAAC0B,MAAM,CAACE,gBAAgB,CAAC,IAAI,CAACrB,SAAS,CAAC,EAAE,UAAUM,GAAG,EAAE;UAC3D,IAAI,CAACS,KAAK,CAACO,KAAK,CAACC,mBAAmB,CAACJ,MAAM,EAAEb,GAAG,CAAC;QACnD,CAAC,EAAE,IAAI,CAAC;QACRhB,UAAU,CAACkC,eAAe,CAAC,IAAI,CAACT,KAAK,CAACO,KAAK,EAAE,IAAI,CAACP,KAAK,CAACZ,KAAK,CAAC;MAChE;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;EACER,MAAM,CAACU,SAAS,CAACoB,MAAM,GAAG,UAAU7B,SAAS,EAAEE,GAAG,EAAE;IAClD,IAAI4B,YAAY,GAAGnC,wBAAwB,CAACK,SAAS,EAAEE,GAAG,CAAC,CAAC4B,YAAY;IACxE,IAAI,CAACC,KAAK,GAAGnC,aAAa,CAACI,SAAS,CAACgC,kBAAkB,CAAC,CAAC,EAAEF,YAAY,CAAC;IACxE,IAAI,CAACG,WAAW,CAAC,CAAC;EACpB,CAAC;EACDlC,MAAM,CAACU,SAAS,CAACyB,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACH,KAAK;EACnB,CAAC;EACDhC,MAAM,CAACU,SAAS,CAACwB,WAAW,GAAG,YAAY;IACzC,IAAIE,IAAI,GAAG,IAAI,CAACJ,KAAK;IACrB,IAAIpB,IAAI,GAAG,IAAI,CAACQ,KAAK;IACrB,IAAIiB,YAAY,GAAGzB,IAAI,CAACyB,YAAY,CAAC,CAAC;IACtC,IAAIC,MAAM,GAAGD,YAAY,GAAG,CAAC,CAAC,EAAED,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,MAAM,CAAC;IAC9D,IAAIC,GAAG,GAAG7B,IAAI,CAACK,OAAO,GAAG,CAAC,GAAG,CAAC;IAC9BL,IAAI,CAAC8B,SAAS,CAACJ,MAAM,CAACG,GAAG,CAAC,EAAEH,MAAM,CAAC,CAAC,GAAGG,GAAG,CAAC,CAAC;IAC5C,IAAI,CAACE,oBAAoB,CAAC/B,IAAI,EAAEyB,YAAY,GAAGD,IAAI,CAACQ,CAAC,GAAGR,IAAI,CAACS,CAAC,CAAC;EACjE,CAAC;EACD7C,MAAM,CAACU,SAAS,CAACiC,oBAAoB,GAAG,UAAU/B,IAAI,EAAEkC,SAAS,EAAE;IACjE,IAAIC,UAAU,GAAGnC,IAAI,CAACoC,SAAS,CAAC,CAAC;IACjC,IAAIC,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAIV,YAAY,GAAGzB,IAAI,CAACyB,YAAY,CAAC,CAAC;IACtCzB,IAAI,CAACsC,aAAa,GAAGb,YAAY,GAAG,UAAUc,KAAK,EAAE;MACnD,OAAOA,KAAK,GAAGL,SAAS;IAC1B,CAAC,GAAG,UAAUK,KAAK,EAAE;MACnB,OAAOF,SAAS,GAAGE,KAAK,GAAGL,SAAS;IACtC,CAAC;IACDlC,IAAI,CAACwC,YAAY,GAAGf,YAAY,GAAG,UAAUc,KAAK,EAAE;MAClD,OAAOA,KAAK,GAAGL,SAAS;IAC1B,CAAC,GAAG,UAAUK,KAAK,EAAE;MACnB,OAAOF,SAAS,GAAGE,KAAK,GAAGL,SAAS;IACtC,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE9C,MAAM,CAACU,SAAS,CAAC2C,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACjC,KAAK;EACnB,CAAC;EACD;AACF;AACA;EACEpB,MAAM,CAACU,SAAS,CAAC4C,WAAW,GAAG,YAAY;IACzC,OAAO,IAAI,CAAClC,KAAK;EACnB,CAAC;EACDpB,MAAM,CAACU,SAAS,CAAC6C,OAAO,GAAG,YAAY;IACrC,OAAO,CAAC,IAAI,CAACnC,KAAK,CAAC;EACrB,CAAC;EACDpB,MAAM,CAACU,SAAS,CAAC8C,cAAc,GAAG,YAAY;IAC5C,OAAO;MACLC,QAAQ,EAAE,CAAC,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC;MAC1B;MACAK,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EACD;AACF;AACA;EACE1D,MAAM,CAACU,SAAS,CAACiD,YAAY,GAAG,UAAUC,KAAK,EAAE;IAC/C,IAAIxB,IAAI,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC;IACzB,IAAIvB,IAAI,GAAG,IAAI,CAACyC,OAAO,CAAC,CAAC;IACzB,IAAInC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACxB,IAAIA,MAAM,KAAK,YAAY,EAAE;MAC3B,OAAON,IAAI,CAACiD,OAAO,CAACjD,IAAI,CAACwC,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIxB,IAAI,CAACS,CAAC,IAAIe,KAAK,CAAC,CAAC,CAAC,IAAIxB,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACI,MAAM;IAC5G,CAAC,MAAM;MACL,OAAO5B,IAAI,CAACiD,OAAO,CAACjD,IAAI,CAACwC,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIxB,IAAI,CAACS,CAAC,IAAIe,KAAK,CAAC,CAAC,CAAC,IAAIxB,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACI,MAAM;IAC5G;EACF,CAAC;EACDxC,MAAM,CAACU,SAAS,CAACoD,WAAW,GAAG,UAAUF,KAAK,EAAEG,QAAQ,EAAEC,GAAG,EAAE;IAC7DA,GAAG,GAAGA,GAAG,IAAI,EAAE;IACf,IAAIpD,IAAI,GAAG,IAAI,CAACyC,OAAO,CAAC,CAAC;IACzBW,GAAG,CAAC,CAAC,CAAC,GAAGpD,IAAI,CAACqD,WAAW,CAACrD,IAAI,CAACwC,YAAY,CAACQ,KAAK,CAAChD,IAAI,CAACM,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzF,OAAO8C,GAAG;EACZ,CAAC;EACD;AACF;AACA;AACA;EACEhE,MAAM,CAACU,SAAS,CAACwD,WAAW,GAAG,UAAUC,GAAG,EAAEJ,QAAQ,EAAEC,GAAG,EAAE;IAC3D,IAAIpD,IAAI,GAAG,IAAI,CAACyC,OAAO,CAAC,CAAC;IACzB,IAAIjB,IAAI,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC;IACzB6B,GAAG,GAAGA,GAAG,IAAI,EAAE;IACf,IAAIvB,GAAG,GAAG7B,IAAI,CAACM,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC9C,IAAIiD,GAAG,YAAYC,KAAK,EAAE;MACxBD,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC;IACd;IACAH,GAAG,CAACvB,GAAG,CAAC,GAAG7B,IAAI,CAACsC,aAAa,CAACtC,IAAI,CAACyD,WAAW,CAAC,CAACF,GAAG,CAAC,CAAC;IACrDH,GAAG,CAAC,CAAC,GAAGvB,GAAG,CAAC,GAAGA,GAAG,KAAK,CAAC,GAAGL,IAAI,CAACS,CAAC,GAAGT,IAAI,CAACI,MAAM,GAAG,CAAC,GAAGJ,IAAI,CAACQ,CAAC,GAAGR,IAAI,CAACG,KAAK,GAAG,CAAC;IAC7E,OAAOyB,GAAG;EACZ,CAAC;EACDhE,MAAM,CAACU,SAAS,CAAC4D,cAAc,GAAG,UAAUpE,OAAO,EAAEqE,MAAM,EAAEC,KAAK,EAAE;IAClE,IAAIC,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAG,IAAI,CAACP,WAAW,CAACM,KAAK,CAAC,GAAG,IAAI;EAC3D,CAAC;EACDxE,MAAM,CAACU,SAAS,CAACiE,gBAAgB,GAAG,UAAUzE,OAAO,EAAEqE,MAAM,EAAEK,KAAK,EAAE;IACpE,IAAIH,QAAQ,GAAGC,WAAW,CAACH,MAAM,CAAC;IAClC,OAAOE,QAAQ,KAAK,IAAI,GAAG,IAAI,CAACX,WAAW,CAACc,KAAK,CAAC,GAAG,IAAI;EAC3D,CAAC;EACD,OAAO5E,MAAM;AACf,CAAC,CAAC,CAAC;AACH,SAAS0E,WAAWA,CAACH,MAAM,EAAE;EAC3B,IAAIhD,WAAW,GAAGgD,MAAM,CAAChD,WAAW;EACpC,IAAIsD,WAAW,GAAGN,MAAM,CAACO,eAAe;EACxC,OAAOD,WAAW,IAAIA,WAAW,CAAC1D,gBAAgB,IAAII,WAAW,IAAIA,WAAW,CAACJ,gBAAgB;AACnG;AACA,eAAenB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}