{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { retrieveZInfo } from '../../util/graphic.js';\nvar IRRELEVANT_EXCLUDES = {\n  'axisPointer': 1,\n  'tooltip': 1,\n  'brush': 1\n};\n/**\n * Used on roam/brush triggering determination.\n * This is to avoid that: mouse clicking on an elements that is over geo or graph,\n * but roam is triggered unexpectedly.\n */\nexport function onIrrelevantElement(e, api, targetComponent) {\n  var eventElComponent = api.getComponentByElement(e.topTarget);\n  if (!eventElComponent || eventElComponent === targetComponent || IRRELEVANT_EXCLUDES.hasOwnProperty(eventElComponent.mainType)) {\n    return false;\n  }\n  // At present the `true` return is conservative. That is, the caller, such as a RoamController,\n  // is more likely to get a `false` return and start a roam behavior, becuase even if the\n  // `model.coordinateSystem` does not exist, the `e.topTarget` may be also a relevant element,\n  // such as axis split line/area or series elements, where roam should be available. Otherwise,\n  // if a dataZoom-served cartesian is full of series elements, the dataZoom-roaming can hardly\n  // be triggered.\n  var eventElCoordSys = eventElComponent.coordinateSystem;\n  // If eventElComponent is axisModel, it works only if it is injected with coordinateSystem.\n  if (!eventElCoordSys || eventElCoordSys.model === targetComponent) {\n    return false;\n  }\n  // e.g., if a cartesian is covered by a graph, the graph has a higher presedence in roam.\n  // A potential bad case is that RoamController does not prevent the cartesian from handling zr\n  // event, such as click and hovering, but it's fine so far.\n  // Aslo be conservative, if equals, return false.\n  var eventElCmptZInfo = retrieveZInfo(eventElComponent);\n  var targetCmptZInfo = retrieveZInfo(targetComponent);\n  if ((eventElCmptZInfo.zlevel - targetCmptZInfo.zlevel || eventElCmptZInfo.z - targetCmptZInfo.z) <= 0) {\n    return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["retrieveZInfo", "IRRELEVANT_EXCLUDES", "onIrrelevantElement", "e", "api", "targetComponent", "eventElComponent", "getComponentByElement", "topTarget", "hasOwnProperty", "mainType", "eventElCoordSys", "coordinateSystem", "model", "eventElCmptZInfo", "targetCmptZInfo", "zlevel", "z"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/component/helper/cursorHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { retrieveZInfo } from '../../util/graphic.js';\nvar IRRELEVANT_EXCLUDES = {\n  'axisPointer': 1,\n  'tooltip': 1,\n  'brush': 1\n};\n/**\n * Used on roam/brush triggering determination.\n * This is to avoid that: mouse clicking on an elements that is over geo or graph,\n * but roam is triggered unexpectedly.\n */\nexport function onIrrelevantElement(e, api, targetComponent) {\n  var eventElComponent = api.getComponentByElement(e.topTarget);\n  if (!eventElComponent || eventElComponent === targetComponent || IRRELEVANT_EXCLUDES.hasOwnProperty(eventElComponent.mainType)) {\n    return false;\n  }\n  // At present the `true` return is conservative. That is, the caller, such as a RoamController,\n  // is more likely to get a `false` return and start a roam behavior, becuase even if the\n  // `model.coordinateSystem` does not exist, the `e.topTarget` may be also a relevant element,\n  // such as axis split line/area or series elements, where roam should be available. Otherwise,\n  // if a dataZoom-served cartesian is full of series elements, the dataZoom-roaming can hardly\n  // be triggered.\n  var eventElCoordSys = eventElComponent.coordinateSystem;\n  // If eventElComponent is axisModel, it works only if it is injected with coordinateSystem.\n  if (!eventElCoordSys || eventElCoordSys.model === targetComponent) {\n    return false;\n  }\n  // e.g., if a cartesian is covered by a graph, the graph has a higher presedence in roam.\n  // A potential bad case is that RoamController does not prevent the cartesian from handling zr\n  // event, such as click and hovering, but it's fine so far.\n  // Aslo be conservative, if equals, return false.\n  var eventElCmptZInfo = retrieveZInfo(eventElComponent);\n  var targetCmptZInfo = retrieveZInfo(targetComponent);\n  if ((eventElCmptZInfo.zlevel - targetCmptZInfo.zlevel || eventElCmptZInfo.z - targetCmptZInfo.z) <= 0) {\n    return false;\n  }\n  return true;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,IAAIC,mBAAmB,GAAG;EACxB,aAAa,EAAE,CAAC;EAChB,SAAS,EAAE,CAAC;EACZ,OAAO,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,CAAC,EAAEC,GAAG,EAAEC,eAAe,EAAE;EAC3D,IAAIC,gBAAgB,GAAGF,GAAG,CAACG,qBAAqB,CAACJ,CAAC,CAACK,SAAS,CAAC;EAC7D,IAAI,CAACF,gBAAgB,IAAIA,gBAAgB,KAAKD,eAAe,IAAIJ,mBAAmB,CAACQ,cAAc,CAACH,gBAAgB,CAACI,QAAQ,CAAC,EAAE;IAC9H,OAAO,KAAK;EACd;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,eAAe,GAAGL,gBAAgB,CAACM,gBAAgB;EACvD;EACA,IAAI,CAACD,eAAe,IAAIA,eAAe,CAACE,KAAK,KAAKR,eAAe,EAAE;IACjE,OAAO,KAAK;EACd;EACA;EACA;EACA;EACA;EACA,IAAIS,gBAAgB,GAAGd,aAAa,CAACM,gBAAgB,CAAC;EACtD,IAAIS,eAAe,GAAGf,aAAa,CAACK,eAAe,CAAC;EACpD,IAAI,CAACS,gBAAgB,CAACE,MAAM,GAAGD,eAAe,CAACC,MAAM,IAAIF,gBAAgB,CAACG,CAAC,GAAGF,eAAe,CAACE,CAAC,KAAK,CAAC,EAAE;IACrG,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}