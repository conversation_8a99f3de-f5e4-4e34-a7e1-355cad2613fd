{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getPrecisionSafe, round } from '../util/number.js';\nimport IntervalScale from '../scale/Interval.js';\nimport { getScaleExtent, retrieveAxisBreaksOption } from './axisHelper.js';\nimport { warn } from '../util/log.js';\nimport { logTransform, increaseInterval, isValueNice } from '../scale/helper.js';\nexport function alignScaleTicks(scale, axisModel, alignToScale) {\n  var _a;\n  var intervalScaleProto = IntervalScale.prototype;\n  // NOTE: There is a precondition for log scale  here:\n  // In log scale we store _interval and _extent of exponent value.\n  // So if we use the method of InternalScale to set/get these data.\n  // It process the exponent value, which is linear and what we want here.\n  var alignToTicks = intervalScaleProto.getTicks.call(alignToScale);\n  var alignToNicedTicks = intervalScaleProto.getTicks.call(alignToScale, {\n    expandToNicedExtent: true\n  });\n  var alignToSplitNumber = alignToTicks.length - 1;\n  var alignToInterval = intervalScaleProto.getInterval.call(alignToScale);\n  var scaleExtent = getScaleExtent(scale, axisModel);\n  var rawExtent = scaleExtent.extent;\n  var isMinFixed = scaleExtent.fixMin;\n  var isMaxFixed = scaleExtent.fixMax;\n  if (scale.type === 'log') {\n    rawExtent = logTransform(scale.base, rawExtent, true);\n  }\n  scale.setBreaksFromOption(retrieveAxisBreaksOption(axisModel));\n  scale.setExtent(rawExtent[0], rawExtent[1]);\n  scale.calcNiceExtent({\n    splitNumber: alignToSplitNumber,\n    fixMin: isMinFixed,\n    fixMax: isMaxFixed\n  });\n  var extent = intervalScaleProto.getExtent.call(scale);\n  // Need to update the rawExtent.\n  // Because value in rawExtent may be not parsed. e.g. 'dataMin', 'dataMax'\n  if (isMinFixed) {\n    rawExtent[0] = extent[0];\n  }\n  if (isMaxFixed) {\n    rawExtent[1] = extent[1];\n  }\n  var interval = intervalScaleProto.getInterval.call(scale);\n  var min = rawExtent[0];\n  var max = rawExtent[1];\n  if (isMinFixed && isMaxFixed) {\n    // User set min, max, divide to get new interval\n    interval = (max - min) / alignToSplitNumber;\n  } else if (isMinFixed) {\n    max = rawExtent[0] + interval * alignToSplitNumber;\n    // User set min, expand extent on the other side\n    while (max < rawExtent[1] && isFinite(max) && isFinite(rawExtent[1])) {\n      interval = increaseInterval(interval);\n      max = rawExtent[0] + interval * alignToSplitNumber;\n    }\n  } else if (isMaxFixed) {\n    // User set max, expand extent on the other side\n    min = rawExtent[1] - interval * alignToSplitNumber;\n    while (min > rawExtent[0] && isFinite(min) && isFinite(rawExtent[0])) {\n      interval = increaseInterval(interval);\n      min = rawExtent[1] - interval * alignToSplitNumber;\n    }\n  } else {\n    var nicedSplitNumber = scale.getTicks().length - 1;\n    if (nicedSplitNumber > alignToSplitNumber) {\n      interval = increaseInterval(interval);\n    }\n    var range = interval * alignToSplitNumber;\n    max = Math.ceil(rawExtent[1] / interval) * interval;\n    min = round(max - range);\n    // Not change the result that crossing zero.\n    if (min < 0 && rawExtent[0] >= 0) {\n      min = 0;\n      max = round(range);\n    } else if (max > 0 && rawExtent[1] <= 0) {\n      max = 0;\n      min = -round(range);\n    }\n  }\n  // Adjust min, max based on the extent of alignTo. When min or max is set in alignTo scale\n  var t0 = (alignToTicks[0].value - alignToNicedTicks[0].value) / alignToInterval;\n  var t1 = (alignToTicks[alignToSplitNumber].value - alignToNicedTicks[alignToSplitNumber].value) / alignToInterval;\n  // NOTE: Must in setExtent -> setInterval -> setNiceExtent order.\n  intervalScaleProto.setExtent.call(scale, min + interval * t0, max + interval * t1);\n  intervalScaleProto.setInterval.call(scale, interval);\n  if (t0 || t1) {\n    intervalScaleProto.setNiceExtent.call(scale, min + interval, max - interval);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var ticks = intervalScaleProto.getTicks.call(scale);\n    if (ticks[1] && (!isValueNice(interval) || getPrecisionSafe(ticks[1].value) > getPrecisionSafe(interval))) {\n      warn(\"The ticks may be not readable when set min: \" + axisModel.get('min') + \", max: \" + axisModel.get('max') + (\" and alignTicks: true. (\" + ((_a = axisModel.axis) === null || _a === void 0 ? void 0 : _a.dim) + \"AxisIndex: \" + axisModel.componentIndex + \")\"), true);\n    }\n  }\n}", "map": {"version": 3, "names": ["getPrecisionSafe", "round", "IntervalScale", "getScaleExtent", "retrieveAxisBreaksOption", "warn", "logTransform", "increaseInterval", "isValueNice", "alignScaleTicks", "scale", "axisModel", "alignToScale", "_a", "intervalScaleProto", "prototype", "alignToTicks", "getTicks", "call", "alignToNicedTicks", "expandToNicedExtent", "alignToSplitNumber", "length", "alignToInterval", "getInterval", "scaleExtent", "rawExtent", "extent", "isMinFixed", "fixMin", "isMaxFixed", "fixMax", "type", "base", "setBreaksFromOption", "setExtent", "calcNiceExtent", "splitNumber", "getExtent", "interval", "min", "max", "isFinite", "nicedSplitNumber", "range", "Math", "ceil", "t0", "value", "t1", "setInterval", "setNiceExtent", "process", "env", "NODE_ENV", "ticks", "get", "axis", "dim", "componentIndex"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/axisAlignTicks.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { getPrecisionSafe, round } from '../util/number.js';\nimport IntervalScale from '../scale/Interval.js';\nimport { getScaleExtent, retrieveAxisBreaksOption } from './axisHelper.js';\nimport { warn } from '../util/log.js';\nimport { logTransform, increaseInterval, isValueNice } from '../scale/helper.js';\nexport function alignScaleTicks(scale, axisModel, alignToScale) {\n  var _a;\n  var intervalScaleProto = IntervalScale.prototype;\n  // NOTE: There is a precondition for log scale  here:\n  // In log scale we store _interval and _extent of exponent value.\n  // So if we use the method of InternalScale to set/get these data.\n  // It process the exponent value, which is linear and what we want here.\n  var alignToTicks = intervalScaleProto.getTicks.call(alignToScale);\n  var alignToNicedTicks = intervalScaleProto.getTicks.call(alignToScale, {\n    expandToNicedExtent: true\n  });\n  var alignToSplitNumber = alignToTicks.length - 1;\n  var alignToInterval = intervalScaleProto.getInterval.call(alignToScale);\n  var scaleExtent = getScaleExtent(scale, axisModel);\n  var rawExtent = scaleExtent.extent;\n  var isMinFixed = scaleExtent.fixMin;\n  var isMaxFixed = scaleExtent.fixMax;\n  if (scale.type === 'log') {\n    rawExtent = logTransform(scale.base, rawExtent, true);\n  }\n  scale.setBreaksFromOption(retrieveAxisBreaksOption(axisModel));\n  scale.setExtent(rawExtent[0], rawExtent[1]);\n  scale.calcNiceExtent({\n    splitNumber: alignToSplitNumber,\n    fixMin: isMinFixed,\n    fixMax: isMaxFixed\n  });\n  var extent = intervalScaleProto.getExtent.call(scale);\n  // Need to update the rawExtent.\n  // Because value in rawExtent may be not parsed. e.g. 'dataMin', 'dataMax'\n  if (isMinFixed) {\n    rawExtent[0] = extent[0];\n  }\n  if (isMaxFixed) {\n    rawExtent[1] = extent[1];\n  }\n  var interval = intervalScaleProto.getInterval.call(scale);\n  var min = rawExtent[0];\n  var max = rawExtent[1];\n  if (isMinFixed && isMaxFixed) {\n    // User set min, max, divide to get new interval\n    interval = (max - min) / alignToSplitNumber;\n  } else if (isMinFixed) {\n    max = rawExtent[0] + interval * alignToSplitNumber;\n    // User set min, expand extent on the other side\n    while (max < rawExtent[1] && isFinite(max) && isFinite(rawExtent[1])) {\n      interval = increaseInterval(interval);\n      max = rawExtent[0] + interval * alignToSplitNumber;\n    }\n  } else if (isMaxFixed) {\n    // User set max, expand extent on the other side\n    min = rawExtent[1] - interval * alignToSplitNumber;\n    while (min > rawExtent[0] && isFinite(min) && isFinite(rawExtent[0])) {\n      interval = increaseInterval(interval);\n      min = rawExtent[1] - interval * alignToSplitNumber;\n    }\n  } else {\n    var nicedSplitNumber = scale.getTicks().length - 1;\n    if (nicedSplitNumber > alignToSplitNumber) {\n      interval = increaseInterval(interval);\n    }\n    var range = interval * alignToSplitNumber;\n    max = Math.ceil(rawExtent[1] / interval) * interval;\n    min = round(max - range);\n    // Not change the result that crossing zero.\n    if (min < 0 && rawExtent[0] >= 0) {\n      min = 0;\n      max = round(range);\n    } else if (max > 0 && rawExtent[1] <= 0) {\n      max = 0;\n      min = -round(range);\n    }\n  }\n  // Adjust min, max based on the extent of alignTo. When min or max is set in alignTo scale\n  var t0 = (alignToTicks[0].value - alignToNicedTicks[0].value) / alignToInterval;\n  var t1 = (alignToTicks[alignToSplitNumber].value - alignToNicedTicks[alignToSplitNumber].value) / alignToInterval;\n  // NOTE: Must in setExtent -> setInterval -> setNiceExtent order.\n  intervalScaleProto.setExtent.call(scale, min + interval * t0, max + interval * t1);\n  intervalScaleProto.setInterval.call(scale, interval);\n  if (t0 || t1) {\n    intervalScaleProto.setNiceExtent.call(scale, min + interval, max - interval);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    var ticks = intervalScaleProto.getTicks.call(scale);\n    if (ticks[1] && (!isValueNice(interval) || getPrecisionSafe(ticks[1].value) > getPrecisionSafe(interval))) {\n      warn(\"The ticks may be not readable when set min: \" + axisModel.get('min') + \", max: \" + axisModel.get('max') + (\" and alignTicks: true. (\" + ((_a = axisModel.axis) === null || _a === void 0 ? void 0 : _a.dim) + \"AxisIndex: \" + axisModel.componentIndex + \")\"), true);\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,EAAEC,KAAK,QAAQ,mBAAmB;AAC3D,OAAOC,aAAa,MAAM,sBAAsB;AAChD,SAASC,cAAc,EAAEC,wBAAwB,QAAQ,iBAAiB;AAC1E,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,oBAAoB;AAChF,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAE;EAC9D,IAAIC,EAAE;EACN,IAAIC,kBAAkB,GAAGZ,aAAa,CAACa,SAAS;EAChD;EACA;EACA;EACA;EACA,IAAIC,YAAY,GAAGF,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACN,YAAY,CAAC;EACjE,IAAIO,iBAAiB,GAAGL,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACN,YAAY,EAAE;IACrEQ,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,IAAIC,kBAAkB,GAAGL,YAAY,CAACM,MAAM,GAAG,CAAC;EAChD,IAAIC,eAAe,GAAGT,kBAAkB,CAACU,WAAW,CAACN,IAAI,CAACN,YAAY,CAAC;EACvE,IAAIa,WAAW,GAAGtB,cAAc,CAACO,KAAK,EAAEC,SAAS,CAAC;EAClD,IAAIe,SAAS,GAAGD,WAAW,CAACE,MAAM;EAClC,IAAIC,UAAU,GAAGH,WAAW,CAACI,MAAM;EACnC,IAAIC,UAAU,GAAGL,WAAW,CAACM,MAAM;EACnC,IAAIrB,KAAK,CAACsB,IAAI,KAAK,KAAK,EAAE;IACxBN,SAAS,GAAGpB,YAAY,CAACI,KAAK,CAACuB,IAAI,EAAEP,SAAS,EAAE,IAAI,CAAC;EACvD;EACAhB,KAAK,CAACwB,mBAAmB,CAAC9B,wBAAwB,CAACO,SAAS,CAAC,CAAC;EAC9DD,KAAK,CAACyB,SAAS,CAACT,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;EAC3ChB,KAAK,CAAC0B,cAAc,CAAC;IACnBC,WAAW,EAAEhB,kBAAkB;IAC/BQ,MAAM,EAAED,UAAU;IAClBG,MAAM,EAAED;EACV,CAAC,CAAC;EACF,IAAIH,MAAM,GAAGb,kBAAkB,CAACwB,SAAS,CAACpB,IAAI,CAACR,KAAK,CAAC;EACrD;EACA;EACA,IAAIkB,UAAU,EAAE;IACdF,SAAS,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIG,UAAU,EAAE;IACdJ,SAAS,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIY,QAAQ,GAAGzB,kBAAkB,CAACU,WAAW,CAACN,IAAI,CAACR,KAAK,CAAC;EACzD,IAAI8B,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC;EACtB,IAAIe,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC;EACtB,IAAIE,UAAU,IAAIE,UAAU,EAAE;IAC5B;IACAS,QAAQ,GAAG,CAACE,GAAG,GAAGD,GAAG,IAAInB,kBAAkB;EAC7C,CAAC,MAAM,IAAIO,UAAU,EAAE;IACrBa,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IAClD;IACA,OAAOoB,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,IAAIgB,QAAQ,CAACD,GAAG,CAAC,IAAIC,QAAQ,CAAChB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACpEa,QAAQ,GAAGhC,gBAAgB,CAACgC,QAAQ,CAAC;MACrCE,GAAG,GAAGf,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IACpD;EACF,CAAC,MAAM,IAAIS,UAAU,EAAE;IACrB;IACAU,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IAClD,OAAOmB,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,IAAIgB,QAAQ,CAACF,GAAG,CAAC,IAAIE,QAAQ,CAAChB,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;MACpEa,QAAQ,GAAGhC,gBAAgB,CAACgC,QAAQ,CAAC;MACrCC,GAAG,GAAGd,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,GAAGlB,kBAAkB;IACpD;EACF,CAAC,MAAM;IACL,IAAIsB,gBAAgB,GAAGjC,KAAK,CAACO,QAAQ,CAAC,CAAC,CAACK,MAAM,GAAG,CAAC;IAClD,IAAIqB,gBAAgB,GAAGtB,kBAAkB,EAAE;MACzCkB,QAAQ,GAAGhC,gBAAgB,CAACgC,QAAQ,CAAC;IACvC;IACA,IAAIK,KAAK,GAAGL,QAAQ,GAAGlB,kBAAkB;IACzCoB,GAAG,GAAGI,IAAI,CAACC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,GAAGa,QAAQ,CAAC,GAAGA,QAAQ;IACnDC,GAAG,GAAGvC,KAAK,CAACwC,GAAG,GAAGG,KAAK,CAAC;IACxB;IACA,IAAIJ,GAAG,GAAG,CAAC,IAAId,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAChCc,GAAG,GAAG,CAAC;MACPC,GAAG,GAAGxC,KAAK,CAAC2C,KAAK,CAAC;IACpB,CAAC,MAAM,IAAIH,GAAG,GAAG,CAAC,IAAIf,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MACvCe,GAAG,GAAG,CAAC;MACPD,GAAG,GAAG,CAACvC,KAAK,CAAC2C,KAAK,CAAC;IACrB;EACF;EACA;EACA,IAAIG,EAAE,GAAG,CAAC/B,YAAY,CAAC,CAAC,CAAC,CAACgC,KAAK,GAAG7B,iBAAiB,CAAC,CAAC,CAAC,CAAC6B,KAAK,IAAIzB,eAAe;EAC/E,IAAI0B,EAAE,GAAG,CAACjC,YAAY,CAACK,kBAAkB,CAAC,CAAC2B,KAAK,GAAG7B,iBAAiB,CAACE,kBAAkB,CAAC,CAAC2B,KAAK,IAAIzB,eAAe;EACjH;EACAT,kBAAkB,CAACqB,SAAS,CAACjB,IAAI,CAACR,KAAK,EAAE8B,GAAG,GAAGD,QAAQ,GAAGQ,EAAE,EAAEN,GAAG,GAAGF,QAAQ,GAAGU,EAAE,CAAC;EAClFnC,kBAAkB,CAACoC,WAAW,CAAChC,IAAI,CAACR,KAAK,EAAE6B,QAAQ,CAAC;EACpD,IAAIQ,EAAE,IAAIE,EAAE,EAAE;IACZnC,kBAAkB,CAACqC,aAAa,CAACjC,IAAI,CAACR,KAAK,EAAE8B,GAAG,GAAGD,QAAQ,EAAEE,GAAG,GAAGF,QAAQ,CAAC;EAC9E;EACA,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,KAAK,GAAGzC,kBAAkB,CAACG,QAAQ,CAACC,IAAI,CAACR,KAAK,CAAC;IACnD,IAAI6C,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC/C,WAAW,CAAC+B,QAAQ,CAAC,IAAIvC,gBAAgB,CAACuD,KAAK,CAAC,CAAC,CAAC,CAACP,KAAK,CAAC,GAAGhD,gBAAgB,CAACuC,QAAQ,CAAC,CAAC,EAAE;MACzGlC,IAAI,CAAC,8CAA8C,GAAGM,SAAS,CAAC6C,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG7C,SAAS,CAAC6C,GAAG,CAAC,KAAK,CAAC,IAAI,0BAA0B,IAAI,CAAC3C,EAAE,GAAGF,SAAS,CAAC8C,IAAI,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C,GAAG,CAAC,GAAG,aAAa,GAAG/C,SAAS,CAACgD,cAAc,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC;IAC5Q;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}