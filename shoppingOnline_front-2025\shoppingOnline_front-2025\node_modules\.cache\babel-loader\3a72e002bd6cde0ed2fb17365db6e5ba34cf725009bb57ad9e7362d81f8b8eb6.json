{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { adjustTextX, adjustTextY, ensureFontMeasureInfo, getLineHeight, measureCharWidth, measureWidth, parsePercent } from '../../contain/text.js';\nimport BoundingRect from '../../core/BoundingRect.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n  var out = {};\n  truncateText2(out, text, containerWidth, font, ellipsis, options);\n  return out.text;\n}\nfunction truncateText2(out, text, containerWidth, font, ellipsis, options) {\n  if (!containerWidth) {\n    out.text = '';\n    out.isTruncated = false;\n    return;\n  }\n  var textLines = (text + '').split('\\n');\n  options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n  var isTruncated = false;\n  var truncateOut = {};\n  for (var i = 0, len = textLines.length; i < len; i++) {\n    truncateSingleLine(truncateOut, textLines[i], options);\n    textLines[i] = truncateOut.textLine;\n    isTruncated = isTruncated || truncateOut.isTruncated;\n  }\n  out.text = textLines.join('\\n');\n  out.isTruncated = isTruncated;\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n  options = options || {};\n  var preparedOpts = extend({}, options);\n  ellipsis = retrieve2(ellipsis, '...');\n  preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n  var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n  var fontMeasureInfo = preparedOpts.fontMeasureInfo = ensureFontMeasureInfo(font);\n  var ascCharWidth = fontMeasureInfo.asciiCharWidth;\n  preparedOpts.placeholder = retrieve2(options.placeholder, '');\n  var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n  for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n    contentWidth -= ascCharWidth;\n  }\n  var ellipsisWidth = measureWidth(fontMeasureInfo, ellipsis);\n  if (ellipsisWidth > contentWidth) {\n    ellipsis = '';\n    ellipsisWidth = 0;\n  }\n  contentWidth = containerWidth - ellipsisWidth;\n  preparedOpts.ellipsis = ellipsis;\n  preparedOpts.ellipsisWidth = ellipsisWidth;\n  preparedOpts.contentWidth = contentWidth;\n  preparedOpts.containerWidth = containerWidth;\n  return preparedOpts;\n}\nfunction truncateSingleLine(out, textLine, options) {\n  var containerWidth = options.containerWidth;\n  var contentWidth = options.contentWidth;\n  var fontMeasureInfo = options.fontMeasureInfo;\n  if (!containerWidth) {\n    out.textLine = '';\n    out.isTruncated = false;\n    return;\n  }\n  var lineWidth = measureWidth(fontMeasureInfo, textLine);\n  if (lineWidth <= containerWidth) {\n    out.textLine = textLine;\n    out.isTruncated = false;\n    return;\n  }\n  for (var j = 0;; j++) {\n    if (lineWidth <= contentWidth || j >= options.maxIterations) {\n      textLine += options.ellipsis;\n      break;\n    }\n    var subLength = j === 0 ? estimateLength(textLine, contentWidth, fontMeasureInfo) : lineWidth > 0 ? Math.floor(textLine.length * contentWidth / lineWidth) : 0;\n    textLine = textLine.substr(0, subLength);\n    lineWidth = measureWidth(fontMeasureInfo, textLine);\n  }\n  if (textLine === '') {\n    textLine = options.placeholder;\n  }\n  out.textLine = textLine;\n  out.isTruncated = true;\n}\nfunction estimateLength(text, contentWidth, fontMeasureInfo) {\n  var width = 0;\n  var i = 0;\n  for (var len = text.length; i < len && width < contentWidth; i++) {\n    width += measureCharWidth(fontMeasureInfo, text.charCodeAt(i));\n  }\n  return i;\n}\nexport function parsePlainText(rawText, style, defaultOuterWidth, defaultOuterHeight) {\n  var text = formatText(rawText);\n  var overflow = style.overflow;\n  var padding = style.padding;\n  var paddingH = padding ? padding[1] + padding[3] : 0;\n  var paddingV = padding ? padding[0] + padding[2] : 0;\n  var font = style.font;\n  var truncate = overflow === 'truncate';\n  var calculatedLineHeight = getLineHeight(font);\n  var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n  var truncateLineOverflow = style.lineOverflow === 'truncate';\n  var isTruncated = false;\n  var width = style.width;\n  if (width == null && defaultOuterWidth != null) {\n    width = defaultOuterWidth - paddingH;\n  }\n  var height = style.height;\n  if (height == null && defaultOuterHeight != null) {\n    height = defaultOuterHeight - paddingV;\n  }\n  var lines;\n  if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n    lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n  } else {\n    lines = text ? text.split('\\n') : [];\n  }\n  var contentHeight = lines.length * lineHeight;\n  if (height == null) {\n    height = contentHeight;\n  }\n  if (contentHeight > height && truncateLineOverflow) {\n    var lineCount = Math.floor(height / lineHeight);\n    isTruncated = isTruncated || lines.length > lineCount;\n    lines = lines.slice(0, lineCount);\n    contentHeight = lines.length * lineHeight;\n  }\n  if (text && truncate && width != null) {\n    var options = prepareTruncateOptions(width, font, style.ellipsis, {\n      minChar: style.truncateMinChar,\n      placeholder: style.placeholder\n    });\n    var singleOut = {};\n    for (var i = 0; i < lines.length; i++) {\n      truncateSingleLine(singleOut, lines[i], options);\n      lines[i] = singleOut.textLine;\n      isTruncated = isTruncated || singleOut.isTruncated;\n    }\n  }\n  var outerHeight = height;\n  var contentWidth = 0;\n  var fontMeasureInfo = ensureFontMeasureInfo(font);\n  for (var i = 0; i < lines.length; i++) {\n    contentWidth = Math.max(measureWidth(fontMeasureInfo, lines[i]), contentWidth);\n  }\n  if (width == null) {\n    width = contentWidth;\n  }\n  var outerWidth = width;\n  outerHeight += paddingV;\n  outerWidth += paddingH;\n  return {\n    lines: lines,\n    height: height,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    lineHeight: lineHeight,\n    calculatedLineHeight: calculatedLineHeight,\n    contentWidth: contentWidth,\n    contentHeight: contentHeight,\n    width: width,\n    isTruncated: isTruncated\n  };\n}\nvar RichTextToken = function () {\n  function RichTextToken() {}\n  return RichTextToken;\n}();\nvar RichTextLine = function () {\n  function RichTextLine(tokens) {\n    this.tokens = [];\n    if (tokens) {\n      this.tokens = tokens;\n    }\n  }\n  return RichTextLine;\n}();\nvar RichTextContentBlock = function () {\n  function RichTextContentBlock() {\n    this.width = 0;\n    this.height = 0;\n    this.contentWidth = 0;\n    this.contentHeight = 0;\n    this.outerWidth = 0;\n    this.outerHeight = 0;\n    this.lines = [];\n    this.isTruncated = false;\n  }\n  return RichTextContentBlock;\n}();\nexport { RichTextContentBlock };\nexport function parseRichText(rawText, style, defaultOuterWidth, defaultOuterHeight, topTextAlign) {\n  var contentBlock = new RichTextContentBlock();\n  var text = formatText(rawText);\n  if (!text) {\n    return contentBlock;\n  }\n  var stlPadding = style.padding;\n  var stlPaddingH = stlPadding ? stlPadding[1] + stlPadding[3] : 0;\n  var stlPaddingV = stlPadding ? stlPadding[0] + stlPadding[2] : 0;\n  var topWidth = style.width;\n  if (topWidth == null && defaultOuterWidth != null) {\n    topWidth = defaultOuterWidth - stlPaddingH;\n  }\n  var topHeight = style.height;\n  if (topHeight == null && defaultOuterHeight != null) {\n    topHeight = defaultOuterHeight - stlPaddingV;\n  }\n  var overflow = style.overflow;\n  var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null ? {\n    width: topWidth,\n    accumWidth: 0,\n    breakAll: overflow === 'breakAll'\n  } : null;\n  var lastIndex = STYLE_REG.lastIndex = 0;\n  var result;\n  while ((result = STYLE_REG.exec(text)) != null) {\n    var matchedIndex = result.index;\n    if (matchedIndex > lastIndex) {\n      pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n    }\n    pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n    lastIndex = STYLE_REG.lastIndex;\n  }\n  if (lastIndex < text.length) {\n    pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n  }\n  var pendingList = [];\n  var calculatedHeight = 0;\n  var calculatedWidth = 0;\n  var truncate = overflow === 'truncate';\n  var truncateLine = style.lineOverflow === 'truncate';\n  var tmpTruncateOut = {};\n  function finishLine(line, lineWidth, lineHeight) {\n    line.width = lineWidth;\n    line.lineHeight = lineHeight;\n    calculatedHeight += lineHeight;\n    calculatedWidth = Math.max(calculatedWidth, lineWidth);\n  }\n  outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n    var line = contentBlock.lines[i];\n    var lineHeight = 0;\n    var lineWidth = 0;\n    for (var j = 0; j < line.tokens.length; j++) {\n      var token = line.tokens[j];\n      var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n      var textPadding = token.textPadding = tokenStyle.padding;\n      var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n      var font = token.font = tokenStyle.font || style.font;\n      token.contentHeight = getLineHeight(font);\n      var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n      token.innerHeight = tokenHeight;\n      textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n      token.height = tokenHeight;\n      token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n      token.align = tokenStyle && tokenStyle.align || topTextAlign;\n      token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n      if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n        var originalLength = contentBlock.lines.length;\n        if (j > 0) {\n          line.tokens = line.tokens.slice(0, j);\n          finishLine(line, lineWidth, lineHeight);\n          contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n        } else {\n          contentBlock.lines = contentBlock.lines.slice(0, i);\n        }\n        contentBlock.isTruncated = contentBlock.isTruncated || contentBlock.lines.length < originalLength;\n        break outer;\n      }\n      var styleTokenWidth = tokenStyle.width;\n      var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n      if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n        token.percentWidth = styleTokenWidth;\n        pendingList.push(token);\n        token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n      } else {\n        if (tokenWidthNotSpecified) {\n          var textBackgroundColor = tokenStyle.backgroundColor;\n          var bgImg = textBackgroundColor && textBackgroundColor.image;\n          if (bgImg) {\n            bgImg = imageHelper.findExistImage(bgImg);\n            if (imageHelper.isImageReady(bgImg)) {\n              token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n            }\n          }\n        }\n        var remainTruncWidth = truncate && topWidth != null ? topWidth - lineWidth : null;\n        if (remainTruncWidth != null && remainTruncWidth < token.width) {\n          if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n            token.text = '';\n            token.width = token.contentWidth = 0;\n          } else {\n            truncateText2(tmpTruncateOut, token.text, remainTruncWidth - paddingH, font, style.ellipsis, {\n              minChar: style.truncateMinChar\n            });\n            token.text = tmpTruncateOut.text;\n            contentBlock.isTruncated = contentBlock.isTruncated || tmpTruncateOut.isTruncated;\n            token.width = token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n          }\n        } else {\n          token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n        }\n      }\n      token.width += paddingH;\n      lineWidth += token.width;\n      tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n    }\n    finishLine(line, lineWidth, lineHeight);\n  }\n  contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n  contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n  contentBlock.contentHeight = calculatedHeight;\n  contentBlock.contentWidth = calculatedWidth;\n  contentBlock.outerWidth += stlPaddingH;\n  contentBlock.outerHeight += stlPaddingV;\n  for (var i = 0; i < pendingList.length; i++) {\n    var token = pendingList[i];\n    var percentWidth = token.percentWidth;\n    token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n  }\n  return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n  var isEmptyStr = str === '';\n  var tokenStyle = styleName && style.rich[styleName] || {};\n  var lines = block.lines;\n  var font = tokenStyle.font || style.font;\n  var newLine = false;\n  var strLines;\n  var linesWidths;\n  if (wrapInfo) {\n    var tokenPadding = tokenStyle.padding;\n    var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n    if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n      var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n      if (lines.length > 0) {\n        if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n          strLines = str.split('\\n');\n          newLine = true;\n        }\n      }\n      wrapInfo.accumWidth = outerWidth_1;\n    } else {\n      var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n      wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n      linesWidths = res.linesWidths;\n      strLines = res.lines;\n    }\n  }\n  if (!strLines) {\n    strLines = str.split('\\n');\n  }\n  var fontMeasureInfo = ensureFontMeasureInfo(font);\n  for (var i = 0; i < strLines.length; i++) {\n    var text = strLines[i];\n    var token = new RichTextToken();\n    token.styleName = styleName;\n    token.text = text;\n    token.isLineHolder = !text && !isEmptyStr;\n    if (typeof tokenStyle.width === 'number') {\n      token.width = tokenStyle.width;\n    } else {\n      token.width = linesWidths ? linesWidths[i] : measureWidth(fontMeasureInfo, text);\n    }\n    if (!i && !newLine) {\n      var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n      var tokensLen = tokens.length;\n      tokensLen === 1 && tokens[0].isLineHolder ? tokens[0] = token : (text || !tokensLen || isEmptyStr) && tokens.push(token);\n    } else {\n      lines.push(new RichTextLine([token]));\n    }\n  }\n}\nfunction isAlphabeticLetter(ch) {\n  var code = ch.charCodeAt(0);\n  return code >= 0x20 && code <= 0x24F || code >= 0x370 && code <= 0x10FF || code >= 0x1200 && code <= 0x13FF || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n  obj[ch] = true;\n  return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n  if (isAlphabeticLetter(ch)) {\n    if (breakCharMap[ch]) {\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n  var lines = [];\n  var linesWidths = [];\n  var line = '';\n  var currentWord = '';\n  var currentWordWidth = 0;\n  var accumWidth = 0;\n  var fontMeasureInfo = ensureFontMeasureInfo(font);\n  for (var i = 0; i < text.length; i++) {\n    var ch = text.charAt(i);\n    if (ch === '\\n') {\n      if (currentWord) {\n        line += currentWord;\n        accumWidth += currentWordWidth;\n      }\n      lines.push(line);\n      linesWidths.push(accumWidth);\n      line = '';\n      currentWord = '';\n      currentWordWidth = 0;\n      accumWidth = 0;\n      continue;\n    }\n    var chWidth = measureCharWidth(fontMeasureInfo, ch.charCodeAt(0));\n    var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n    if (!lines.length ? lastAccumWidth + accumWidth + chWidth > lineWidth : accumWidth + chWidth > lineWidth) {\n      if (!accumWidth) {\n        if (inWord) {\n          lines.push(currentWord);\n          linesWidths.push(currentWordWidth);\n          currentWord = ch;\n          currentWordWidth = chWidth;\n        } else {\n          lines.push(ch);\n          linesWidths.push(chWidth);\n        }\n      } else if (line || currentWord) {\n        if (inWord) {\n          if (!line) {\n            line = currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = currentWordWidth;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth - currentWordWidth);\n          currentWord += ch;\n          currentWordWidth += chWidth;\n          line = '';\n          accumWidth = currentWordWidth;\n        } else {\n          if (currentWord) {\n            line += currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth);\n          line = ch;\n          accumWidth = chWidth;\n        }\n      }\n      continue;\n    }\n    accumWidth += chWidth;\n    if (inWord) {\n      currentWord += ch;\n      currentWordWidth += chWidth;\n    } else {\n      if (currentWord) {\n        line += currentWord;\n        currentWord = '';\n        currentWordWidth = 0;\n      }\n      line += ch;\n    }\n  }\n  if (currentWord) {\n    line += currentWord;\n  }\n  if (line) {\n    lines.push(line);\n    linesWidths.push(accumWidth);\n  }\n  if (lines.length === 1) {\n    accumWidth += lastAccumWidth;\n  }\n  return {\n    accumWidth: accumWidth,\n    lines: lines,\n    linesWidths: linesWidths\n  };\n}\nexport function calcInnerTextOverflowArea(out, overflowRect, baseX, baseY, textAlign, textVerticalAlign) {\n  out.baseX = baseX;\n  out.baseY = baseY;\n  out.outerWidth = out.outerHeight = null;\n  if (!overflowRect) {\n    return;\n  }\n  var textWidth = overflowRect.width * 2;\n  var textHeight = overflowRect.height * 2;\n  BoundingRect.set(tmpCITCTextRect, adjustTextX(baseX, textWidth, textAlign), adjustTextY(baseY, textHeight, textVerticalAlign), textWidth, textHeight);\n  BoundingRect.intersect(overflowRect, tmpCITCTextRect, null, tmpCITCIntersectRectOpt);\n  var outIntersectRect = tmpCITCIntersectRectOpt.outIntersectRect;\n  out.outerWidth = outIntersectRect.width;\n  out.outerHeight = outIntersectRect.height;\n  out.baseX = adjustTextX(outIntersectRect.x, outIntersectRect.width, textAlign, true);\n  out.baseY = adjustTextY(outIntersectRect.y, outIntersectRect.height, textVerticalAlign, true);\n}\nvar tmpCITCTextRect = new BoundingRect(0, 0, 0, 0);\nvar tmpCITCIntersectRectOpt = {\n  outIntersectRect: {},\n  clamp: true\n};\nfunction formatText(text) {\n  return text != null ? text += '' : text = '';\n}\nexport function tSpanCreateBoundingRect(style) {\n  var text = formatText(style.text);\n  var font = style.font;\n  var contentWidth = measureWidth(ensureFontMeasureInfo(font), text);\n  var contentHeight = getLineHeight(font);\n  return tSpanCreateBoundingRect2(style, contentWidth, contentHeight, null);\n}\nexport function tSpanCreateBoundingRect2(style, contentWidth, contentHeight, forceLineWidth) {\n  var rect = new BoundingRect(adjustTextX(style.x || 0, contentWidth, style.textAlign), adjustTextY(style.y || 0, contentHeight, style.textBaseline), contentWidth, contentHeight);\n  var lineWidth = forceLineWidth != null ? forceLineWidth : tSpanHasStroke(style) ? style.lineWidth : 0;\n  if (lineWidth > 0) {\n    rect.x -= lineWidth / 2;\n    rect.y -= lineWidth / 2;\n    rect.width += lineWidth;\n    rect.height += lineWidth;\n  }\n  return rect;\n}\nexport function tSpanHasStroke(style) {\n  var stroke = style.stroke;\n  return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n}", "map": {"version": 3, "names": ["imageHelper", "extend", "retrieve2", "retrieve3", "reduce", "adjustTextX", "adjustTextY", "ensureFontMeasureInfo", "getLineHeight", "measureCharWidth", "measureWidth", "parsePercent", "BoundingRect", "STYLE_REG", "truncateText", "text", "containerWidth", "font", "ellipsis", "options", "out", "truncateText2", "isTruncated", "textLines", "split", "prepareTruncateOptions", "truncateOut", "i", "len", "length", "truncateSingleLine", "textLine", "join", "preparedOpts", "maxIterations", "minChar", "fontMeasureInfo", "ascCharWidth", "asciiCharWidth", "placeholder", "contentWidth", "Math", "max", "ellip<PERSON><PERSON><PERSON><PERSON>", "lineWidth", "j", "subLength", "estimateLength", "floor", "substr", "width", "charCodeAt", "parsePlainText", "rawText", "style", "defaultOuterWidth", "defaultOuterHeight", "formatText", "overflow", "padding", "paddingH", "paddingV", "truncate", "calculatedLineHeight", "lineHeight", "truncateLineOverflow", "lineOverflow", "height", "lines", "wrapText", "contentHeight", "lineCount", "slice", "truncateMinChar", "singleOut", "outerHeight", "outerWidth", "RichTextToken", "RichTextLine", "tokens", "RichTextContentBlock", "parseRichText", "topTextAlign", "contentBlock", "stlPadding", "stlPaddingH", "stlPaddingV", "topWidth", "topHeight", "wrapInfo", "accumWidth", "breakAll", "lastIndex", "result", "exec", "matchedIndex", "index", "pushTokens", "substring", "pendingList", "calculatedHeight", "calculatedWidth", "truncateLine", "tmpTruncateOut", "finishLine", "line", "outer", "token", "tokenStyle", "styleName", "rich", "textPadding", "tokenHeight", "innerHeight", "align", "verticalAlign", "original<PERSON>ength", "styleTokenWidth", "tokenWidthNotSpecified", "char<PERSON>t", "percentWidth", "push", "textBackgroundColor", "backgroundColor", "bgImg", "image", "findExistImage", "isImageReady", "remainTrunc<PERSON>idth", "parseInt", "block", "str", "isEmptyStr", "newLine", "strLines", "linesWidths", "tokenPadding", "tokenPaddingH", "outerWidth_1", "res", "isLineHolder", "tokensLen", "isAlphabeticLetter", "ch", "code", "breakCharMap", "obj", "isWordBreakChar", "isBreakAll", "lastAccum<PERSON>idth", "currentWord", "currentWordWidth", "ch<PERSON><PERSON><PERSON>", "inWord", "calcInnerTextOverflowArea", "overflowRect", "baseX", "baseY", "textAlign", "textVerticalAlign", "textWidth", "textHeight", "set", "tmpCITCTextRect", "intersect", "tmpCITCIntersectRectOpt", "outIntersectRect", "x", "y", "clamp", "tSpanCreateBoundingRect", "tSpanCreateBoundingRect2", "forceLineWidth", "rect", "textBaseline", "tSpanHasStroke", "stroke"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/zrender/lib/graphic/helper/parseText.js"], "sourcesContent": ["import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce, } from '../../core/util.js';\nimport { adjustTextX, adjustTextY, ensureFontMeasureInfo, getLineHeight, measureCharWidth, measureWidth, parsePercent, } from '../../contain/text.js';\nimport BoundingRect from '../../core/BoundingRect.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n    var out = {};\n    truncateText2(out, text, containerWidth, font, ellipsis, options);\n    return out.text;\n}\nfunction truncateText2(out, text, containerWidth, font, ellipsis, options) {\n    if (!containerWidth) {\n        out.text = '';\n        out.isTruncated = false;\n        return;\n    }\n    var textLines = (text + '').split('\\n');\n    options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n    var isTruncated = false;\n    var truncateOut = {};\n    for (var i = 0, len = textLines.length; i < len; i++) {\n        truncateSingleLine(truncateOut, textLines[i], options);\n        textLines[i] = truncateOut.textLine;\n        isTruncated = isTruncated || truncateOut.isTruncated;\n    }\n    out.text = textLines.join('\\n');\n    out.isTruncated = isTruncated;\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n    options = options || {};\n    var preparedOpts = extend({}, options);\n    ellipsis = retrieve2(ellipsis, '...');\n    preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n    var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n    var fontMeasureInfo = preparedOpts.fontMeasureInfo = ensureFontMeasureInfo(font);\n    var ascCharWidth = fontMeasureInfo.asciiCharWidth;\n    preparedOpts.placeholder = retrieve2(options.placeholder, '');\n    var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n    for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n        contentWidth -= ascCharWidth;\n    }\n    var ellipsisWidth = measureWidth(fontMeasureInfo, ellipsis);\n    if (ellipsisWidth > contentWidth) {\n        ellipsis = '';\n        ellipsisWidth = 0;\n    }\n    contentWidth = containerWidth - ellipsisWidth;\n    preparedOpts.ellipsis = ellipsis;\n    preparedOpts.ellipsisWidth = ellipsisWidth;\n    preparedOpts.contentWidth = contentWidth;\n    preparedOpts.containerWidth = containerWidth;\n    return preparedOpts;\n}\nfunction truncateSingleLine(out, textLine, options) {\n    var containerWidth = options.containerWidth;\n    var contentWidth = options.contentWidth;\n    var fontMeasureInfo = options.fontMeasureInfo;\n    if (!containerWidth) {\n        out.textLine = '';\n        out.isTruncated = false;\n        return;\n    }\n    var lineWidth = measureWidth(fontMeasureInfo, textLine);\n    if (lineWidth <= containerWidth) {\n        out.textLine = textLine;\n        out.isTruncated = false;\n        return;\n    }\n    for (var j = 0;; j++) {\n        if (lineWidth <= contentWidth || j >= options.maxIterations) {\n            textLine += options.ellipsis;\n            break;\n        }\n        var subLength = j === 0\n            ? estimateLength(textLine, contentWidth, fontMeasureInfo)\n            : lineWidth > 0\n                ? Math.floor(textLine.length * contentWidth / lineWidth)\n                : 0;\n        textLine = textLine.substr(0, subLength);\n        lineWidth = measureWidth(fontMeasureInfo, textLine);\n    }\n    if (textLine === '') {\n        textLine = options.placeholder;\n    }\n    out.textLine = textLine;\n    out.isTruncated = true;\n}\nfunction estimateLength(text, contentWidth, fontMeasureInfo) {\n    var width = 0;\n    var i = 0;\n    for (var len = text.length; i < len && width < contentWidth; i++) {\n        width += measureCharWidth(fontMeasureInfo, text.charCodeAt(i));\n    }\n    return i;\n}\nexport function parsePlainText(rawText, style, defaultOuterWidth, defaultOuterHeight) {\n    var text = formatText(rawText);\n    var overflow = style.overflow;\n    var padding = style.padding;\n    var paddingH = padding ? padding[1] + padding[3] : 0;\n    var paddingV = padding ? padding[0] + padding[2] : 0;\n    var font = style.font;\n    var truncate = overflow === 'truncate';\n    var calculatedLineHeight = getLineHeight(font);\n    var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n    var truncateLineOverflow = style.lineOverflow === 'truncate';\n    var isTruncated = false;\n    var width = style.width;\n    if (width == null && defaultOuterWidth != null) {\n        width = defaultOuterWidth - paddingH;\n    }\n    var height = style.height;\n    if (height == null && defaultOuterHeight != null) {\n        height = defaultOuterHeight - paddingV;\n    }\n    var lines;\n    if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n        lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n    }\n    else {\n        lines = text ? text.split('\\n') : [];\n    }\n    var contentHeight = lines.length * lineHeight;\n    if (height == null) {\n        height = contentHeight;\n    }\n    if (contentHeight > height && truncateLineOverflow) {\n        var lineCount = Math.floor(height / lineHeight);\n        isTruncated = isTruncated || (lines.length > lineCount);\n        lines = lines.slice(0, lineCount);\n        contentHeight = lines.length * lineHeight;\n    }\n    if (text && truncate && width != null) {\n        var options = prepareTruncateOptions(width, font, style.ellipsis, {\n            minChar: style.truncateMinChar,\n            placeholder: style.placeholder\n        });\n        var singleOut = {};\n        for (var i = 0; i < lines.length; i++) {\n            truncateSingleLine(singleOut, lines[i], options);\n            lines[i] = singleOut.textLine;\n            isTruncated = isTruncated || singleOut.isTruncated;\n        }\n    }\n    var outerHeight = height;\n    var contentWidth = 0;\n    var fontMeasureInfo = ensureFontMeasureInfo(font);\n    for (var i = 0; i < lines.length; i++) {\n        contentWidth = Math.max(measureWidth(fontMeasureInfo, lines[i]), contentWidth);\n    }\n    if (width == null) {\n        width = contentWidth;\n    }\n    var outerWidth = width;\n    outerHeight += paddingV;\n    outerWidth += paddingH;\n    return {\n        lines: lines,\n        height: height,\n        outerWidth: outerWidth,\n        outerHeight: outerHeight,\n        lineHeight: lineHeight,\n        calculatedLineHeight: calculatedLineHeight,\n        contentWidth: contentWidth,\n        contentHeight: contentHeight,\n        width: width,\n        isTruncated: isTruncated\n    };\n}\nvar RichTextToken = (function () {\n    function RichTextToken() {\n    }\n    return RichTextToken;\n}());\nvar RichTextLine = (function () {\n    function RichTextLine(tokens) {\n        this.tokens = [];\n        if (tokens) {\n            this.tokens = tokens;\n        }\n    }\n    return RichTextLine;\n}());\nvar RichTextContentBlock = (function () {\n    function RichTextContentBlock() {\n        this.width = 0;\n        this.height = 0;\n        this.contentWidth = 0;\n        this.contentHeight = 0;\n        this.outerWidth = 0;\n        this.outerHeight = 0;\n        this.lines = [];\n        this.isTruncated = false;\n    }\n    return RichTextContentBlock;\n}());\nexport { RichTextContentBlock };\nexport function parseRichText(rawText, style, defaultOuterWidth, defaultOuterHeight, topTextAlign) {\n    var contentBlock = new RichTextContentBlock();\n    var text = formatText(rawText);\n    if (!text) {\n        return contentBlock;\n    }\n    var stlPadding = style.padding;\n    var stlPaddingH = stlPadding ? stlPadding[1] + stlPadding[3] : 0;\n    var stlPaddingV = stlPadding ? stlPadding[0] + stlPadding[2] : 0;\n    var topWidth = style.width;\n    if (topWidth == null && defaultOuterWidth != null) {\n        topWidth = defaultOuterWidth - stlPaddingH;\n    }\n    var topHeight = style.height;\n    if (topHeight == null && defaultOuterHeight != null) {\n        topHeight = defaultOuterHeight - stlPaddingV;\n    }\n    var overflow = style.overflow;\n    var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null\n        ? { width: topWidth, accumWidth: 0, breakAll: overflow === 'breakAll' }\n        : null;\n    var lastIndex = STYLE_REG.lastIndex = 0;\n    var result;\n    while ((result = STYLE_REG.exec(text)) != null) {\n        var matchedIndex = result.index;\n        if (matchedIndex > lastIndex) {\n            pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n        }\n        pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n        lastIndex = STYLE_REG.lastIndex;\n    }\n    if (lastIndex < text.length) {\n        pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n    }\n    var pendingList = [];\n    var calculatedHeight = 0;\n    var calculatedWidth = 0;\n    var truncate = overflow === 'truncate';\n    var truncateLine = style.lineOverflow === 'truncate';\n    var tmpTruncateOut = {};\n    function finishLine(line, lineWidth, lineHeight) {\n        line.width = lineWidth;\n        line.lineHeight = lineHeight;\n        calculatedHeight += lineHeight;\n        calculatedWidth = Math.max(calculatedWidth, lineWidth);\n    }\n    outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n        var line = contentBlock.lines[i];\n        var lineHeight = 0;\n        var lineWidth = 0;\n        for (var j = 0; j < line.tokens.length; j++) {\n            var token = line.tokens[j];\n            var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n            var textPadding = token.textPadding = tokenStyle.padding;\n            var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n            var font = token.font = tokenStyle.font || style.font;\n            token.contentHeight = getLineHeight(font);\n            var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n            token.innerHeight = tokenHeight;\n            textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n            token.height = tokenHeight;\n            token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n            token.align = tokenStyle && tokenStyle.align || topTextAlign;\n            token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n            if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n                var originalLength = contentBlock.lines.length;\n                if (j > 0) {\n                    line.tokens = line.tokens.slice(0, j);\n                    finishLine(line, lineWidth, lineHeight);\n                    contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n                }\n                else {\n                    contentBlock.lines = contentBlock.lines.slice(0, i);\n                }\n                contentBlock.isTruncated = contentBlock.isTruncated || (contentBlock.lines.length < originalLength);\n                break outer;\n            }\n            var styleTokenWidth = tokenStyle.width;\n            var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n            if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n                token.percentWidth = styleTokenWidth;\n                pendingList.push(token);\n                token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n            }\n            else {\n                if (tokenWidthNotSpecified) {\n                    var textBackgroundColor = tokenStyle.backgroundColor;\n                    var bgImg = textBackgroundColor && textBackgroundColor.image;\n                    if (bgImg) {\n                        bgImg = imageHelper.findExistImage(bgImg);\n                        if (imageHelper.isImageReady(bgImg)) {\n                            token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n                        }\n                    }\n                }\n                var remainTruncWidth = truncate && topWidth != null\n                    ? topWidth - lineWidth : null;\n                if (remainTruncWidth != null && remainTruncWidth < token.width) {\n                    if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n                        token.text = '';\n                        token.width = token.contentWidth = 0;\n                    }\n                    else {\n                        truncateText2(tmpTruncateOut, token.text, remainTruncWidth - paddingH, font, style.ellipsis, { minChar: style.truncateMinChar });\n                        token.text = tmpTruncateOut.text;\n                        contentBlock.isTruncated = contentBlock.isTruncated || tmpTruncateOut.isTruncated;\n                        token.width = token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n                    }\n                }\n                else {\n                    token.contentWidth = measureWidth(ensureFontMeasureInfo(font), token.text);\n                }\n            }\n            token.width += paddingH;\n            lineWidth += token.width;\n            tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n        }\n        finishLine(line, lineWidth, lineHeight);\n    }\n    contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n    contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n    contentBlock.contentHeight = calculatedHeight;\n    contentBlock.contentWidth = calculatedWidth;\n    contentBlock.outerWidth += stlPaddingH;\n    contentBlock.outerHeight += stlPaddingV;\n    for (var i = 0; i < pendingList.length; i++) {\n        var token = pendingList[i];\n        var percentWidth = token.percentWidth;\n        token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n    }\n    return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n    var isEmptyStr = str === '';\n    var tokenStyle = styleName && style.rich[styleName] || {};\n    var lines = block.lines;\n    var font = tokenStyle.font || style.font;\n    var newLine = false;\n    var strLines;\n    var linesWidths;\n    if (wrapInfo) {\n        var tokenPadding = tokenStyle.padding;\n        var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n        if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n            var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n            if (lines.length > 0) {\n                if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n                    strLines = str.split('\\n');\n                    newLine = true;\n                }\n            }\n            wrapInfo.accumWidth = outerWidth_1;\n        }\n        else {\n            var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n            wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n            linesWidths = res.linesWidths;\n            strLines = res.lines;\n        }\n    }\n    if (!strLines) {\n        strLines = str.split('\\n');\n    }\n    var fontMeasureInfo = ensureFontMeasureInfo(font);\n    for (var i = 0; i < strLines.length; i++) {\n        var text = strLines[i];\n        var token = new RichTextToken();\n        token.styleName = styleName;\n        token.text = text;\n        token.isLineHolder = !text && !isEmptyStr;\n        if (typeof tokenStyle.width === 'number') {\n            token.width = tokenStyle.width;\n        }\n        else {\n            token.width = linesWidths\n                ? linesWidths[i]\n                : measureWidth(fontMeasureInfo, text);\n        }\n        if (!i && !newLine) {\n            var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n            var tokensLen = tokens.length;\n            (tokensLen === 1 && tokens[0].isLineHolder)\n                ? (tokens[0] = token)\n                : ((text || !tokensLen || isEmptyStr) && tokens.push(token));\n        }\n        else {\n            lines.push(new RichTextLine([token]));\n        }\n    }\n}\nfunction isAlphabeticLetter(ch) {\n    var code = ch.charCodeAt(0);\n    return code >= 0x20 && code <= 0x24F\n        || code >= 0x370 && code <= 0x10FF\n        || code >= 0x1200 && code <= 0x13FF\n        || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n    obj[ch] = true;\n    return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n    if (isAlphabeticLetter(ch)) {\n        if (breakCharMap[ch]) {\n            return true;\n        }\n        return false;\n    }\n    return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n    var lines = [];\n    var linesWidths = [];\n    var line = '';\n    var currentWord = '';\n    var currentWordWidth = 0;\n    var accumWidth = 0;\n    var fontMeasureInfo = ensureFontMeasureInfo(font);\n    for (var i = 0; i < text.length; i++) {\n        var ch = text.charAt(i);\n        if (ch === '\\n') {\n            if (currentWord) {\n                line += currentWord;\n                accumWidth += currentWordWidth;\n            }\n            lines.push(line);\n            linesWidths.push(accumWidth);\n            line = '';\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = 0;\n            continue;\n        }\n        var chWidth = measureCharWidth(fontMeasureInfo, ch.charCodeAt(0));\n        var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n        if (!lines.length\n            ? lastAccumWidth + accumWidth + chWidth > lineWidth\n            : accumWidth + chWidth > lineWidth) {\n            if (!accumWidth) {\n                if (inWord) {\n                    lines.push(currentWord);\n                    linesWidths.push(currentWordWidth);\n                    currentWord = ch;\n                    currentWordWidth = chWidth;\n                }\n                else {\n                    lines.push(ch);\n                    linesWidths.push(chWidth);\n                }\n            }\n            else if (line || currentWord) {\n                if (inWord) {\n                    if (!line) {\n                        line = currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                        accumWidth = currentWordWidth;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth - currentWordWidth);\n                    currentWord += ch;\n                    currentWordWidth += chWidth;\n                    line = '';\n                    accumWidth = currentWordWidth;\n                }\n                else {\n                    if (currentWord) {\n                        line += currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth);\n                    line = ch;\n                    accumWidth = chWidth;\n                }\n            }\n            continue;\n        }\n        accumWidth += chWidth;\n        if (inWord) {\n            currentWord += ch;\n            currentWordWidth += chWidth;\n        }\n        else {\n            if (currentWord) {\n                line += currentWord;\n                currentWord = '';\n                currentWordWidth = 0;\n            }\n            line += ch;\n        }\n    }\n    if (currentWord) {\n        line += currentWord;\n    }\n    if (line) {\n        lines.push(line);\n        linesWidths.push(accumWidth);\n    }\n    if (lines.length === 1) {\n        accumWidth += lastAccumWidth;\n    }\n    return {\n        accumWidth: accumWidth,\n        lines: lines,\n        linesWidths: linesWidths\n    };\n}\nexport function calcInnerTextOverflowArea(out, overflowRect, baseX, baseY, textAlign, textVerticalAlign) {\n    out.baseX = baseX;\n    out.baseY = baseY;\n    out.outerWidth = out.outerHeight = null;\n    if (!overflowRect) {\n        return;\n    }\n    var textWidth = overflowRect.width * 2;\n    var textHeight = overflowRect.height * 2;\n    BoundingRect.set(tmpCITCTextRect, adjustTextX(baseX, textWidth, textAlign), adjustTextY(baseY, textHeight, textVerticalAlign), textWidth, textHeight);\n    BoundingRect.intersect(overflowRect, tmpCITCTextRect, null, tmpCITCIntersectRectOpt);\n    var outIntersectRect = tmpCITCIntersectRectOpt.outIntersectRect;\n    out.outerWidth = outIntersectRect.width;\n    out.outerHeight = outIntersectRect.height;\n    out.baseX = adjustTextX(outIntersectRect.x, outIntersectRect.width, textAlign, true);\n    out.baseY = adjustTextY(outIntersectRect.y, outIntersectRect.height, textVerticalAlign, true);\n}\nvar tmpCITCTextRect = new BoundingRect(0, 0, 0, 0);\nvar tmpCITCIntersectRectOpt = { outIntersectRect: {}, clamp: true };\nfunction formatText(text) {\n    return text != null ? (text += '') : (text = '');\n}\nexport function tSpanCreateBoundingRect(style) {\n    var text = formatText(style.text);\n    var font = style.font;\n    var contentWidth = measureWidth(ensureFontMeasureInfo(font), text);\n    var contentHeight = getLineHeight(font);\n    return tSpanCreateBoundingRect2(style, contentWidth, contentHeight, null);\n}\nexport function tSpanCreateBoundingRect2(style, contentWidth, contentHeight, forceLineWidth) {\n    var rect = new BoundingRect(adjustTextX(style.x || 0, contentWidth, style.textAlign), adjustTextY(style.y || 0, contentHeight, style.textBaseline), contentWidth, contentHeight);\n    var lineWidth = forceLineWidth != null\n        ? forceLineWidth\n        : (tSpanHasStroke(style) ? style.lineWidth : 0);\n    if (lineWidth > 0) {\n        rect.x -= lineWidth / 2;\n        rect.y -= lineWidth / 2;\n        rect.width += lineWidth;\n        rect.height += lineWidth;\n    }\n    return rect;\n}\nexport function tSpanHasStroke(style) {\n    var stroke = style.stroke;\n    return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,WAAW,MAAM,oBAAoB;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,QAAS,oBAAoB;AAC1E,SAASC,WAAW,EAAEC,WAAW,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,YAAY,QAAS,uBAAuB;AACrJ,OAAOC,YAAY,MAAM,4BAA4B;AACrD,IAAIC,SAAS,GAAG,+BAA+B;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACxE,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZC,aAAa,CAACD,GAAG,EAAEL,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACjE,OAAOC,GAAG,CAACL,IAAI;AACnB;AACA,SAASM,aAAaA,CAACD,GAAG,EAAEL,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACvE,IAAI,CAACH,cAAc,EAAE;IACjBI,GAAG,CAACL,IAAI,GAAG,EAAE;IACbK,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,IAAIC,SAAS,GAAG,CAACR,IAAI,GAAG,EAAE,EAAES,KAAK,CAAC,IAAI,CAAC;EACvCL,OAAO,GAAGM,sBAAsB,CAACT,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACzE,IAAIG,WAAW,GAAG,KAAK;EACvB,IAAII,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,SAAS,CAACM,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAClDG,kBAAkB,CAACJ,WAAW,EAAEH,SAAS,CAACI,CAAC,CAAC,EAAER,OAAO,CAAC;IACtDI,SAAS,CAACI,CAAC,CAAC,GAAGD,WAAW,CAACK,QAAQ;IACnCT,WAAW,GAAGA,WAAW,IAAII,WAAW,CAACJ,WAAW;EACxD;EACAF,GAAG,CAACL,IAAI,GAAGQ,SAAS,CAACS,IAAI,CAAC,IAAI,CAAC;EAC/BZ,GAAG,CAACE,WAAW,GAAGA,WAAW;AACjC;AACA,SAASG,sBAAsBA,CAACT,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIc,YAAY,GAAGhC,MAAM,CAAC,CAAC,CAAC,EAAEkB,OAAO,CAAC;EACtCD,QAAQ,GAAGhB,SAAS,CAACgB,QAAQ,EAAE,KAAK,CAAC;EACrCe,YAAY,CAACC,aAAa,GAAGhC,SAAS,CAACiB,OAAO,CAACe,aAAa,EAAE,CAAC,CAAC;EAChE,IAAIC,OAAO,GAAGF,YAAY,CAACE,OAAO,GAAGjC,SAAS,CAACiB,OAAO,CAACgB,OAAO,EAAE,CAAC,CAAC;EAClE,IAAIC,eAAe,GAAGH,YAAY,CAACG,eAAe,GAAG7B,qBAAqB,CAACU,IAAI,CAAC;EAChF,IAAIoB,YAAY,GAAGD,eAAe,CAACE,cAAc;EACjDL,YAAY,CAACM,WAAW,GAAGrC,SAAS,CAACiB,OAAO,CAACoB,WAAW,EAAE,EAAE,CAAC;EAC7D,IAAIC,YAAY,GAAGxB,cAAc,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE1B,cAAc,GAAG,CAAC,CAAC;EACnE,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,OAAO,IAAIK,YAAY,IAAIH,YAAY,EAAEV,CAAC,EAAE,EAAE;IAC9Da,YAAY,IAAIH,YAAY;EAChC;EACA,IAAIM,aAAa,GAAGjC,YAAY,CAAC0B,eAAe,EAAElB,QAAQ,CAAC;EAC3D,IAAIyB,aAAa,GAAGH,YAAY,EAAE;IAC9BtB,QAAQ,GAAG,EAAE;IACbyB,aAAa,GAAG,CAAC;EACrB;EACAH,YAAY,GAAGxB,cAAc,GAAG2B,aAAa;EAC7CV,YAAY,CAACf,QAAQ,GAAGA,QAAQ;EAChCe,YAAY,CAACU,aAAa,GAAGA,aAAa;EAC1CV,YAAY,CAACO,YAAY,GAAGA,YAAY;EACxCP,YAAY,CAACjB,cAAc,GAAGA,cAAc;EAC5C,OAAOiB,YAAY;AACvB;AACA,SAASH,kBAAkBA,CAACV,GAAG,EAAEW,QAAQ,EAAEZ,OAAO,EAAE;EAChD,IAAIH,cAAc,GAAGG,OAAO,CAACH,cAAc;EAC3C,IAAIwB,YAAY,GAAGrB,OAAO,CAACqB,YAAY;EACvC,IAAIJ,eAAe,GAAGjB,OAAO,CAACiB,eAAe;EAC7C,IAAI,CAACpB,cAAc,EAAE;IACjBI,GAAG,CAACW,QAAQ,GAAG,EAAE;IACjBX,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,IAAIsB,SAAS,GAAGlC,YAAY,CAAC0B,eAAe,EAAEL,QAAQ,CAAC;EACvD,IAAIa,SAAS,IAAI5B,cAAc,EAAE;IAC7BI,GAAG,CAACW,QAAQ,GAAGA,QAAQ;IACvBX,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,EAAE;IAClB,IAAID,SAAS,IAAIJ,YAAY,IAAIK,CAAC,IAAI1B,OAAO,CAACe,aAAa,EAAE;MACzDH,QAAQ,IAAIZ,OAAO,CAACD,QAAQ;MAC5B;IACJ;IACA,IAAI4B,SAAS,GAAGD,CAAC,KAAK,CAAC,GACjBE,cAAc,CAAChB,QAAQ,EAAES,YAAY,EAAEJ,eAAe,CAAC,GACvDQ,SAAS,GAAG,CAAC,GACTH,IAAI,CAACO,KAAK,CAACjB,QAAQ,CAACF,MAAM,GAAGW,YAAY,GAAGI,SAAS,CAAC,GACtD,CAAC;IACXb,QAAQ,GAAGA,QAAQ,CAACkB,MAAM,CAAC,CAAC,EAAEH,SAAS,CAAC;IACxCF,SAAS,GAAGlC,YAAY,CAAC0B,eAAe,EAAEL,QAAQ,CAAC;EACvD;EACA,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACjBA,QAAQ,GAAGZ,OAAO,CAACoB,WAAW;EAClC;EACAnB,GAAG,CAACW,QAAQ,GAAGA,QAAQ;EACvBX,GAAG,CAACE,WAAW,GAAG,IAAI;AAC1B;AACA,SAASyB,cAAcA,CAAChC,IAAI,EAAEyB,YAAY,EAAEJ,eAAe,EAAE;EACzD,IAAIc,KAAK,GAAG,CAAC;EACb,IAAIvB,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,GAAG,GAAGb,IAAI,CAACc,MAAM,EAAEF,CAAC,GAAGC,GAAG,IAAIsB,KAAK,GAAGV,YAAY,EAAEb,CAAC,EAAE,EAAE;IAC9DuB,KAAK,IAAIzC,gBAAgB,CAAC2B,eAAe,EAAErB,IAAI,CAACoC,UAAU,CAACxB,CAAC,CAAC,CAAC;EAClE;EACA,OAAOA,CAAC;AACZ;AACA,OAAO,SAASyB,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAE;EAClF,IAAIzC,IAAI,GAAG0C,UAAU,CAACJ,OAAO,CAAC;EAC9B,IAAIK,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC7B,IAAIC,OAAO,GAAGL,KAAK,CAACK,OAAO;EAC3B,IAAIC,QAAQ,GAAGD,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACpD,IAAIE,QAAQ,GAAGF,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACpD,IAAI1C,IAAI,GAAGqC,KAAK,CAACrC,IAAI;EACrB,IAAI6C,QAAQ,GAAGJ,QAAQ,KAAK,UAAU;EACtC,IAAIK,oBAAoB,GAAGvD,aAAa,CAACS,IAAI,CAAC;EAC9C,IAAI+C,UAAU,GAAG9D,SAAS,CAACoD,KAAK,CAACU,UAAU,EAAED,oBAAoB,CAAC;EAClE,IAAIE,oBAAoB,GAAGX,KAAK,CAACY,YAAY,KAAK,UAAU;EAC5D,IAAI5C,WAAW,GAAG,KAAK;EACvB,IAAI4B,KAAK,GAAGI,KAAK,CAACJ,KAAK;EACvB,IAAIA,KAAK,IAAI,IAAI,IAAIK,iBAAiB,IAAI,IAAI,EAAE;IAC5CL,KAAK,GAAGK,iBAAiB,GAAGK,QAAQ;EACxC;EACA,IAAIO,MAAM,GAAGb,KAAK,CAACa,MAAM;EACzB,IAAIA,MAAM,IAAI,IAAI,IAAIX,kBAAkB,IAAI,IAAI,EAAE;IAC9CW,MAAM,GAAGX,kBAAkB,GAAGK,QAAQ;EAC1C;EACA,IAAIO,KAAK;EACT,IAAIlB,KAAK,IAAI,IAAI,KAAKQ,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,CAAC,EAAE;IACpEU,KAAK,GAAGrD,IAAI,GAAGsD,QAAQ,CAACtD,IAAI,EAAEuC,KAAK,CAACrC,IAAI,EAAEiC,KAAK,EAAEQ,QAAQ,KAAK,UAAU,EAAE,CAAC,CAAC,CAACU,KAAK,GAAG,EAAE;EAC3F,CAAC,MACI;IACDA,KAAK,GAAGrD,IAAI,GAAGA,IAAI,CAACS,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;EACxC;EACA,IAAI8C,aAAa,GAAGF,KAAK,CAACvC,MAAM,GAAGmC,UAAU;EAC7C,IAAIG,MAAM,IAAI,IAAI,EAAE;IAChBA,MAAM,GAAGG,aAAa;EAC1B;EACA,IAAIA,aAAa,GAAGH,MAAM,IAAIF,oBAAoB,EAAE;IAChD,IAAIM,SAAS,GAAG9B,IAAI,CAACO,KAAK,CAACmB,MAAM,GAAGH,UAAU,CAAC;IAC/C1C,WAAW,GAAGA,WAAW,IAAK8C,KAAK,CAACvC,MAAM,GAAG0C,SAAU;IACvDH,KAAK,GAAGA,KAAK,CAACI,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC;IACjCD,aAAa,GAAGF,KAAK,CAACvC,MAAM,GAAGmC,UAAU;EAC7C;EACA,IAAIjD,IAAI,IAAI+C,QAAQ,IAAIZ,KAAK,IAAI,IAAI,EAAE;IACnC,IAAI/B,OAAO,GAAGM,sBAAsB,CAACyB,KAAK,EAAEjC,IAAI,EAAEqC,KAAK,CAACpC,QAAQ,EAAE;MAC9DiB,OAAO,EAAEmB,KAAK,CAACmB,eAAe;MAC9BlC,WAAW,EAAEe,KAAK,CAACf;IACvB,CAAC,CAAC;IACF,IAAImC,SAAS,GAAG,CAAC,CAAC;IAClB,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,KAAK,CAACvC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACnCG,kBAAkB,CAAC4C,SAAS,EAAEN,KAAK,CAACzC,CAAC,CAAC,EAAER,OAAO,CAAC;MAChDiD,KAAK,CAACzC,CAAC,CAAC,GAAG+C,SAAS,CAAC3C,QAAQ;MAC7BT,WAAW,GAAGA,WAAW,IAAIoD,SAAS,CAACpD,WAAW;IACtD;EACJ;EACA,IAAIqD,WAAW,GAAGR,MAAM;EACxB,IAAI3B,YAAY,GAAG,CAAC;EACpB,IAAIJ,eAAe,GAAG7B,qBAAqB,CAACU,IAAI,CAAC;EACjD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,KAAK,CAACvC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACnCa,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAChC,YAAY,CAAC0B,eAAe,EAAEgC,KAAK,CAACzC,CAAC,CAAC,CAAC,EAAEa,YAAY,CAAC;EAClF;EACA,IAAIU,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGV,YAAY;EACxB;EACA,IAAIoC,UAAU,GAAG1B,KAAK;EACtByB,WAAW,IAAId,QAAQ;EACvBe,UAAU,IAAIhB,QAAQ;EACtB,OAAO;IACHQ,KAAK,EAAEA,KAAK;IACZD,MAAM,EAAEA,MAAM;IACdS,UAAU,EAAEA,UAAU;IACtBD,WAAW,EAAEA,WAAW;IACxBX,UAAU,EAAEA,UAAU;IACtBD,oBAAoB,EAAEA,oBAAoB;IAC1CvB,YAAY,EAAEA,YAAY;IAC1B8B,aAAa,EAAEA,aAAa;IAC5BpB,KAAK,EAAEA,KAAK;IACZ5B,WAAW,EAAEA;EACjB,CAAC;AACL;AACA,IAAIuD,aAAa,GAAI,YAAY;EAC7B,SAASA,aAAaA,CAAA,EAAG,CACzB;EACA,OAAOA,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;EACJ;EACA,OAAOD,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,IAAIE,oBAAoB,GAAI,YAAY;EACpC,SAASA,oBAAoBA,CAAA,EAAG;IAC5B,IAAI,CAAC9B,KAAK,GAAG,CAAC;IACd,IAAI,CAACiB,MAAM,GAAG,CAAC;IACf,IAAI,CAAC3B,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC8B,aAAa,GAAG,CAAC;IACtB,IAAI,CAACM,UAAU,GAAG,CAAC;IACnB,IAAI,CAACD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACP,KAAK,GAAG,EAAE;IACf,IAAI,CAAC9C,WAAW,GAAG,KAAK;EAC5B;EACA,OAAO0D,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,SAASA,oBAAoB;AAC7B,OAAO,SAASC,aAAaA,CAAC5B,OAAO,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAE0B,YAAY,EAAE;EAC/F,IAAIC,YAAY,GAAG,IAAIH,oBAAoB,CAAC,CAAC;EAC7C,IAAIjE,IAAI,GAAG0C,UAAU,CAACJ,OAAO,CAAC;EAC9B,IAAI,CAACtC,IAAI,EAAE;IACP,OAAOoE,YAAY;EACvB;EACA,IAAIC,UAAU,GAAG9B,KAAK,CAACK,OAAO;EAC9B,IAAI0B,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAChE,IAAIE,WAAW,GAAGF,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAChE,IAAIG,QAAQ,GAAGjC,KAAK,CAACJ,KAAK;EAC1B,IAAIqC,QAAQ,IAAI,IAAI,IAAIhC,iBAAiB,IAAI,IAAI,EAAE;IAC/CgC,QAAQ,GAAGhC,iBAAiB,GAAG8B,WAAW;EAC9C;EACA,IAAIG,SAAS,GAAGlC,KAAK,CAACa,MAAM;EAC5B,IAAIqB,SAAS,IAAI,IAAI,IAAIhC,kBAAkB,IAAI,IAAI,EAAE;IACjDgC,SAAS,GAAGhC,kBAAkB,GAAG8B,WAAW;EAChD;EACA,IAAI5B,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC7B,IAAI+B,QAAQ,GAAG,CAAC/B,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,KAAK6B,QAAQ,IAAI,IAAI,GAC9E;IAAErC,KAAK,EAAEqC,QAAQ;IAAEG,UAAU,EAAE,CAAC;IAAEC,QAAQ,EAAEjC,QAAQ,KAAK;EAAW,CAAC,GACrE,IAAI;EACV,IAAIkC,SAAS,GAAG/E,SAAS,CAAC+E,SAAS,GAAG,CAAC;EACvC,IAAIC,MAAM;EACV,OAAO,CAACA,MAAM,GAAGhF,SAAS,CAACiF,IAAI,CAAC/E,IAAI,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAIgF,YAAY,GAAGF,MAAM,CAACG,KAAK;IAC/B,IAAID,YAAY,GAAGH,SAAS,EAAE;MAC1BK,UAAU,CAACd,YAAY,EAAEpE,IAAI,CAACmF,SAAS,CAACN,SAAS,EAAEG,YAAY,CAAC,EAAEzC,KAAK,EAAEmC,QAAQ,CAAC;IACtF;IACAQ,UAAU,CAACd,YAAY,EAAEU,MAAM,CAAC,CAAC,CAAC,EAAEvC,KAAK,EAAEmC,QAAQ,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/DD,SAAS,GAAG/E,SAAS,CAAC+E,SAAS;EACnC;EACA,IAAIA,SAAS,GAAG7E,IAAI,CAACc,MAAM,EAAE;IACzBoE,UAAU,CAACd,YAAY,EAAEpE,IAAI,CAACmF,SAAS,CAACN,SAAS,EAAE7E,IAAI,CAACc,MAAM,CAAC,EAAEyB,KAAK,EAAEmC,QAAQ,CAAC;EACrF;EACA,IAAIU,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIvC,QAAQ,GAAGJ,QAAQ,KAAK,UAAU;EACtC,IAAI4C,YAAY,GAAGhD,KAAK,CAACY,YAAY,KAAK,UAAU;EACpD,IAAIqC,cAAc,GAAG,CAAC,CAAC;EACvB,SAASC,UAAUA,CAACC,IAAI,EAAE7D,SAAS,EAAEoB,UAAU,EAAE;IAC7CyC,IAAI,CAACvD,KAAK,GAAGN,SAAS;IACtB6D,IAAI,CAACzC,UAAU,GAAGA,UAAU;IAC5BoC,gBAAgB,IAAIpC,UAAU;IAC9BqC,eAAe,GAAG5D,IAAI,CAACC,GAAG,CAAC2D,eAAe,EAAEzD,SAAS,CAAC;EAC1D;EACA8D,KAAK,EAAE,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,YAAY,CAACf,KAAK,CAACvC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACvD,IAAI8E,IAAI,GAAGtB,YAAY,CAACf,KAAK,CAACzC,CAAC,CAAC;IAChC,IAAIqC,UAAU,GAAG,CAAC;IAClB,IAAIpB,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,IAAI,CAAC1B,MAAM,CAAClD,MAAM,EAAEgB,CAAC,EAAE,EAAE;MACzC,IAAI8D,KAAK,GAAGF,IAAI,CAAC1B,MAAM,CAAClC,CAAC,CAAC;MAC1B,IAAI+D,UAAU,GAAGD,KAAK,CAACE,SAAS,IAAIvD,KAAK,CAACwD,IAAI,CAACH,KAAK,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;MACrE,IAAIE,WAAW,GAAGJ,KAAK,CAACI,WAAW,GAAGH,UAAU,CAACjD,OAAO;MACxD,IAAIC,QAAQ,GAAGmD,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;MAChE,IAAI9F,IAAI,GAAG0F,KAAK,CAAC1F,IAAI,GAAG2F,UAAU,CAAC3F,IAAI,IAAIqC,KAAK,CAACrC,IAAI;MACrD0F,KAAK,CAACrC,aAAa,GAAG9D,aAAa,CAACS,IAAI,CAAC;MACzC,IAAI+F,WAAW,GAAG9G,SAAS,CAAC0G,UAAU,CAACzC,MAAM,EAAEwC,KAAK,CAACrC,aAAa,CAAC;MACnEqC,KAAK,CAACM,WAAW,GAAGD,WAAW;MAC/BD,WAAW,KAAKC,WAAW,IAAID,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC;MAC/DJ,KAAK,CAACxC,MAAM,GAAG6C,WAAW;MAC1BL,KAAK,CAAC3C,UAAU,GAAG7D,SAAS,CAACyG,UAAU,CAAC5C,UAAU,EAAEV,KAAK,CAACU,UAAU,EAAEgD,WAAW,CAAC;MAClFL,KAAK,CAACO,KAAK,GAAGN,UAAU,IAAIA,UAAU,CAACM,KAAK,IAAIhC,YAAY;MAC5DyB,KAAK,CAACQ,aAAa,GAAGP,UAAU,IAAIA,UAAU,CAACO,aAAa,IAAI,QAAQ;MACxE,IAAIb,YAAY,IAAId,SAAS,IAAI,IAAI,IAAIY,gBAAgB,GAAGO,KAAK,CAAC3C,UAAU,GAAGwB,SAAS,EAAE;QACtF,IAAI4B,cAAc,GAAGjC,YAAY,CAACf,KAAK,CAACvC,MAAM;QAC9C,IAAIgB,CAAC,GAAG,CAAC,EAAE;UACP4D,IAAI,CAAC1B,MAAM,GAAG0B,IAAI,CAAC1B,MAAM,CAACP,KAAK,CAAC,CAAC,EAAE3B,CAAC,CAAC;UACrC2D,UAAU,CAACC,IAAI,EAAE7D,SAAS,EAAEoB,UAAU,CAAC;UACvCmB,YAAY,CAACf,KAAK,GAAGe,YAAY,CAACf,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE7C,CAAC,GAAG,CAAC,CAAC;QAC3D,CAAC,MACI;UACDwD,YAAY,CAACf,KAAK,GAAGe,YAAY,CAACf,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE7C,CAAC,CAAC;QACvD;QACAwD,YAAY,CAAC7D,WAAW,GAAG6D,YAAY,CAAC7D,WAAW,IAAK6D,YAAY,CAACf,KAAK,CAACvC,MAAM,GAAGuF,cAAe;QACnG,MAAMV,KAAK;MACf;MACA,IAAIW,eAAe,GAAGT,UAAU,CAAC1D,KAAK;MACtC,IAAIoE,sBAAsB,GAAGD,eAAe,IAAI,IAAI,IAAIA,eAAe,KAAK,MAAM;MAClF,IAAI,OAAOA,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACE,MAAM,CAACF,eAAe,CAACxF,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnG8E,KAAK,CAACa,YAAY,GAAGH,eAAe;QACpClB,WAAW,CAACsB,IAAI,CAACd,KAAK,CAAC;QACvBA,KAAK,CAACnE,YAAY,GAAG9B,YAAY,CAACH,qBAAqB,CAACU,IAAI,CAAC,EAAE0F,KAAK,CAAC5F,IAAI,CAAC;MAC9E,CAAC,MACI;QACD,IAAIuG,sBAAsB,EAAE;UACxB,IAAII,mBAAmB,GAAGd,UAAU,CAACe,eAAe;UACpD,IAAIC,KAAK,GAAGF,mBAAmB,IAAIA,mBAAmB,CAACG,KAAK;UAC5D,IAAID,KAAK,EAAE;YACPA,KAAK,GAAG5H,WAAW,CAAC8H,cAAc,CAACF,KAAK,CAAC;YACzC,IAAI5H,WAAW,CAAC+H,YAAY,CAACH,KAAK,CAAC,EAAE;cACjCjB,KAAK,CAACzD,KAAK,GAAGT,IAAI,CAACC,GAAG,CAACiE,KAAK,CAACzD,KAAK,EAAE0E,KAAK,CAAC1E,KAAK,GAAG8D,WAAW,GAAGY,KAAK,CAACzD,MAAM,CAAC;YACjF;UACJ;QACJ;QACA,IAAI6D,gBAAgB,GAAGlE,QAAQ,IAAIyB,QAAQ,IAAI,IAAI,GAC7CA,QAAQ,GAAG3C,SAAS,GAAG,IAAI;QACjC,IAAIoF,gBAAgB,IAAI,IAAI,IAAIA,gBAAgB,GAAGrB,KAAK,CAACzD,KAAK,EAAE;UAC5D,IAAI,CAACoE,sBAAsB,IAAIU,gBAAgB,GAAGpE,QAAQ,EAAE;YACxD+C,KAAK,CAAC5F,IAAI,GAAG,EAAE;YACf4F,KAAK,CAACzD,KAAK,GAAGyD,KAAK,CAACnE,YAAY,GAAG,CAAC;UACxC,CAAC,MACI;YACDnB,aAAa,CAACkF,cAAc,EAAEI,KAAK,CAAC5F,IAAI,EAAEiH,gBAAgB,GAAGpE,QAAQ,EAAE3C,IAAI,EAAEqC,KAAK,CAACpC,QAAQ,EAAE;cAAEiB,OAAO,EAAEmB,KAAK,CAACmB;YAAgB,CAAC,CAAC;YAChIkC,KAAK,CAAC5F,IAAI,GAAGwF,cAAc,CAACxF,IAAI;YAChCoE,YAAY,CAAC7D,WAAW,GAAG6D,YAAY,CAAC7D,WAAW,IAAIiF,cAAc,CAACjF,WAAW;YACjFqF,KAAK,CAACzD,KAAK,GAAGyD,KAAK,CAACnE,YAAY,GAAG9B,YAAY,CAACH,qBAAqB,CAACU,IAAI,CAAC,EAAE0F,KAAK,CAAC5F,IAAI,CAAC;UAC5F;QACJ,CAAC,MACI;UACD4F,KAAK,CAACnE,YAAY,GAAG9B,YAAY,CAACH,qBAAqB,CAACU,IAAI,CAAC,EAAE0F,KAAK,CAAC5F,IAAI,CAAC;QAC9E;MACJ;MACA4F,KAAK,CAACzD,KAAK,IAAIU,QAAQ;MACvBhB,SAAS,IAAI+D,KAAK,CAACzD,KAAK;MACxB0D,UAAU,KAAK5C,UAAU,GAAGvB,IAAI,CAACC,GAAG,CAACsB,UAAU,EAAE2C,KAAK,CAAC3C,UAAU,CAAC,CAAC;IACvE;IACAwC,UAAU,CAACC,IAAI,EAAE7D,SAAS,EAAEoB,UAAU,CAAC;EAC3C;EACAmB,YAAY,CAACP,UAAU,GAAGO,YAAY,CAACjC,KAAK,GAAGhD,SAAS,CAACqF,QAAQ,EAAEc,eAAe,CAAC;EACnFlB,YAAY,CAACR,WAAW,GAAGQ,YAAY,CAAChB,MAAM,GAAGjE,SAAS,CAACsF,SAAS,EAAEY,gBAAgB,CAAC;EACvFjB,YAAY,CAACb,aAAa,GAAG8B,gBAAgB;EAC7CjB,YAAY,CAAC3C,YAAY,GAAG6D,eAAe;EAC3ClB,YAAY,CAACP,UAAU,IAAIS,WAAW;EACtCF,YAAY,CAACR,WAAW,IAAIW,WAAW;EACvC,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,WAAW,CAACtE,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIgF,KAAK,GAAGR,WAAW,CAACxE,CAAC,CAAC;IAC1B,IAAI6F,YAAY,GAAGb,KAAK,CAACa,YAAY;IACrCb,KAAK,CAACzD,KAAK,GAAG+E,QAAQ,CAACT,YAAY,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGrC,YAAY,CAACjC,KAAK;EACvE;EACA,OAAOiC,YAAY;AACvB;AACA,SAASc,UAAUA,CAACiC,KAAK,EAAEC,GAAG,EAAE7E,KAAK,EAAEmC,QAAQ,EAAEoB,SAAS,EAAE;EACxD,IAAIuB,UAAU,GAAGD,GAAG,KAAK,EAAE;EAC3B,IAAIvB,UAAU,GAAGC,SAAS,IAAIvD,KAAK,CAACwD,IAAI,CAACD,SAAS,CAAC,IAAI,CAAC,CAAC;EACzD,IAAIzC,KAAK,GAAG8D,KAAK,CAAC9D,KAAK;EACvB,IAAInD,IAAI,GAAG2F,UAAU,CAAC3F,IAAI,IAAIqC,KAAK,CAACrC,IAAI;EACxC,IAAIoH,OAAO,GAAG,KAAK;EACnB,IAAIC,QAAQ;EACZ,IAAIC,WAAW;EACf,IAAI9C,QAAQ,EAAE;IACV,IAAI+C,YAAY,GAAG5B,UAAU,CAACjD,OAAO;IACrC,IAAI8E,aAAa,GAAGD,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IACxE,IAAI5B,UAAU,CAAC1D,KAAK,IAAI,IAAI,IAAI0D,UAAU,CAAC1D,KAAK,KAAK,MAAM,EAAE;MACzD,IAAIwF,YAAY,GAAG/H,YAAY,CAACiG,UAAU,CAAC1D,KAAK,EAAEuC,QAAQ,CAACvC,KAAK,CAAC,GAAGuF,aAAa;MACjF,IAAIrE,KAAK,CAACvC,MAAM,GAAG,CAAC,EAAE;QAClB,IAAI6G,YAAY,GAAGjD,QAAQ,CAACC,UAAU,GAAGD,QAAQ,CAACvC,KAAK,EAAE;UACrDoF,QAAQ,GAAGH,GAAG,CAAC3G,KAAK,CAAC,IAAI,CAAC;UAC1B6G,OAAO,GAAG,IAAI;QAClB;MACJ;MACA5C,QAAQ,CAACC,UAAU,GAAGgD,YAAY;IACtC,CAAC,MACI;MACD,IAAIC,GAAG,GAAGtE,QAAQ,CAAC8D,GAAG,EAAElH,IAAI,EAAEwE,QAAQ,CAACvC,KAAK,EAAEuC,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACC,UAAU,CAAC;MACrFD,QAAQ,CAACC,UAAU,GAAGiD,GAAG,CAACjD,UAAU,GAAG+C,aAAa;MACpDF,WAAW,GAAGI,GAAG,CAACJ,WAAW;MAC7BD,QAAQ,GAAGK,GAAG,CAACvE,KAAK;IACxB;EACJ;EACA,IAAI,CAACkE,QAAQ,EAAE;IACXA,QAAQ,GAAGH,GAAG,CAAC3G,KAAK,CAAC,IAAI,CAAC;EAC9B;EACA,IAAIY,eAAe,GAAG7B,qBAAqB,CAACU,IAAI,CAAC;EACjD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2G,QAAQ,CAACzG,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtC,IAAIZ,IAAI,GAAGuH,QAAQ,CAAC3G,CAAC,CAAC;IACtB,IAAIgF,KAAK,GAAG,IAAI9B,aAAa,CAAC,CAAC;IAC/B8B,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAAC5F,IAAI,GAAGA,IAAI;IACjB4F,KAAK,CAACiC,YAAY,GAAG,CAAC7H,IAAI,IAAI,CAACqH,UAAU;IACzC,IAAI,OAAOxB,UAAU,CAAC1D,KAAK,KAAK,QAAQ,EAAE;MACtCyD,KAAK,CAACzD,KAAK,GAAG0D,UAAU,CAAC1D,KAAK;IAClC,CAAC,MACI;MACDyD,KAAK,CAACzD,KAAK,GAAGqF,WAAW,GACnBA,WAAW,CAAC5G,CAAC,CAAC,GACdjB,YAAY,CAAC0B,eAAe,EAAErB,IAAI,CAAC;IAC7C;IACA,IAAI,CAACY,CAAC,IAAI,CAAC0G,OAAO,EAAE;MAChB,IAAItD,MAAM,GAAG,CAACX,KAAK,CAACA,KAAK,CAACvC,MAAM,GAAG,CAAC,CAAC,KAAKuC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAIU,YAAY,CAAC,CAAC,CAAC,EAAEC,MAAM;MAChF,IAAI8D,SAAS,GAAG9D,MAAM,CAAClD,MAAM;MAC5BgH,SAAS,KAAK,CAAC,IAAI9D,MAAM,CAAC,CAAC,CAAC,CAAC6D,YAAY,GACnC7D,MAAM,CAAC,CAAC,CAAC,GAAG4B,KAAK,GACjB,CAAC5F,IAAI,IAAI,CAAC8H,SAAS,IAAIT,UAAU,KAAKrD,MAAM,CAAC0C,IAAI,CAACd,KAAK,CAAE;IACpE,CAAC,MACI;MACDvC,KAAK,CAACqD,IAAI,CAAC,IAAI3C,YAAY,CAAC,CAAC6B,KAAK,CAAC,CAAC,CAAC;IACzC;EACJ;AACJ;AACA,SAASmC,kBAAkBA,CAACC,EAAE,EAAE;EAC5B,IAAIC,IAAI,GAAGD,EAAE,CAAC5F,UAAU,CAAC,CAAC,CAAC;EAC3B,OAAO6F,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,KAAK,IAC7BA,IAAI,IAAI,KAAK,IAAIA,IAAI,IAAI,MAAM,IAC/BA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAChCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AAC3C;AACA,IAAIC,YAAY,GAAG7I,MAAM,CAAC,SAAS,CAACoB,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU0H,GAAG,EAAEH,EAAE,EAAE;EAC9DG,GAAG,CAACH,EAAE,CAAC,GAAG,IAAI;EACd,OAAOG,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,SAASC,eAAeA,CAACJ,EAAE,EAAE;EACzB,IAAID,kBAAkB,CAACC,EAAE,CAAC,EAAE;IACxB,IAAIE,YAAY,CAACF,EAAE,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,SAAS1E,QAAQA,CAACtD,IAAI,EAAEE,IAAI,EAAE2B,SAAS,EAAEwG,UAAU,EAAEC,cAAc,EAAE;EACjE,IAAIjF,KAAK,GAAG,EAAE;EACd,IAAImE,WAAW,GAAG,EAAE;EACpB,IAAI9B,IAAI,GAAG,EAAE;EACb,IAAI6C,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAI7D,UAAU,GAAG,CAAC;EAClB,IAAItD,eAAe,GAAG7B,qBAAqB,CAACU,IAAI,CAAC;EACjD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACc,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClC,IAAIoH,EAAE,GAAGhI,IAAI,CAACwG,MAAM,CAAC5F,CAAC,CAAC;IACvB,IAAIoH,EAAE,KAAK,IAAI,EAAE;MACb,IAAIO,WAAW,EAAE;QACb7C,IAAI,IAAI6C,WAAW;QACnB5D,UAAU,IAAI6D,gBAAgB;MAClC;MACAnF,KAAK,CAACqD,IAAI,CAAChB,IAAI,CAAC;MAChB8B,WAAW,CAACd,IAAI,CAAC/B,UAAU,CAAC;MAC5Be,IAAI,GAAG,EAAE;MACT6C,WAAW,GAAG,EAAE;MAChBC,gBAAgB,GAAG,CAAC;MACpB7D,UAAU,GAAG,CAAC;MACd;IACJ;IACA,IAAI8D,OAAO,GAAG/I,gBAAgB,CAAC2B,eAAe,EAAE2G,EAAE,CAAC5F,UAAU,CAAC,CAAC,CAAC,CAAC;IACjE,IAAIsG,MAAM,GAAGL,UAAU,GAAG,KAAK,GAAG,CAACD,eAAe,CAACJ,EAAE,CAAC;IACtD,IAAI,CAAC3E,KAAK,CAACvC,MAAM,GACXwH,cAAc,GAAG3D,UAAU,GAAG8D,OAAO,GAAG5G,SAAS,GACjD8C,UAAU,GAAG8D,OAAO,GAAG5G,SAAS,EAAE;MACpC,IAAI,CAAC8C,UAAU,EAAE;QACb,IAAI+D,MAAM,EAAE;UACRrF,KAAK,CAACqD,IAAI,CAAC6B,WAAW,CAAC;UACvBf,WAAW,CAACd,IAAI,CAAC8B,gBAAgB,CAAC;UAClCD,WAAW,GAAGP,EAAE;UAChBQ,gBAAgB,GAAGC,OAAO;QAC9B,CAAC,MACI;UACDpF,KAAK,CAACqD,IAAI,CAACsB,EAAE,CAAC;UACdR,WAAW,CAACd,IAAI,CAAC+B,OAAO,CAAC;QAC7B;MACJ,CAAC,MACI,IAAI/C,IAAI,IAAI6C,WAAW,EAAE;QAC1B,IAAIG,MAAM,EAAE;UACR,IAAI,CAAChD,IAAI,EAAE;YACPA,IAAI,GAAG6C,WAAW;YAClBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;YACpB7D,UAAU,GAAG6D,gBAAgB;UACjC;UACAnF,KAAK,CAACqD,IAAI,CAAChB,IAAI,CAAC;UAChB8B,WAAW,CAACd,IAAI,CAAC/B,UAAU,GAAG6D,gBAAgB,CAAC;UAC/CD,WAAW,IAAIP,EAAE;UACjBQ,gBAAgB,IAAIC,OAAO;UAC3B/C,IAAI,GAAG,EAAE;UACTf,UAAU,GAAG6D,gBAAgB;QACjC,CAAC,MACI;UACD,IAAID,WAAW,EAAE;YACb7C,IAAI,IAAI6C,WAAW;YACnBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;UACxB;UACAnF,KAAK,CAACqD,IAAI,CAAChB,IAAI,CAAC;UAChB8B,WAAW,CAACd,IAAI,CAAC/B,UAAU,CAAC;UAC5Be,IAAI,GAAGsC,EAAE;UACTrD,UAAU,GAAG8D,OAAO;QACxB;MACJ;MACA;IACJ;IACA9D,UAAU,IAAI8D,OAAO;IACrB,IAAIC,MAAM,EAAE;MACRH,WAAW,IAAIP,EAAE;MACjBQ,gBAAgB,IAAIC,OAAO;IAC/B,CAAC,MACI;MACD,IAAIF,WAAW,EAAE;QACb7C,IAAI,IAAI6C,WAAW;QACnBA,WAAW,GAAG,EAAE;QAChBC,gBAAgB,GAAG,CAAC;MACxB;MACA9C,IAAI,IAAIsC,EAAE;IACd;EACJ;EACA,IAAIO,WAAW,EAAE;IACb7C,IAAI,IAAI6C,WAAW;EACvB;EACA,IAAI7C,IAAI,EAAE;IACNrC,KAAK,CAACqD,IAAI,CAAChB,IAAI,CAAC;IAChB8B,WAAW,CAACd,IAAI,CAAC/B,UAAU,CAAC;EAChC;EACA,IAAItB,KAAK,CAACvC,MAAM,KAAK,CAAC,EAAE;IACpB6D,UAAU,IAAI2D,cAAc;EAChC;EACA,OAAO;IACH3D,UAAU,EAAEA,UAAU;IACtBtB,KAAK,EAAEA,KAAK;IACZmE,WAAW,EAAEA;EACjB,CAAC;AACL;AACA,OAAO,SAASmB,yBAAyBA,CAACtI,GAAG,EAAEuI,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,iBAAiB,EAAE;EACrG3I,GAAG,CAACwI,KAAK,GAAGA,KAAK;EACjBxI,GAAG,CAACyI,KAAK,GAAGA,KAAK;EACjBzI,GAAG,CAACwD,UAAU,GAAGxD,GAAG,CAACuD,WAAW,GAAG,IAAI;EACvC,IAAI,CAACgF,YAAY,EAAE;IACf;EACJ;EACA,IAAIK,SAAS,GAAGL,YAAY,CAACzG,KAAK,GAAG,CAAC;EACtC,IAAI+G,UAAU,GAAGN,YAAY,CAACxF,MAAM,GAAG,CAAC;EACxCvD,YAAY,CAACsJ,GAAG,CAACC,eAAe,EAAE9J,WAAW,CAACuJ,KAAK,EAAEI,SAAS,EAAEF,SAAS,CAAC,EAAExJ,WAAW,CAACuJ,KAAK,EAAEI,UAAU,EAAEF,iBAAiB,CAAC,EAAEC,SAAS,EAAEC,UAAU,CAAC;EACrJrJ,YAAY,CAACwJ,SAAS,CAACT,YAAY,EAAEQ,eAAe,EAAE,IAAI,EAAEE,uBAAuB,CAAC;EACpF,IAAIC,gBAAgB,GAAGD,uBAAuB,CAACC,gBAAgB;EAC/DlJ,GAAG,CAACwD,UAAU,GAAG0F,gBAAgB,CAACpH,KAAK;EACvC9B,GAAG,CAACuD,WAAW,GAAG2F,gBAAgB,CAACnG,MAAM;EACzC/C,GAAG,CAACwI,KAAK,GAAGvJ,WAAW,CAACiK,gBAAgB,CAACC,CAAC,EAAED,gBAAgB,CAACpH,KAAK,EAAE4G,SAAS,EAAE,IAAI,CAAC;EACpF1I,GAAG,CAACyI,KAAK,GAAGvJ,WAAW,CAACgK,gBAAgB,CAACE,CAAC,EAAEF,gBAAgB,CAACnG,MAAM,EAAE4F,iBAAiB,EAAE,IAAI,CAAC;AACjG;AACA,IAAII,eAAe,GAAG,IAAIvJ,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD,IAAIyJ,uBAAuB,GAAG;EAAEC,gBAAgB,EAAE,CAAC,CAAC;EAAEG,KAAK,EAAE;AAAK,CAAC;AACnE,SAAShH,UAAUA,CAAC1C,IAAI,EAAE;EACtB,OAAOA,IAAI,IAAI,IAAI,GAAIA,IAAI,IAAI,EAAE,GAAKA,IAAI,GAAG,EAAG;AACpD;AACA,OAAO,SAAS2J,uBAAuBA,CAACpH,KAAK,EAAE;EAC3C,IAAIvC,IAAI,GAAG0C,UAAU,CAACH,KAAK,CAACvC,IAAI,CAAC;EACjC,IAAIE,IAAI,GAAGqC,KAAK,CAACrC,IAAI;EACrB,IAAIuB,YAAY,GAAG9B,YAAY,CAACH,qBAAqB,CAACU,IAAI,CAAC,EAAEF,IAAI,CAAC;EAClE,IAAIuD,aAAa,GAAG9D,aAAa,CAACS,IAAI,CAAC;EACvC,OAAO0J,wBAAwB,CAACrH,KAAK,EAAEd,YAAY,EAAE8B,aAAa,EAAE,IAAI,CAAC;AAC7E;AACA,OAAO,SAASqG,wBAAwBA,CAACrH,KAAK,EAAEd,YAAY,EAAE8B,aAAa,EAAEsG,cAAc,EAAE;EACzF,IAAIC,IAAI,GAAG,IAAIjK,YAAY,CAACP,WAAW,CAACiD,KAAK,CAACiH,CAAC,IAAI,CAAC,EAAE/H,YAAY,EAAEc,KAAK,CAACwG,SAAS,CAAC,EAAExJ,WAAW,CAACgD,KAAK,CAACkH,CAAC,IAAI,CAAC,EAAElG,aAAa,EAAEhB,KAAK,CAACwH,YAAY,CAAC,EAAEtI,YAAY,EAAE8B,aAAa,CAAC;EAChL,IAAI1B,SAAS,GAAGgI,cAAc,IAAI,IAAI,GAChCA,cAAc,GACbG,cAAc,CAACzH,KAAK,CAAC,GAAGA,KAAK,CAACV,SAAS,GAAG,CAAE;EACnD,IAAIA,SAAS,GAAG,CAAC,EAAE;IACfiI,IAAI,CAACN,CAAC,IAAI3H,SAAS,GAAG,CAAC;IACvBiI,IAAI,CAACL,CAAC,IAAI5H,SAAS,GAAG,CAAC;IACvBiI,IAAI,CAAC3H,KAAK,IAAIN,SAAS;IACvBiI,IAAI,CAAC1G,MAAM,IAAIvB,SAAS;EAC5B;EACA,OAAOiI,IAAI;AACf;AACA,OAAO,SAASE,cAAcA,CAACzH,KAAK,EAAE;EAClC,IAAI0H,MAAM,GAAG1H,KAAK,CAAC0H,MAAM;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,MAAM,IAAI1H,KAAK,CAACV,SAAS,GAAG,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}