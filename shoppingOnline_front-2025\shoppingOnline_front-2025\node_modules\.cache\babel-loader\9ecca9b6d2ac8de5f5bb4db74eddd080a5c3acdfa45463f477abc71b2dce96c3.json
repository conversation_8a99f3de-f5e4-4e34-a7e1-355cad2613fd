{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, each, extend, isArray, isObject } from 'zrender/lib/core/util.js';\nimport { error } from '../../util/log.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport { resolveXYLocatorRangeByCellMerge, MatrixClampOption, parseCoordRangeOption, fillIdSpanFromLocatorRange, createNaNRectLike, isXYLocatorRangeInvalidOnDim, resetXYLocatorRange, cloneXYLocatorRange } from './matrixCoordHelper.js';\n/**\n * Lifetime: the same with `MatrixModel`, but different from `coord/Matrix`.\n */\nvar MatrixBodyCorner = /** @class */function () {\n  function MatrixBodyCorner(kind, bodyOrCornerModel, dims) {\n    this._model = bodyOrCornerModel;\n    this._dims = dims;\n    this._kind = kind;\n    this._cellMergeOwnerList = [];\n  }\n  /**\n   * Can not be called before series models initialization finished, since the ordinalMeta may\n   * use collect the values from `series.data` in series initialization.\n   */\n  MatrixBodyCorner.prototype._ensureCellMap = function () {\n    var self = this;\n    var _cellMap = self._cellMap;\n    if (!_cellMap) {\n      _cellMap = self._cellMap = createHashMap();\n      fillCellMap();\n    }\n    return _cellMap;\n    function fillCellMap() {\n      var parsedList = [];\n      var cellOptionList = self._model.getShallow('data');\n      if (cellOptionList && !isArray(cellOptionList)) {\n        if (process.env.NODE_ENV !== 'production') {\n          error(\"matrix.\" + cellOptionList + \".data must be an array if specified.\");\n        }\n        cellOptionList = null;\n      }\n      each(cellOptionList, function (option, idx) {\n        if (!isObject(option) || !isArray(option.coord)) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Illegal matrix.\" + self._kind + \".data[\" + idx + \"], must be a {coord: [...], ...}\");\n          }\n          return;\n        }\n        var locatorRange = resetXYLocatorRange([]);\n        var reasonArr = null;\n        if (process.env.NODE_ENV !== 'production') {\n          reasonArr = [];\n        }\n        parseCoordRangeOption(locatorRange, reasonArr, option.coord, self._dims, option.coordClamp ? MatrixClampOption[self._kind] : MatrixClampOption.none);\n        if (isXYLocatorRangeInvalidOnDim(locatorRange, 0) || isXYLocatorRangeInvalidOnDim(locatorRange, 1)) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Can not determine cells by option matrix.\" + self._kind + \".data[\" + idx + \"]: \" + (\"\" + reasonArr.join(' ')));\n          }\n          return;\n        }\n        var cellMergeOwner = option && option.mergeCells;\n        var parsed = {\n          id: new Point(),\n          span: new Point(),\n          locatorRange: locatorRange,\n          option: option,\n          cellMergeOwner: cellMergeOwner\n        };\n        fillIdSpanFromLocatorRange(parsed, locatorRange);\n        // The order of the `parsedList` determines the precedence of the styles, if there\n        // are overlaps between ranges specified in different items. Preserve the original\n        // order of `matrix.body/corner/data` to make it predictable for users.\n        parsedList.push(parsed);\n      });\n      // Resolve cell merging intersection - union to a larger rect.\n      var mergedMarkList = [];\n      for (var parsedIdx = 0; parsedIdx < parsedList.length; parsedIdx++) {\n        var parsed = parsedList[parsedIdx];\n        if (!parsed.cellMergeOwner) {\n          continue;\n        }\n        var locatorRange = parsed.locatorRange;\n        resolveXYLocatorRangeByCellMerge(locatorRange, mergedMarkList, parsedList, parsedIdx);\n        for (var idx = 0; idx < parsedIdx; idx++) {\n          if (mergedMarkList[idx]) {\n            parsedList[idx].cellMergeOwner = false;\n          }\n        }\n        if (locatorRange[0][0] !== parsed.id.x || locatorRange[1][0] !== parsed.id.y) {\n          // The top-left cell of the unioned locatorRange is not this cell any more.\n          parsed.cellMergeOwner = false;\n          // Reconcile: simply use the last style and value option if multiple styles involved\n          // in a merged area, since there might be no commonly used merge strategy.\n          var newOption = extend({}, parsed.option);\n          newOption.coord = null;\n          var newParsed = {\n            id: new Point(),\n            span: new Point(),\n            locatorRange: locatorRange,\n            option: newOption,\n            cellMergeOwner: true\n          };\n          fillIdSpanFromLocatorRange(newParsed, locatorRange);\n          parsedList.push(newParsed);\n        }\n      }\n      // Assign options to cells.\n      each(parsedList, function (parsed) {\n        var topLeftCell = ensureBodyOrCornerCell(parsed.id.x, parsed.id.y);\n        if (parsed.cellMergeOwner) {\n          topLeftCell.cellMergeOwner = true;\n          topLeftCell.span = parsed.span;\n          topLeftCell.locatorRange = parsed.locatorRange;\n          topLeftCell.spanRect = createNaNRectLike();\n          self._cellMergeOwnerList.push(topLeftCell);\n        }\n        if (!parsed.cellMergeOwner && !parsed.option) {\n          return;\n        }\n        for (var yidx = 0; yidx < parsed.span.y; yidx++) {\n          for (var xidx = 0; xidx < parsed.span.x; xidx++) {\n            var cell = ensureBodyOrCornerCell(parsed.id.x + xidx, parsed.id.y + yidx);\n            // If multiple style options are defined on a cell, the later ones takes precedence.\n            cell.option = parsed.option;\n            if (parsed.cellMergeOwner) {\n              cell.inSpanOf = topLeftCell;\n            }\n          }\n        }\n      });\n    } // End of fillCellMap\n    function ensureBodyOrCornerCell(x, y) {\n      var key = makeCellMapKey(x, y);\n      var cell = _cellMap.get(key);\n      if (!cell) {\n        cell = _cellMap.set(key, {\n          id: new Point(x, y),\n          option: null,\n          inSpanOf: null,\n          span: null,\n          spanRect: null,\n          locatorRange: null,\n          cellMergeOwner: false\n        });\n      }\n      return cell;\n    }\n  };\n  /**\n   * Body cells or corner cell are not commonly defined specifically, especially in a large\n   * table, thus his is a sparse data structure - bodys or corner cells exist only if there\n   * are options specified to it (in `matrix.body.data` or `matrix.corner.data`);\n   * otherwise, return `NullUndefined`.\n   */\n  MatrixBodyCorner.prototype.getCell = function (xy) {\n    // Assert xy do not contain NaN\n    return this._ensureCellMap().get(makeCellMapKey(xy[0], xy[1]));\n  };\n  /**\n   * Only cell existing (has specific definition or props) will be travelled.\n   */\n  MatrixBodyCorner.prototype.travelExistingCells = function (cb) {\n    this._ensureCellMap().each(cb);\n  };\n  /**\n   * @param locatorRange Must be the return of `parseCoordRangeOption`.\n   */\n  MatrixBodyCorner.prototype.expandRangeByCellMerge = function (locatorRange) {\n    if (!isXYLocatorRangeInvalidOnDim(locatorRange, 0) && !isXYLocatorRangeInvalidOnDim(locatorRange, 1) && locatorRange[0][0] === locatorRange[0][1] && locatorRange[1][0] === locatorRange[1][1]) {\n      // If it locates to a single cell, use this quick path to avoid travelling.\n      // It is based on the fact that any cell is not contained by more than one cell merging rect.\n      _tmpERBCMLocator[0] = locatorRange[0][0];\n      _tmpERBCMLocator[1] = locatorRange[1][0];\n      var cell = this.getCell(_tmpERBCMLocator);\n      var inSpanOf = cell && cell.inSpanOf;\n      if (inSpanOf) {\n        cloneXYLocatorRange(locatorRange, inSpanOf.locatorRange);\n        return;\n      }\n    }\n    var list = this._cellMergeOwnerList;\n    resolveXYLocatorRangeByCellMerge(locatorRange, null, list, list.length);\n  };\n  return MatrixBodyCorner;\n}();\nexport { MatrixBodyCorner };\nvar _tmpERBCMLocator = [];\nfunction makeCellMapKey(x, y) {\n  return x + \"|\" + y;\n}", "map": {"version": 3, "names": ["createHashMap", "each", "extend", "isArray", "isObject", "error", "Point", "resolveXYLocatorRangeByCellMerge", "MatrixClampOption", "parseCoordRangeOption", "fillIdSpanFromLocatorRange", "createNaNRectLike", "isXYLocatorRangeInvalidOnDim", "resetXYLocatorRange", "cloneXYLocatorRange", "MatrixBodyCorner", "kind", "bodyOrCornerModel", "dims", "_model", "_dims", "_kind", "_cellMergeOwnerList", "prototype", "_ensureCellMap", "self", "_cellMap", "fillCellMap", "parsedList", "cellOptionList", "getShallow", "process", "env", "NODE_ENV", "option", "idx", "coord", "locatorRange", "reasonArr", "coordClamp", "none", "join", "cellMergeOwner", "mergeCells", "parsed", "id", "span", "push", "mergedMarkList", "parsedIdx", "length", "x", "y", "newOption", "newParsed", "topLeftCell", "ensureBodyOrCornerCell", "spanRect", "yidx", "xidx", "cell", "inSpanOf", "key", "makeCellMapKey", "get", "set", "getCell", "xy", "travelExistingCells", "cb", "expandRangeByCellMerge", "_tmpERBCMLocator", "list"], "sources": ["E:/shixi 8.25/work1/shopping-front/shoppingOnline_front-2025/shoppingOnline_front-2025/shoppingOnline_front-2025/node_modules/echarts/lib/coord/matrix/MatrixBodyCorner.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { createHashMap, each, extend, isArray, isObject } from 'zrender/lib/core/util.js';\nimport { error } from '../../util/log.js';\nimport Point from 'zrender/lib/core/Point.js';\nimport { resolveXYLocatorRangeByCellMerge, MatrixClampOption, parseCoordRangeOption, fillIdSpanFromLocatorRange, createNaNRectLike, isXYLocatorRangeInvalidOnDim, resetXYLocatorRange, cloneXYLocatorRange } from './matrixCoordHelper.js';\n/**\n * Lifetime: the same with `MatrixModel`, but different from `coord/Matrix`.\n */\nvar MatrixBodyCorner = /** @class */function () {\n  function MatrixBodyCorner(kind, bodyOrCornerModel, dims) {\n    this._model = bodyOrCornerModel;\n    this._dims = dims;\n    this._kind = kind;\n    this._cellMergeOwnerList = [];\n  }\n  /**\n   * Can not be called before series models initialization finished, since the ordinalMeta may\n   * use collect the values from `series.data` in series initialization.\n   */\n  MatrixBodyCorner.prototype._ensureCellMap = function () {\n    var self = this;\n    var _cellMap = self._cellMap;\n    if (!_cellMap) {\n      _cellMap = self._cellMap = createHashMap();\n      fillCellMap();\n    }\n    return _cellMap;\n    function fillCellMap() {\n      var parsedList = [];\n      var cellOptionList = self._model.getShallow('data');\n      if (cellOptionList && !isArray(cellOptionList)) {\n        if (process.env.NODE_ENV !== 'production') {\n          error(\"matrix.\" + cellOptionList + \".data must be an array if specified.\");\n        }\n        cellOptionList = null;\n      }\n      each(cellOptionList, function (option, idx) {\n        if (!isObject(option) || !isArray(option.coord)) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Illegal matrix.\" + self._kind + \".data[\" + idx + \"], must be a {coord: [...], ...}\");\n          }\n          return;\n        }\n        var locatorRange = resetXYLocatorRange([]);\n        var reasonArr = null;\n        if (process.env.NODE_ENV !== 'production') {\n          reasonArr = [];\n        }\n        parseCoordRangeOption(locatorRange, reasonArr, option.coord, self._dims, option.coordClamp ? MatrixClampOption[self._kind] : MatrixClampOption.none);\n        if (isXYLocatorRangeInvalidOnDim(locatorRange, 0) || isXYLocatorRangeInvalidOnDim(locatorRange, 1)) {\n          if (process.env.NODE_ENV !== 'production') {\n            error(\"Can not determine cells by option matrix.\" + self._kind + \".data[\" + idx + \"]: \" + (\"\" + reasonArr.join(' ')));\n          }\n          return;\n        }\n        var cellMergeOwner = option && option.mergeCells;\n        var parsed = {\n          id: new Point(),\n          span: new Point(),\n          locatorRange: locatorRange,\n          option: option,\n          cellMergeOwner: cellMergeOwner\n        };\n        fillIdSpanFromLocatorRange(parsed, locatorRange);\n        // The order of the `parsedList` determines the precedence of the styles, if there\n        // are overlaps between ranges specified in different items. Preserve the original\n        // order of `matrix.body/corner/data` to make it predictable for users.\n        parsedList.push(parsed);\n      });\n      // Resolve cell merging intersection - union to a larger rect.\n      var mergedMarkList = [];\n      for (var parsedIdx = 0; parsedIdx < parsedList.length; parsedIdx++) {\n        var parsed = parsedList[parsedIdx];\n        if (!parsed.cellMergeOwner) {\n          continue;\n        }\n        var locatorRange = parsed.locatorRange;\n        resolveXYLocatorRangeByCellMerge(locatorRange, mergedMarkList, parsedList, parsedIdx);\n        for (var idx = 0; idx < parsedIdx; idx++) {\n          if (mergedMarkList[idx]) {\n            parsedList[idx].cellMergeOwner = false;\n          }\n        }\n        if (locatorRange[0][0] !== parsed.id.x || locatorRange[1][0] !== parsed.id.y) {\n          // The top-left cell of the unioned locatorRange is not this cell any more.\n          parsed.cellMergeOwner = false;\n          // Reconcile: simply use the last style and value option if multiple styles involved\n          // in a merged area, since there might be no commonly used merge strategy.\n          var newOption = extend({}, parsed.option);\n          newOption.coord = null;\n          var newParsed = {\n            id: new Point(),\n            span: new Point(),\n            locatorRange: locatorRange,\n            option: newOption,\n            cellMergeOwner: true\n          };\n          fillIdSpanFromLocatorRange(newParsed, locatorRange);\n          parsedList.push(newParsed);\n        }\n      }\n      // Assign options to cells.\n      each(parsedList, function (parsed) {\n        var topLeftCell = ensureBodyOrCornerCell(parsed.id.x, parsed.id.y);\n        if (parsed.cellMergeOwner) {\n          topLeftCell.cellMergeOwner = true;\n          topLeftCell.span = parsed.span;\n          topLeftCell.locatorRange = parsed.locatorRange;\n          topLeftCell.spanRect = createNaNRectLike();\n          self._cellMergeOwnerList.push(topLeftCell);\n        }\n        if (!parsed.cellMergeOwner && !parsed.option) {\n          return;\n        }\n        for (var yidx = 0; yidx < parsed.span.y; yidx++) {\n          for (var xidx = 0; xidx < parsed.span.x; xidx++) {\n            var cell = ensureBodyOrCornerCell(parsed.id.x + xidx, parsed.id.y + yidx);\n            // If multiple style options are defined on a cell, the later ones takes precedence.\n            cell.option = parsed.option;\n            if (parsed.cellMergeOwner) {\n              cell.inSpanOf = topLeftCell;\n            }\n          }\n        }\n      });\n    } // End of fillCellMap\n    function ensureBodyOrCornerCell(x, y) {\n      var key = makeCellMapKey(x, y);\n      var cell = _cellMap.get(key);\n      if (!cell) {\n        cell = _cellMap.set(key, {\n          id: new Point(x, y),\n          option: null,\n          inSpanOf: null,\n          span: null,\n          spanRect: null,\n          locatorRange: null,\n          cellMergeOwner: false\n        });\n      }\n      return cell;\n    }\n  };\n  /**\n   * Body cells or corner cell are not commonly defined specifically, especially in a large\n   * table, thus his is a sparse data structure - bodys or corner cells exist only if there\n   * are options specified to it (in `matrix.body.data` or `matrix.corner.data`);\n   * otherwise, return `NullUndefined`.\n   */\n  MatrixBodyCorner.prototype.getCell = function (xy) {\n    // Assert xy do not contain NaN\n    return this._ensureCellMap().get(makeCellMapKey(xy[0], xy[1]));\n  };\n  /**\n   * Only cell existing (has specific definition or props) will be travelled.\n   */\n  MatrixBodyCorner.prototype.travelExistingCells = function (cb) {\n    this._ensureCellMap().each(cb);\n  };\n  /**\n   * @param locatorRange Must be the return of `parseCoordRangeOption`.\n   */\n  MatrixBodyCorner.prototype.expandRangeByCellMerge = function (locatorRange) {\n    if (!isXYLocatorRangeInvalidOnDim(locatorRange, 0) && !isXYLocatorRangeInvalidOnDim(locatorRange, 1) && locatorRange[0][0] === locatorRange[0][1] && locatorRange[1][0] === locatorRange[1][1]) {\n      // If it locates to a single cell, use this quick path to avoid travelling.\n      // It is based on the fact that any cell is not contained by more than one cell merging rect.\n      _tmpERBCMLocator[0] = locatorRange[0][0];\n      _tmpERBCMLocator[1] = locatorRange[1][0];\n      var cell = this.getCell(_tmpERBCMLocator);\n      var inSpanOf = cell && cell.inSpanOf;\n      if (inSpanOf) {\n        cloneXYLocatorRange(locatorRange, inSpanOf.locatorRange);\n        return;\n      }\n    }\n    var list = this._cellMergeOwnerList;\n    resolveXYLocatorRangeByCellMerge(locatorRange, null, list, list.length);\n  };\n  return MatrixBodyCorner;\n}();\nexport { MatrixBodyCorner };\nvar _tmpERBCMLocator = [];\nfunction makeCellMapKey(x, y) {\n  return x + \"|\" + y;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,0BAA0B;AACzF,SAASC,KAAK,QAAQ,mBAAmB;AACzC,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,SAASC,gCAAgC,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,4BAA4B,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC1O;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EAC9C,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,iBAAiB,EAAEC,IAAI,EAAE;IACvD,IAAI,CAACC,MAAM,GAAGF,iBAAiB;IAC/B,IAAI,CAACG,KAAK,GAAGF,IAAI;IACjB,IAAI,CAACG,KAAK,GAAGL,IAAI;IACjB,IAAI,CAACM,mBAAmB,GAAG,EAAE;EAC/B;EACA;AACF;AACA;AACA;EACEP,gBAAgB,CAACQ,SAAS,CAACC,cAAc,GAAG,YAAY;IACtD,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC5B,IAAI,CAACA,QAAQ,EAAE;MACbA,QAAQ,GAAGD,IAAI,CAACC,QAAQ,GAAG1B,aAAa,CAAC,CAAC;MAC1C2B,WAAW,CAAC,CAAC;IACf;IACA,OAAOD,QAAQ;IACf,SAASC,WAAWA,CAAA,EAAG;MACrB,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAIC,cAAc,GAAGJ,IAAI,CAACN,MAAM,CAACW,UAAU,CAAC,MAAM,CAAC;MACnD,IAAID,cAAc,IAAI,CAAC1B,OAAO,CAAC0B,cAAc,CAAC,EAAE;QAC9C,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC5B,KAAK,CAAC,SAAS,GAAGwB,cAAc,GAAG,sCAAsC,CAAC;QAC5E;QACAA,cAAc,GAAG,IAAI;MACvB;MACA5B,IAAI,CAAC4B,cAAc,EAAE,UAAUK,MAAM,EAAEC,GAAG,EAAE;QAC1C,IAAI,CAAC/B,QAAQ,CAAC8B,MAAM,CAAC,IAAI,CAAC/B,OAAO,CAAC+B,MAAM,CAACE,KAAK,CAAC,EAAE;UAC/C,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC5B,KAAK,CAAC,iBAAiB,GAAGoB,IAAI,CAACJ,KAAK,GAAG,QAAQ,GAAGc,GAAG,GAAG,kCAAkC,CAAC;UAC7F;UACA;QACF;QACA,IAAIE,YAAY,GAAGxB,mBAAmB,CAAC,EAAE,CAAC;QAC1C,IAAIyB,SAAS,GAAG,IAAI;QACpB,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCK,SAAS,GAAG,EAAE;QAChB;QACA7B,qBAAqB,CAAC4B,YAAY,EAAEC,SAAS,EAAEJ,MAAM,CAACE,KAAK,EAAEX,IAAI,CAACL,KAAK,EAAEc,MAAM,CAACK,UAAU,GAAG/B,iBAAiB,CAACiB,IAAI,CAACJ,KAAK,CAAC,GAAGb,iBAAiB,CAACgC,IAAI,CAAC;QACpJ,IAAI5B,4BAA4B,CAACyB,YAAY,EAAE,CAAC,CAAC,IAAIzB,4BAA4B,CAACyB,YAAY,EAAE,CAAC,CAAC,EAAE;UAClG,IAAIN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC5B,KAAK,CAAC,2CAA2C,GAAGoB,IAAI,CAACJ,KAAK,GAAG,QAAQ,GAAGc,GAAG,GAAG,KAAK,IAAI,EAAE,GAAGG,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;UACvH;UACA;QACF;QACA,IAAIC,cAAc,GAAGR,MAAM,IAAIA,MAAM,CAACS,UAAU;QAChD,IAAIC,MAAM,GAAG;UACXC,EAAE,EAAE,IAAIvC,KAAK,CAAC,CAAC;UACfwC,IAAI,EAAE,IAAIxC,KAAK,CAAC,CAAC;UACjB+B,YAAY,EAAEA,YAAY;UAC1BH,MAAM,EAAEA,MAAM;UACdQ,cAAc,EAAEA;QAClB,CAAC;QACDhC,0BAA0B,CAACkC,MAAM,EAAEP,YAAY,CAAC;QAChD;QACA;QACA;QACAT,UAAU,CAACmB,IAAI,CAACH,MAAM,CAAC;MACzB,CAAC,CAAC;MACF;MACA,IAAII,cAAc,GAAG,EAAE;MACvB,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGrB,UAAU,CAACsB,MAAM,EAAED,SAAS,EAAE,EAAE;QAClE,IAAIL,MAAM,GAAGhB,UAAU,CAACqB,SAAS,CAAC;QAClC,IAAI,CAACL,MAAM,CAACF,cAAc,EAAE;UAC1B;QACF;QACA,IAAIL,YAAY,GAAGO,MAAM,CAACP,YAAY;QACtC9B,gCAAgC,CAAC8B,YAAY,EAAEW,cAAc,EAAEpB,UAAU,EAAEqB,SAAS,CAAC;QACrF,KAAK,IAAId,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGc,SAAS,EAAEd,GAAG,EAAE,EAAE;UACxC,IAAIa,cAAc,CAACb,GAAG,CAAC,EAAE;YACvBP,UAAU,CAACO,GAAG,CAAC,CAACO,cAAc,GAAG,KAAK;UACxC;QACF;QACA,IAAIL,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKO,MAAM,CAACC,EAAE,CAACM,CAAC,IAAId,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKO,MAAM,CAACC,EAAE,CAACO,CAAC,EAAE;UAC5E;UACAR,MAAM,CAACF,cAAc,GAAG,KAAK;UAC7B;UACA;UACA,IAAIW,SAAS,GAAGnD,MAAM,CAAC,CAAC,CAAC,EAAE0C,MAAM,CAACV,MAAM,CAAC;UACzCmB,SAAS,CAACjB,KAAK,GAAG,IAAI;UACtB,IAAIkB,SAAS,GAAG;YACdT,EAAE,EAAE,IAAIvC,KAAK,CAAC,CAAC;YACfwC,IAAI,EAAE,IAAIxC,KAAK,CAAC,CAAC;YACjB+B,YAAY,EAAEA,YAAY;YAC1BH,MAAM,EAAEmB,SAAS;YACjBX,cAAc,EAAE;UAClB,CAAC;UACDhC,0BAA0B,CAAC4C,SAAS,EAAEjB,YAAY,CAAC;UACnDT,UAAU,CAACmB,IAAI,CAACO,SAAS,CAAC;QAC5B;MACF;MACA;MACArD,IAAI,CAAC2B,UAAU,EAAE,UAAUgB,MAAM,EAAE;QACjC,IAAIW,WAAW,GAAGC,sBAAsB,CAACZ,MAAM,CAACC,EAAE,CAACM,CAAC,EAAEP,MAAM,CAACC,EAAE,CAACO,CAAC,CAAC;QAClE,IAAIR,MAAM,CAACF,cAAc,EAAE;UACzBa,WAAW,CAACb,cAAc,GAAG,IAAI;UACjCa,WAAW,CAACT,IAAI,GAAGF,MAAM,CAACE,IAAI;UAC9BS,WAAW,CAAClB,YAAY,GAAGO,MAAM,CAACP,YAAY;UAC9CkB,WAAW,CAACE,QAAQ,GAAG9C,iBAAiB,CAAC,CAAC;UAC1Cc,IAAI,CAACH,mBAAmB,CAACyB,IAAI,CAACQ,WAAW,CAAC;QAC5C;QACA,IAAI,CAACX,MAAM,CAACF,cAAc,IAAI,CAACE,MAAM,CAACV,MAAM,EAAE;UAC5C;QACF;QACA,KAAK,IAAIwB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGd,MAAM,CAACE,IAAI,CAACM,CAAC,EAAEM,IAAI,EAAE,EAAE;UAC/C,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGf,MAAM,CAACE,IAAI,CAACK,CAAC,EAAEQ,IAAI,EAAE,EAAE;YAC/C,IAAIC,IAAI,GAAGJ,sBAAsB,CAACZ,MAAM,CAACC,EAAE,CAACM,CAAC,GAAGQ,IAAI,EAAEf,MAAM,CAACC,EAAE,CAACO,CAAC,GAAGM,IAAI,CAAC;YACzE;YACAE,IAAI,CAAC1B,MAAM,GAAGU,MAAM,CAACV,MAAM;YAC3B,IAAIU,MAAM,CAACF,cAAc,EAAE;cACzBkB,IAAI,CAACC,QAAQ,GAAGN,WAAW;YAC7B;UACF;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,SAASC,sBAAsBA,CAACL,CAAC,EAAEC,CAAC,EAAE;MACpC,IAAIU,GAAG,GAAGC,cAAc,CAACZ,CAAC,EAAEC,CAAC,CAAC;MAC9B,IAAIQ,IAAI,GAAGlC,QAAQ,CAACsC,GAAG,CAACF,GAAG,CAAC;MAC5B,IAAI,CAACF,IAAI,EAAE;QACTA,IAAI,GAAGlC,QAAQ,CAACuC,GAAG,CAACH,GAAG,EAAE;UACvBjB,EAAE,EAAE,IAAIvC,KAAK,CAAC6C,CAAC,EAAEC,CAAC,CAAC;UACnBlB,MAAM,EAAE,IAAI;UACZ2B,QAAQ,EAAE,IAAI;UACdf,IAAI,EAAE,IAAI;UACVW,QAAQ,EAAE,IAAI;UACdpB,YAAY,EAAE,IAAI;UAClBK,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ;MACA,OAAOkB,IAAI;IACb;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE7C,gBAAgB,CAACQ,SAAS,CAAC2C,OAAO,GAAG,UAAUC,EAAE,EAAE;IACjD;IACA,OAAO,IAAI,CAAC3C,cAAc,CAAC,CAAC,CAACwC,GAAG,CAACD,cAAc,CAACI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC;EACD;AACF;AACA;EACEpD,gBAAgB,CAACQ,SAAS,CAAC6C,mBAAmB,GAAG,UAAUC,EAAE,EAAE;IAC7D,IAAI,CAAC7C,cAAc,CAAC,CAAC,CAACvB,IAAI,CAACoE,EAAE,CAAC;EAChC,CAAC;EACD;AACF;AACA;EACEtD,gBAAgB,CAACQ,SAAS,CAAC+C,sBAAsB,GAAG,UAAUjC,YAAY,EAAE;IAC1E,IAAI,CAACzB,4BAA4B,CAACyB,YAAY,EAAE,CAAC,CAAC,IAAI,CAACzB,4BAA4B,CAACyB,YAAY,EAAE,CAAC,CAAC,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9L;MACA;MACAkC,gBAAgB,CAAC,CAAC,CAAC,GAAGlC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxCkC,gBAAgB,CAAC,CAAC,CAAC,GAAGlC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,IAAIuB,IAAI,GAAG,IAAI,CAACM,OAAO,CAACK,gBAAgB,CAAC;MACzC,IAAIV,QAAQ,GAAGD,IAAI,IAAIA,IAAI,CAACC,QAAQ;MACpC,IAAIA,QAAQ,EAAE;QACZ/C,mBAAmB,CAACuB,YAAY,EAAEwB,QAAQ,CAACxB,YAAY,CAAC;QACxD;MACF;IACF;IACA,IAAImC,IAAI,GAAG,IAAI,CAAClD,mBAAmB;IACnCf,gCAAgC,CAAC8B,YAAY,EAAE,IAAI,EAAEmC,IAAI,EAAEA,IAAI,CAACtB,MAAM,CAAC;EACzE,CAAC;EACD,OAAOnC,gBAAgB;AACzB,CAAC,CAAC,CAAC;AACH,SAASA,gBAAgB;AACzB,IAAIwD,gBAAgB,GAAG,EAAE;AACzB,SAASR,cAAcA,CAACZ,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOD,CAAC,GAAG,GAAG,GAAGC,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}